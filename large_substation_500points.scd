<?xml version="1.0" ?>
<SCL xmlns="http://www.iec.ch/61850/2003/SCL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2007" revision="B" release="4">
  <Header id="TestSubstation_500Points_Large_SCD" version="2.0" revision="1" toolID="AutoPoint_Large_SCD_Generator" nameStructure="IEDName">
    <History>
      <Hitem version="2.0" revision="1" when="2025-07-03T15:55:09.387649" who="AutoPoint System" what="Large SCD creation with 500+ data points"/>
    </History>
  </Header>
  <Substation name="TestSubstation_500Points" desc="TestSubstation_500Points Large Scale Configuration">
    <VoltageLevel name="500kV" desc="500kV Voltage Level" nomFreq="50" numPhases="3">
      <Voltage unit="V" multiplier="k">500</Voltage>
      <Bay name="500kV_Bay1" desc="500kV Bay 1"/>
      <Bay name="500kV_Bay2" desc="500kV Bay 2"/>
      <Bay name="500kV_Bay3" desc="500kV Bay 3"/>
    </VoltageLevel>
    <VoltageLevel name="220kV" desc="220kV Voltage Level" nomFreq="50" numPhases="3">
      <Voltage unit="V" multiplier="k">220</Voltage>
      <Bay name="220kV_Bay1" desc="220kV Bay 1"/>
      <Bay name="220kV_Bay2" desc="220kV Bay 2"/>
      <Bay name="220kV_Bay3" desc="220kV Bay 3"/>
    </VoltageLevel>
    <VoltageLevel name="110kV" desc="110kV Voltage Level" nomFreq="50" numPhases="3">
      <Voltage unit="V" multiplier="k">110</Voltage>
      <Bay name="110kV_Bay1" desc="110kV Bay 1"/>
      <Bay name="110kV_Bay2" desc="110kV Bay 2"/>
      <Bay name="110kV_Bay3" desc="110kV Bay 3"/>
    </VoltageLevel>
    <VoltageLevel name="35kV" desc="35kV Voltage Level" nomFreq="50" numPhases="3">
      <Voltage unit="V" multiplier="k">35</Voltage>
      <Bay name="35kV_Bay1" desc="35kV Bay 1"/>
      <Bay name="35kV_Bay2" desc="35kV Bay 2"/>
      <Bay name="35kV_Bay3" desc="35kV Bay 3"/>
    </VoltageLevel>
    <VoltageLevel name="10kV" desc="10kV Voltage Level" nomFreq="50" numPhases="3">
      <Voltage unit="V" multiplier="k">10</Voltage>
      <Bay name="10kV_Bay1" desc="10kV Bay 1"/>
      <Bay name="10kV_Bay2" desc="10kV Bay 2"/>
      <Bay name="10kV_Bay3" desc="10kV Bay 3"/>
    </VoltageLevel>
  </Substation>
  <IED name="IED_001" type="MMXU" manufacturer="TestManufacturer" configVersion="2.0" originalSclVersion="2007" originalSclRevision="B">
    <Services>
      <DynAssociation/>
      <SettingGroups/>
      <GetDirectory/>
      <GetDataObjectDefinition/>
      <DataObjectDirectory/>
      <GetDataSetValue/>
      <ReadWrite/>
    </Services>
    <AccessPoint name="AP1">
      <Server>
        <Authentication/>
        <LDevice inst="CTRL">
          <LN lnClass="XCBR" inst="1" lnType="XCBR_TYPE"/>
          <LN lnClass="MMXU" inst="2" lnType="MMXU_TYPE"/>
          <LN lnClass="PTRC" inst="3" lnType="PTRC_TYPE"/>
          <LN lnClass="GGIO" inst="4" lnType="GGIO_TYPE"/>
          <LN lnClass="CSWI" inst="5" lnType="CSWI_TYPE"/>
          <LN lnClass="TCTR" inst="6" lnType="TCTR_TYPE"/>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  <IED name="IED_002" type="PTRC" manufacturer="TestManufacturer" configVersion="2.0" originalSclVersion="2007" originalSclRevision="B">
    <Services>
      <DynAssociation/>
      <SettingGroups/>
      <GetDirectory/>
      <GetDataObjectDefinition/>
      <DataObjectDirectory/>
      <GetDataSetValue/>
      <ReadWrite/>
    </Services>
    <AccessPoint name="AP1">
      <Server>
        <Authentication/>
        <LDevice inst="CTRL">
          <LN lnClass="XCBR" inst="1" lnType="XCBR_TYPE"/>
          <LN lnClass="MMXU" inst="2" lnType="MMXU_TYPE"/>
          <LN lnClass="PTRC" inst="3" lnType="PTRC_TYPE"/>
          <LN lnClass="GGIO" inst="4" lnType="GGIO_TYPE"/>
          <LN lnClass="CSWI" inst="5" lnType="CSWI_TYPE"/>
          <LN lnClass="TCTR" inst="6" lnType="TCTR_TYPE"/>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  <IED name="IED_003" type="GGIO" manufacturer="TestManufacturer" configVersion="2.0" originalSclVersion="2007" originalSclRevision="B">
    <Services>
      <DynAssociation/>
      <SettingGroups/>
      <GetDirectory/>
      <GetDataObjectDefinition/>
      <DataObjectDirectory/>
      <GetDataSetValue/>
      <ReadWrite/>
    </Services>
    <AccessPoint name="AP1">
      <Server>
        <Authentication/>
        <LDevice inst="CTRL">
          <LN lnClass="XCBR" inst="1" lnType="XCBR_TYPE"/>
          <LN lnClass="MMXU" inst="2" lnType="MMXU_TYPE"/>
          <LN lnClass="PTRC" inst="3" lnType="PTRC_TYPE"/>
          <LN lnClass="GGIO" inst="4" lnType="GGIO_TYPE"/>
          <LN lnClass="CSWI" inst="5" lnType="CSWI_TYPE"/>
          <LN lnClass="TCTR" inst="6" lnType="TCTR_TYPE"/>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  <IED name="IED_004" type="CSWI" manufacturer="TestManufacturer" configVersion="2.0" originalSclVersion="2007" originalSclRevision="B">
    <Services>
      <DynAssociation/>
      <SettingGroups/>
      <GetDirectory/>
      <GetDataObjectDefinition/>
      <DataObjectDirectory/>
      <GetDataSetValue/>
      <ReadWrite/>
    </Services>
    <AccessPoint name="AP1">
      <Server>
        <Authentication/>
        <LDevice inst="CTRL">
          <LN lnClass="XCBR" inst="1" lnType="XCBR_TYPE"/>
          <LN lnClass="MMXU" inst="2" lnType="MMXU_TYPE"/>
          <LN lnClass="PTRC" inst="3" lnType="PTRC_TYPE"/>
          <LN lnClass="GGIO" inst="4" lnType="GGIO_TYPE"/>
          <LN lnClass="CSWI" inst="5" lnType="CSWI_TYPE"/>
          <LN lnClass="TCTR" inst="6" lnType="TCTR_TYPE"/>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  <IED name="IED_005" type="TCTR" manufacturer="TestManufacturer" configVersion="2.0" originalSclVersion="2007" originalSclRevision="B">
    <Services>
      <DynAssociation/>
      <SettingGroups/>
      <GetDirectory/>
      <GetDataObjectDefinition/>
      <DataObjectDirectory/>
      <GetDataSetValue/>
      <ReadWrite/>
    </Services>
    <AccessPoint name="AP1">
      <Server>
        <Authentication/>
        <LDevice inst="CTRL">
          <LN lnClass="XCBR" inst="1" lnType="XCBR_TYPE"/>
          <LN lnClass="MMXU" inst="2" lnType="MMXU_TYPE"/>
          <LN lnClass="PTRC" inst="3" lnType="PTRC_TYPE"/>
          <LN lnClass="GGIO" inst="4" lnType="GGIO_TYPE"/>
          <LN lnClass="CSWI" inst="5" lnType="CSWI_TYPE"/>
          <LN lnClass="TCTR" inst="6" lnType="TCTR_TYPE"/>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  <IED name="IED_006" type="TVTR" manufacturer="TestManufacturer" configVersion="2.0" originalSclVersion="2007" originalSclRevision="B">
    <Services>
      <DynAssociation/>
      <SettingGroups/>
      <GetDirectory/>
      <GetDataObjectDefinition/>
      <DataObjectDirectory/>
      <GetDataSetValue/>
      <ReadWrite/>
    </Services>
    <AccessPoint name="AP1">
      <Server>
        <Authentication/>
        <LDevice inst="CTRL">
          <LN lnClass="XCBR" inst="1" lnType="XCBR_TYPE"/>
          <LN lnClass="MMXU" inst="2" lnType="MMXU_TYPE"/>
          <LN lnClass="PTRC" inst="3" lnType="PTRC_TYPE"/>
          <LN lnClass="GGIO" inst="4" lnType="GGIO_TYPE"/>
          <LN lnClass="CSWI" inst="5" lnType="CSWI_TYPE"/>
          <LN lnClass="TCTR" inst="6" lnType="TCTR_TYPE"/>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  <IED name="IED_007" type="XCBR" manufacturer="TestManufacturer" configVersion="2.0" originalSclVersion="2007" originalSclRevision="B">
    <Services>
      <DynAssociation/>
      <SettingGroups/>
      <GetDirectory/>
      <GetDataObjectDefinition/>
      <DataObjectDirectory/>
      <GetDataSetValue/>
      <ReadWrite/>
    </Services>
    <AccessPoint name="AP1">
      <Server>
        <Authentication/>
        <LDevice inst="CTRL">
          <LN lnClass="XCBR" inst="1" lnType="XCBR_TYPE"/>
          <LN lnClass="MMXU" inst="2" lnType="MMXU_TYPE"/>
          <LN lnClass="PTRC" inst="3" lnType="PTRC_TYPE"/>
          <LN lnClass="GGIO" inst="4" lnType="GGIO_TYPE"/>
          <LN lnClass="CSWI" inst="5" lnType="CSWI_TYPE"/>
          <LN lnClass="TCTR" inst="6" lnType="TCTR_TYPE"/>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  <IED name="IED_008" type="MMXU" manufacturer="TestManufacturer" configVersion="2.0" originalSclVersion="2007" originalSclRevision="B">
    <Services>
      <DynAssociation/>
      <SettingGroups/>
      <GetDirectory/>
      <GetDataObjectDefinition/>
      <DataObjectDirectory/>
      <GetDataSetValue/>
      <ReadWrite/>
    </Services>
    <AccessPoint name="AP1">
      <Server>
        <Authentication/>
        <LDevice inst="CTRL">
          <LN lnClass="XCBR" inst="1" lnType="XCBR_TYPE"/>
          <LN lnClass="MMXU" inst="2" lnType="MMXU_TYPE"/>
          <LN lnClass="PTRC" inst="3" lnType="PTRC_TYPE"/>
          <LN lnClass="GGIO" inst="4" lnType="GGIO_TYPE"/>
          <LN lnClass="CSWI" inst="5" lnType="CSWI_TYPE"/>
          <LN lnClass="TCTR" inst="6" lnType="TCTR_TYPE"/>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  <IED name="IED_009" type="PTRC" manufacturer="TestManufacturer" configVersion="2.0" originalSclVersion="2007" originalSclRevision="B">
    <Services>
      <DynAssociation/>
      <SettingGroups/>
      <GetDirectory/>
      <GetDataObjectDefinition/>
      <DataObjectDirectory/>
      <GetDataSetValue/>
      <ReadWrite/>
    </Services>
    <AccessPoint name="AP1">
      <Server>
        <Authentication/>
        <LDevice inst="CTRL">
          <LN lnClass="XCBR" inst="1" lnType="XCBR_TYPE"/>
          <LN lnClass="MMXU" inst="2" lnType="MMXU_TYPE"/>
          <LN lnClass="PTRC" inst="3" lnType="PTRC_TYPE"/>
          <LN lnClass="GGIO" inst="4" lnType="GGIO_TYPE"/>
          <LN lnClass="CSWI" inst="5" lnType="CSWI_TYPE"/>
          <LN lnClass="TCTR" inst="6" lnType="TCTR_TYPE"/>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  <IED name="IED_010" type="GGIO" manufacturer="TestManufacturer" configVersion="2.0" originalSclVersion="2007" originalSclRevision="B">
    <Services>
      <DynAssociation/>
      <SettingGroups/>
      <GetDirectory/>
      <GetDataObjectDefinition/>
      <DataObjectDirectory/>
      <GetDataSetValue/>
      <ReadWrite/>
    </Services>
    <AccessPoint name="AP1">
      <Server>
        <Authentication/>
        <LDevice inst="CTRL">
          <LN lnClass="XCBR" inst="1" lnType="XCBR_TYPE"/>
          <LN lnClass="MMXU" inst="2" lnType="MMXU_TYPE"/>
          <LN lnClass="PTRC" inst="3" lnType="PTRC_TYPE"/>
          <LN lnClass="GGIO" inst="4" lnType="GGIO_TYPE"/>
          <LN lnClass="CSWI" inst="5" lnType="CSWI_TYPE"/>
          <LN lnClass="TCTR" inst="6" lnType="TCTR_TYPE"/>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  <DataTypeTemplates>
    <LNodeType id="XCBR_TYPE" lnClass="XCBR">
      <DO name="Pos" type="DPC_TYPE"/>
      <DO name="BlkOpn" type="SPC_TYPE"/>
      <DO name="BlkCls" type="SPC_TYPE"/>
      <DO name="Beh" type="ENS_TYPE"/>
      <DO name="Health" type="ENS_TYPE"/>
      <DO name="Mod" type="ENC_TYPE"/>
      <DO name="OpCnt" type="INS_TYPE"/>
      <DO name="Loc" type="SPS_TYPE"/>
    </LNodeType>
    <LNodeType id="MMXU_TYPE" lnClass="MMXU">
      <DO name="TotW" type="MV_TYPE"/>
      <DO name="TotVAr" type="MV_TYPE"/>
      <DO name="TotVA" type="MV_TYPE"/>
      <DO name="TotPF" type="MV_TYPE"/>
      <DO name="Hz" type="MV_TYPE"/>
      <DO name="PhV" type="WYE_TYPE"/>
      <DO name="A" type="WYE_TYPE"/>
      <DO name="PPV" type="DEL_TYPE"/>
      <DO name="Beh" type="ENS_TYPE"/>
      <DO name="Health" type="ENS_TYPE"/>
      <DO name="Mod" type="ENC_TYPE"/>
      <DO name="MaxW" type="MV_TYPE"/>
      <DO name="MaxVAr" type="MV_TYPE"/>
      <DO name="MinW" type="MV_TYPE"/>
      <DO name="MinVAr" type="MV_TYPE"/>
    </LNodeType>
    <LNodeType id="PTRC_TYPE" lnClass="PTRC">
      <DO name="Str" type="ACD_TYPE"/>
      <DO name="Op" type="ACT_TYPE"/>
      <DO name="Tr" type="ACD_TYPE"/>
      <DO name="Beh" type="ENS_TYPE"/>
      <DO name="Health" type="ENS_TYPE"/>
      <DO name="Mod" type="ENC_TYPE"/>
    </LNodeType>
    <LNodeType id="GGIO_TYPE" lnClass="GGIO">
      <DO name="SPCSO1" type="SPC_TYPE"/>
      <DO name="SPCSO2" type="SPC_TYPE"/>
      <DO name="SPCSO3" type="SPC_TYPE"/>
      <DO name="SPCSO4" type="SPC_TYPE"/>
      <DO name="SPCSO5" type="SPC_TYPE"/>
      <DO name="SPCSO6" type="SPC_TYPE"/>
      <DO name="SPCSO7" type="SPC_TYPE"/>
      <DO name="SPCSO8" type="SPC_TYPE"/>
      <DO name="Ind1" type="SPS_TYPE"/>
      <DO name="Ind2" type="SPS_TYPE"/>
      <DO name="Ind3" type="SPS_TYPE"/>
      <DO name="Ind4" type="SPS_TYPE"/>
    </LNodeType>
    <LNodeType id="CSWI_TYPE" lnClass="CSWI">
      <DO name="Pos" type="DPC_TYPE"/>
      <DO name="Beh" type="ENS_TYPE"/>
      <DO name="Health" type="ENS_TYPE"/>
      <DO name="Mod" type="ENC_TYPE"/>
      <DO name="Loc" type="SPS_TYPE"/>
    </LNodeType>
    <LNodeType id="TCTR_TYPE" lnClass="TCTR">
      <DO name="Amp" type="SAV_TYPE"/>
      <DO name="Beh" type="ENS_TYPE"/>
      <DO name="Health" type="ENS_TYPE"/>
      <DO name="Mod" type="ENC_TYPE"/>
    </LNodeType>
    <LNodeType id="TVTR_TYPE" lnClass="TVTR">
      <DO name="Vol" type="SAV_TYPE"/>
      <DO name="Beh" type="ENS_TYPE"/>
      <DO name="Health" type="ENS_TYPE"/>
      <DO name="Mod" type="ENC_TYPE"/>
    </LNodeType>
    <DOType id="DPC_TYPE" cdc="DPC">
      <DA name="stVal" fc="ST" type="BOOLEAN"/>
      <DA name="q" fc="ST" type="Quality"/>
      <DA name="t" fc="ST" type="Timestamp"/>
      <DA name="ctlVal" fc="CO" type="BOOLEAN"/>
    </DOType>
    <DOType id="SPC_TYPE" cdc="SPC">
      <DA name="stVal" fc="ST" type="BOOLEAN"/>
      <DA name="q" fc="ST" type="Quality"/>
      <DA name="t" fc="ST" type="Timestamp"/>
      <DA name="ctlVal" fc="CO" type="BOOLEAN"/>
    </DOType>
    <DOType id="SPS_TYPE" cdc="SPS">
      <DA name="stVal" fc="ST" type="BOOLEAN"/>
      <DA name="q" fc="ST" type="Quality"/>
      <DA name="t" fc="ST" type="Timestamp"/>
    </DOType>
    <DOType id="ENS_TYPE" cdc="ENS">
      <DA name="stVal" fc="ST" type="INT32"/>
      <DA name="q" fc="ST" type="Quality"/>
      <DA name="t" fc="ST" type="Timestamp"/>
    </DOType>
    <DOType id="ENC_TYPE" cdc="ENC">
      <DA name="stVal" fc="ST" type="INT32"/>
      <DA name="q" fc="ST" type="Quality"/>
      <DA name="t" fc="ST" type="Timestamp"/>
      <DA name="ctlVal" fc="CO" type="INT32"/>
    </DOType>
    <DOType id="INS_TYPE" cdc="INS">
      <DA name="stVal" fc="ST" type="INT32"/>
      <DA name="q" fc="ST" type="Quality"/>
      <DA name="t" fc="ST" type="Timestamp"/>
    </DOType>
    <DOType id="MV_TYPE" cdc="MV">
      <DA name="mag" fc="MX" type="AnalogueValue"/>
      <DA name="q" fc="MX" type="Quality"/>
      <DA name="t" fc="MX" type="Timestamp"/>
    </DOType>
    <DOType id="WYE_TYPE" cdc="WYE">
      <DA name="phsA" fc="MX" type="CMV"/>
      <DA name="phsB" fc="MX" type="CMV"/>
      <DA name="phsC" fc="MX" type="CMV"/>
      <DA name="neut" fc="MX" type="CMV"/>
    </DOType>
    <DOType id="DEL_TYPE" cdc="DEL">
      <DA name="phsAB" fc="MX" type="CMV"/>
      <DA name="phsBC" fc="MX" type="CMV"/>
      <DA name="phsCA" fc="MX" type="CMV"/>
    </DOType>
    <DOType id="ACD_TYPE" cdc="ACD">
      <DA name="general" fc="ST" type="BOOLEAN"/>
      <DA name="dirGeneral" fc="ST" type="INT32"/>
      <DA name="q" fc="ST" type="Quality"/>
      <DA name="t" fc="ST" type="Timestamp"/>
    </DOType>
    <DOType id="ACT_TYPE" cdc="ACT">
      <DA name="general" fc="ST" type="BOOLEAN"/>
      <DA name="q" fc="ST" type="Quality"/>
      <DA name="t" fc="ST" type="Timestamp"/>
    </DOType>
    <DOType id="SAV_TYPE" cdc="SAV">
      <DA name="instMag" fc="MX" type="AnalogueValue"/>
      <DA name="q" fc="MX" type="Quality"/>
      <DA name="t" fc="MX" type="Timestamp"/>
    </DOType>
  </DataTypeTemplates>
</SCL>