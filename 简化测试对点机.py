#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版对点机测试
专门测试宽松解析功能
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_scd_parsing():
    """测试SCD解析功能"""
    print("🎯 测试SCD解析功能")
    print("=" * 50)
    
    scd_file = "220kVQFB_recovered.scd"
    
    if not os.path.exists(scd_file):
        print(f"❌ 文件不存在: {scd_file}")
        return False
    
    try:
        # 尝试导入scd_converter
        from scd_converter import SCDConverter
        
        print("✅ 成功导入SCDConverter")
        
        # 创建转换器实例
        converter = SCDConverter()
        
        print("✅ 创建转换器实例成功")
        
        # 尝试解析
        print(f"🔍 解析文件: {scd_file}")
        result = converter.parse_scd_file(scd_file)
        
        if result and len(converter.signal_points) > 0:
            print(f"✅ 标准解析成功: {len(converter.signal_points)}个数据点")
            return True
        else:
            print("⚠️ 标准解析失败，尝试宽松解析...")
            
            # 尝试宽松解析
            regex_result = parse_scd_with_regex(scd_file)
            if regex_result['success']:
                print(f"✅ 宽松解析成功: {regex_result['data_object_count']}个数据点")
                return True
            else:
                print("❌ 宽松解析也失败")
                return False
    
    except ImportError as e:
        print(f"❌ 导入SCDConverter失败: {e}")
        
        # 直接使用宽松解析
        print("🔧 直接使用宽松解析...")
        regex_result = parse_scd_with_regex(scd_file)
        if regex_result['success']:
            print(f"✅ 宽松解析成功: {regex_result['data_object_count']}个数据点")
            return True
        else:
            print("❌ 宽松解析失败")
            return False
    
    except Exception as e:
        print(f"❌ 解析异常: {e}")
        return False

def parse_scd_with_regex(scd_file):
    """使用正则表达式解析SCD文件"""
    try:
        import re
        
        print(f"🔧 正则表达式解析: {os.path.basename(scd_file)}")
        
        # 读取文件内容
        with open(scd_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 提取IED信息
        ied_pattern = r'<IED[^>]*name="([^"]*)"[^>]*>'
        ieds = re.findall(ied_pattern, content)
        
        # 提取数据对象实例
        doi_pattern = r'<DOI[^>]*name="([^"]*)"[^>]*>'
        dois = re.findall(doi_pattern, content)
        
        result = {
            'success': True,
            'ied_count': len(ieds),
            'data_object_count': len(dois),
            'content_length': len(content)
        }
        
        print(f"📊 正则解析结果: {len(ieds)}个IED, {len(dois)}个数据对象")
        
        return result
        
    except Exception as e:
        print(f"❌ 正则表达式解析失败: {e}")
        return {'success': False, 'error': str(e)}

def test_point_table():
    """测试点表读取"""
    print("\n🎯 测试点表读取")
    print("=" * 50)
    
    table_file = "point_table_regex_20250705_142137.csv"
    
    if not os.path.exists(table_file):
        print(f"❌ 文件不存在: {table_file}")
        return False
    
    try:
        import pandas as pd
        
        # 读取点表文件
        df = pd.read_csv(table_file, encoding='utf-8-sig')
        
        print(f"✅ 点表读取成功: {len(df)}个数据点")
        
        # 显示基本信息
        if '信号类型' in df.columns:
            signal_types = df['信号类型'].value_counts()
            print(f"📊 信号类型分布: {dict(signal_types)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 点表读取失败: {e}")
        return False

def test_compatibility():
    """测试兼容性"""
    print("\n🎯 测试文件兼容性")
    print("=" * 50)
    
    scd_ok = test_scd_parsing()
    table_ok = test_point_table()
    
    if scd_ok and table_ok:
        print("\n✅ 兼容性测试通过！")
        print("💡 文件可以用于同时加载功能")
        return True
    else:
        print("\n❌ 兼容性测试失败")
        return False

def main():
    """主函数"""
    print("🎯 简化版对点机测试")
    print("=" * 60)
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查必要文件
    required_files = [
        "220kVQFB_recovered.scd",
        "point_table_regex_20250705_142137.csv"
    ]
    
    print(f"\n📁 检查必要文件:")
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file) / 1024 / 1024
            print(f"   ✅ {file} ({size:.1f}MB)")
        else:
            print(f"   ❌ {file} (不存在)")
    
    # 执行兼容性测试
    success = test_compatibility()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成：文件兼容性良好！")
        print("💡 建议:")
        print("   1. 启动完整的对点机程序")
        print("   2. 使用同时加载功能")
        print("   3. 选择这两个文件")
        print("   4. 忽略XML格式警告")
        print("   5. 继续加载并测试")
    else:
        print("❌ 测试失败：需要检查文件")
    
    print(f"🕐 测试结束: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
