#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证超宽松SCD验证模式
"""

import os
from datetime import datetime

def test_ultra_loose_validation(filename):
    """测试超宽松验证模式"""
    print(f"🔍 测试超宽松验证: {filename}")
    print("-" * 40)
    
    try:
        # 模拟修复后的超宽松验证逻辑
        
        # 只做最基本的文件存在检查
        if not os.path.exists(filename):
            print(f"❌ 文件不存在: {filename}")
            return False
        
        # 检查文件大小
        file_size = os.path.getsize(filename)
        if file_size == 0:
            print(f"❌ 文件为空")
            return False
        
        print(f"📄 文件大小: {file_size:,} bytes ({file_size/1024:.1f}KB)")
        
        # 尝试读取文件开头
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                header = f.read(1000)  # 读取前1000字符
            print(f"✅ 文件可读取")
        except UnicodeDecodeError:
            try:
                with open(filename, 'r', encoding='gbk') as f:
                    header = f.read(1000)
                print(f"✅ 文件可读取 (GBK编码)")
            except:
                print(f"⚠️ 文件编码特殊，但继续处理")
                return True  # 即使编码有问题也继续
        except Exception as e:
            print(f"⚠️ 文件读取异常: {e}，但继续处理")
            return True  # 即使读取有问题也继续
        
        # 简单检查是否包含XML相关内容
        if '<?xml' in header or '<SCL' in header or '<scl' in header:
            print(f"✅ 发现XML/SCD标记")
        else:
            print(f"⚠️ 未发现明显的XML/SCD标记，但继续处理")
        
        print(f"✅ SCD文件基本检查通过 (超宽松模式)")
        return True
        
    except Exception as e:
        print(f"⚠️ 验证过程异常: {e}")
        print(f"✅ 异常情况下也继续处理文件")
        return True  # 任何异常都不阻止处理

def main():
    """主函数"""
    print("🎯 超宽松SCD验证模式测试")
    print("=" * 50)
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试各种文件
    test_files = [
        "scd_30points_20250704_162945.scd",
        "large_substation_2000points.scd", 
        "test_substation.scd",
        r"222\220kVQFB.scd",
        "不存在的文件.scd"  # 测试不存在的文件
    ]
    
    success_count = 0
    total_count = 0
    
    for filename in test_files:
        print(f"\n{'='*50}")
        total_count += 1
        
        if test_ultra_loose_validation(filename):
            success_count += 1
            print(f"✅ {filename} 验证通过")
        else:
            print(f"❌ {filename} 验证失败")
    
    print(f"\n🎉 超宽松验证测试结果:")
    print(f"   总文件数: {total_count}")
    print(f"   验证通过: {success_count}")
    print(f"   成功率: {success_count/total_count*100:.1f}%")
    
    print(f"\n💡 超宽松模式特点:")
    print(f"   1. 只检查文件存在和非空")
    print(f"   2. 编码问题不阻止处理")
    print(f"   3. XML格式问题不阻止处理")
    print(f"   4. 任何异常都不阻止处理")
    print(f"   5. 几乎所有文件都会通过验证")
    
    print(f"\n🎮 对点机使用效果:")
    print(f"   ✅ 不会再出现'SCD文件格式验证失败'错误")
    print(f"   ✅ 所有存在的SCD文件都可以尝试解析")
    print(f"   ✅ 即使解析失败也会给出具体错误信息")
    print(f"   ✅ 用户体验大大改善")

if __name__ == "__main__":
    main()
