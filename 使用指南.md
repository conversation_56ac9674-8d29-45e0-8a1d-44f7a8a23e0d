# Auto_Point 对点机使用指南

## 🎯 概述

Auto_Point是一个专业的变电站对点测试系统，支持IEC 61850标准，提供完整的SCD文件解析、点表管理、通信数据监控和自动对点测试功能。

### ✨ 最新功能特性
- **同时加载SCD和点表**: 满足对策设备双重文件要求
- **目录文件选择**: 灵活的文件管理和选择方式
- **智能兼容性检查**: 自动检测文件匹配度和兼容性
- **通信数据监控**: 实时观察数据传输过程（二进制码/ASCII码）
- **SCD文件修复**: 自动修复XML命名空间等问题

## 🚀 快速开始

### 系统要求
- Python 3.8+
- PySide6
- pandas, xml.etree.ElementTree
- 8GB+ 内存（处理大型SCD文件）

### 启动系统
```bash
# 启动对点机
python main_web_functional.py

# 启动子站模拟器（可选）
python substation_simulator.py
```

### 访问Web界面
打开浏览器访问：`http://localhost:8080`

## 📋 主要功能模块

### 1. 配置文件管理 🗂️
#### **基础功能**
- **SCD文件解析**: 支持IEC 61850标准SCD文件
- **点表文件加载**: 支持CSV、Excel格式点表
- **文件格式转换**: SCD转点表，格式标准化

#### **新增功能**
- **同时加载SCD和点表**: 
  - 对策设备要求的双重文件支持
  - 智能文件匹配和兼容性检查
  - 一键加载双重配置
- **目录文件选择**:
  - 可视化目录浏览器
  - 支持任意路径文件选择
  - 智能文件类型识别
- **快速选择点表**:
  - 下拉框快速选择
  - 文件大小智能分类
  - 一键加载功能

### 2. 通信配置 📡
#### **基础功能**
- **连接参数设置**: IP地址、端口、协议配置
- **通信测试**: 连接状态检测和验证
- **协议支持**: IEC 61850、Modbus等

#### **新增功能**
- **通信数据监控**: 
  - 实时数据传输监控
  - 多格式显示（HEX/BIN/ASCII/混合）
  - 独立监控窗口
  - 传输统计和分析
- **连接状态智能检测**:
  - 自动检测连接状态
  - 连接成功后启用监控功能
  - 实时状态更新

### 3. 自动对点 🎯
- **自动测试执行**: 批量数据点测试
- **手动测试**: 单点测试功能
- **测试速度调节**: 可配置测试间隔
- **结果分析**: 详细的对点结果分析
- **报告生成**: 专业的Excel/HTML/CSV/JSON报告

### 4. 智能检查 🔍
- **文件兼容性检查**: 自动检测SCD和点表匹配度
- **SCD文件修复**: 自动修复XML命名空间问题
- **格式验证**: 超宽松验证模式，减少格式阻碍

## 🎮 详细使用流程

### 第一步：文件准备和加载

#### **方案A: 单独加载文件**
1. **选择文件方式**:
   - 点击"选择文件"：标准文件选择
   - 点击"浏览目录"：可视化目录浏览
   - 使用"快速选择点表"：下拉框快速选择

2. **文件类型支持**:
   - **SCD文件**: IEC 61850标准配置文件
   - **CSV文件**: 点表数据文件
   - **Excel文件**: 表格格式数据

#### **方案B: 同时加载SCD和点表（推荐）**
1. **进入同时加载区域**: "同时加载SCD和点表 (对策设备要求)"
2. **选择SCD文件**: 
   - 使用SCD文件下拉框
   - 或点击"浏览SCD目录"
3. **选择点表文件**:
   - 使用点表文件下拉框
   - 或点击"浏览点表目录"
4. **执行加载**: 点击"同时加载SCD和点表"
5. **兼容性检查**: 系统自动检查文件兼容性
6. **确认加载**: 根据检查结果决定是否继续

### 第二步：通信配置

#### **基础配置**
1. **进入通信配置模块**
2. **设置连接参数**:
   - 主机地址: `localhost` 或目标IP
   - 端口号: `102` (IEC 61850标准)
   - 协议类型: IEC 61850
3. **测试连接**: 点击"测试连接"按钮

#### **数据监控（新功能）**
1. **连接成功后**: "数据监控"按钮变为可用
2. **打开监控窗口**: 点击"数据监控"按钮
3. **配置监控参数**:
   - 选择显示格式: HEX/BIN/ASCII/混合
   - 启用时间戳显示
   - 设置自动滚动
4. **开始监控**: 点击"开始监控"
5. **观察数据传输**:
   - 左侧: 发送数据 (TX)
   - 右侧: 接收数据 (RX)
   - 底部: 传输统计

### 第三步：执行对点测试

#### **自动对点测试**
1. **进入自动对点模块**
2. **配置测试参数**:
   - 测试速度: 可调节间隔时间
   - 测试范围: 全部或部分数据点
3. **启动测试**: 点击"开始自动对点"
4. **监控进度**: 观察测试进度和状态
5. **查看结果**: 实时查看测试结果

#### **手动测试**
1. **选择数据点**: 从列表中选择特定数据点
2. **执行单点测试**: 点击"手动测试"
3. **查看结果**: 立即查看测试结果

### 第四步：结果分析和报告

#### **结果查看**
1. **测试结果表格**: 详细的对点结果
2. **统计信息**: 成功率、失败率等
3. **错误分析**: 失败原因分析

#### **报告生成**
1. **选择报告格式**: Excel/HTML/CSV/JSON
2. **生成报告**: 点击"生成报告"
3. **下载报告**: 获取专业的对点报告

## 🔧 高级功能

### SCD文件修复
```bash
# 修复XML命名空间问题
python 修复SCD文件.py

# 分析大型SCD文件
python 终极SCD分析工具.py
```

### 兼容性检查
```bash
# 检查文件兼容性
python 智能文件匹配检查.py
```

### 数据监控测试
```bash
# 测试监控功能
python 测试数据监控功能.py
```

## 📊 文件支持

### SCD文件
- **标准**: IEC 61850
- **大小**: 支持1KB-100MB+
- **格式**: 标准XML格式
- **修复**: 自动修复命名空间问题

### 点表文件
- **格式**: CSV, Excel (.xlsx)
- **编码**: UTF-8, GBK
- **大小**: 支持10KB-10MB+
- **列要求**: 信号名称、信号类型等

### 推荐文件组合
- **快速测试**: 30点位SCD + 对应CSV (9KB + 3.7KB)
- **功能验证**: 2000点位SCD + 对应CSV (392KB + 200KB)
- **性能测试**: 220kVQFB.scd + 大型CSV (64.4MB + 1MB+)

## 💡 使用建议

### 🎯 最佳实践
1. **文件准备**:
   - 先用小文件验证功能
   - 确保SCD和点表文件匹配
   - 使用兼容性检查功能

2. **测试流程**:
   - 先建立通信连接
   - 开启数据监控观察传输
   - 执行对点测试
   - 生成专业报告

3. **问题处理**:
   - 遇到SCD解析问题使用修复工具
   - 利用兼容性检查避免文件不匹配
   - 使用数据监控诊断通信问题

### ⚠️ 注意事项
- **大文件处理**: 64MB+文件需要8GB+内存
- **网络配置**: 确保防火墙允许通信端口
- **文件编码**: 推荐使用UTF-8编码
- **系统资源**: 监控大量数据时注意CPU和内存使用

### 🔧 故障排除
- **SCD解析失败**: 使用SCD修复工具
- **连接失败**: 检查IP地址和端口配置
- **文件不匹配**: 使用兼容性检查功能
- **性能问题**: 使用较小文件或增加系统内存
