#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版SCD文件转换器
解决大型SCD文件解析问题，正确提取所有数据点
"""

import xml.etree.ElementTree as ET
import csv
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

class FixedSCDConverter:
    """修复版SCD转换器"""
    
    def __init__(self):
        self.namespace = {}
        self.data_points = []
        self.statistics = {
            'total_ieds': 0,
            'total_logical_devices': 0,
            'total_logical_nodes': 0,
            'total_dois': 0,
            'total_dais': 0,
            'signal_types': {}
        }
    
    def parse_scd_file(self, scd_file_path: str) -> Dict[str, Any]:
        """解析SCD文件"""
        print(f"🔄 开始解析SCD文件: {scd_file_path}")
        
        try:
            # 解析XML文件
            tree = ET.parse(scd_file_path)
            root = tree.getroot()
            
            # 获取命名空间
            self.namespace = self._get_namespace(root)
            print(f"📋 检测到命名空间: {self.namespace}")
            
            # 解析IED设备
            ieds = self._find_elements(root, 'IED')
            self.statistics['total_ieds'] = len(ieds)
            print(f"🏭 找到IED设备: {len(ieds)}个")
            
            # 解析每个IED
            for ied in ieds:
                self._parse_ied(ied)
            
            # 生成统计信息
            self._generate_statistics()
            
            print(f"✅ SCD文件解析完成")
            print(f"📊 统计结果:")
            print(f"   🏭 IED设备: {self.statistics['total_ieds']}个")
            print(f"   📱 逻辑设备: {self.statistics['total_logical_devices']}个")
            print(f"   🔧 逻辑节点: {self.statistics['total_logical_nodes']}个")
            print(f"   📋 数据对象: {self.statistics['total_dois']}个")
            print(f"   📊 数据属性: {self.statistics['total_dais']}个")
            print(f"   🎯 提取数据点: {len(self.data_points)}个")
            
            return {
                'success': True,
                'data_points': self.data_points,
                'statistics': self.statistics
            }
            
        except Exception as e:
            print(f"❌ 解析SCD文件失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'data_points': [],
                'statistics': self.statistics
            }
    
    def _get_namespace(self, root) -> Dict[str, str]:
        """获取XML命名空间"""
        namespace = {}
        
        # 检查根元素的命名空间
        if root.tag.startswith('{'):
            # 提取命名空间URI
            ns_uri = root.tag[1:root.tag.find('}')]
            namespace[''] = ns_uri
            
        # 检查其他命名空间声明
        for key, value in root.attrib.items():
            if key.startswith('xmlns'):
                if key == 'xmlns':
                    namespace[''] = value
                else:
                    prefix = key.split(':')[1]
                    namespace[prefix] = value
        
        return namespace
    
    def _find_elements(self, parent, tag_name: str) -> List:
        """查找元素，支持命名空间"""
        elements = []
        
        # 尝试不同的查找方式
        if self.namespace:
            # 使用命名空间查找
            for prefix, uri in self.namespace.items():
                if prefix == '':
                    # 默认命名空间
                    namespaced_tag = f"{{{uri}}}{tag_name}"
                    elements.extend(parent.findall(f".//{namespaced_tag}"))
                else:
                    # 有前缀的命名空间
                    elements.extend(parent.findall(f".//{prefix}:{tag_name}", self.namespace))
        
        # 如果没有找到，尝试不使用命名空间
        if not elements:
            elements = parent.findall(f".//{tag_name}")
        
        return elements
    
    def _parse_ied(self, ied_element):
        """解析IED设备"""
        ied_name = ied_element.get('name', 'Unknown_IED')
        ied_desc = ied_element.get('desc', '')
        
        print(f"   🔄 解析IED: {ied_name}")
        
        # 查找逻辑设备
        logical_devices = self._find_elements(ied_element, 'LDevice')
        self.statistics['total_logical_devices'] += len(logical_devices)
        
        for ld in logical_devices:
            self._parse_logical_device(ld, ied_name)
    
    def _parse_logical_device(self, ld_element, ied_name: str):
        """解析逻辑设备"""
        ld_inst = ld_element.get('inst', 'LD0')
        
        # 查找逻辑节点
        logical_nodes = self._find_elements(ld_element, 'LN')
        self.statistics['total_logical_nodes'] += len(logical_nodes)
        
        for ln in logical_nodes:
            self._parse_logical_node(ln, ied_name, ld_inst)
    
    def _parse_logical_node(self, ln_element, ied_name: str, ld_inst: str):
        """解析逻辑节点"""
        ln_class = ln_element.get('lnClass', '')
        ln_inst = ln_element.get('inst', '')
        ln_desc = ln_element.get('desc', '')
        
        # 查找数据对象实例
        dois = self._find_elements(ln_element, 'DOI')
        self.statistics['total_dois'] += len(dois)
        
        for doi in dois:
            self._parse_doi(doi, ied_name, ld_inst, ln_class, ln_inst)
    
    def _parse_doi(self, doi_element, ied_name: str, ld_inst: str, ln_class: str, ln_inst: str):
        """解析数据对象实例"""
        doi_name = doi_element.get('name', '')
        doi_desc = doi_element.get('desc', '')
        
        # 查找数据属性实例
        dais = self._find_elements(doi_element, 'DAI')
        self.statistics['total_dais'] += len(dais)
        
        for dai in dais:
            self._parse_dai(dai, ied_name, ld_inst, ln_class, ln_inst, doi_name, doi_desc)
    
    def _parse_dai(self, dai_element, ied_name: str, ld_inst: str, ln_class: str, 
                   ln_inst: str, doi_name: str, doi_desc: str):
        """解析数据属性实例"""
        dai_name = dai_element.get('name', '')
        
        # 获取值
        val_element = self._find_elements(dai_element, 'Val')
        value = val_element[0].text if val_element else '0'
        
        # 生成信号名称
        signal_name = f"{ied_name}_{ln_class}{ln_inst}_{doi_name}_{dai_name}"
        
        # 确定信号类型和数据类型
        signal_type, data_type = self._determine_signal_type(ln_class, doi_name, dai_name, value)
        
        # 生成SCD路径
        scd_path = f"{ied_name}.{ld_inst}.{ln_class}{ln_inst}.{doi_name}.{dai_name}"
        
        # 生成描述
        description = f"{doi_desc}_{dai_name}" if doi_desc else f"{doi_name}_{dai_name}"
        
        # 分配地址
        address = self._allocate_address(signal_type)
        
        # 创建数据点
        data_point = {
            'address': address,
            'signal_name': signal_name,
            'signal_type': signal_type,
            'data_type': data_type,
            'expected_value': value,
            'description': description,
            'scd_path': scd_path,
            'ied_name': ied_name,
            'ln_class': ln_class,
            'ln_inst': ln_inst,
            'doi_name': doi_name,
            'dai_name': dai_name
        }
        
        self.data_points.append(data_point)
    
    def _determine_signal_type(self, ln_class: str, doi_name: str, dai_name: str, value: str) -> tuple:
        """确定信号类型和数据类型"""
        
        # 根据逻辑节点类型和数据对象名称判断
        if ln_class in ['XCBR', 'XSWI', 'CSWI']:  # 开关类
            if dai_name in ['stVal', 'q']:
                if doi_name in ['Pos', 'Loc', 'Blk', 'Alm']:
                    return 'DI', 'BOOL'  # 遥信
                elif doi_name in ['OpOpn', 'OpCls', 'BlkOpn', 'BlkCls']:
                    return 'DO', 'BOOL'  # 遥控
            elif dai_name in ['ctlVal', 'origin', 'ctlNum']:
                return 'DO', 'BOOL'  # 遥控
                
        elif ln_class in ['MMXU', 'TCTR', 'TVTR']:  # 测量类
            if dai_name in ['mag', 'ang', 'phsA', 'phsB', 'phsC']:
                return 'AI', 'FLOAT'  # 遥测
            elif dai_name in ['setMag', 'setAng']:
                return 'AO', 'FLOAT'  # 遥调
                
        elif ln_class in ['PTRC', 'PDIS', 'YPTR']:  # 保护类
            if dai_name in ['stVal', 'q', 'general']:
                return 'DI', 'BOOL'  # 遥信
            elif dai_name in ['ctlVal', 'origin']:
                return 'DO', 'BOOL'  # 遥控
                
        elif ln_class == 'GGIO':  # 通用输入输出
            if dai_name in ['stVal', 'q']:
                return 'DI', 'BOOL'  # 遥信
            elif dai_name in ['ctlVal', 'origin']:
                return 'DO', 'BOOL'  # 遥控
        
        # 默认根据值类型判断
        try:
            float(value)
            if '.' in value:
                return 'AI', 'FLOAT'  # 遥测
            else:
                return 'DI', 'BOOL'   # 遥信
        except:
            return 'DI', 'BOOL'  # 遥信
    
    def _allocate_address(self, signal_type: str) -> int:
        """分配地址"""
        # 地址分配规则
        address_ranges = {
            'DI': (1001, 1999),   # 遥信
            'AI': (2001, 2999),   # 遥测
            'DO': (3001, 3999),   # 遥控
            'AO': (4001, 4999)    # 遥调
        }
        
        # 统计当前信号类型的数量
        current_count = self.statistics['signal_types'].get(signal_type, 0)
        self.statistics['signal_types'][signal_type] = current_count + 1
        
        # 计算地址
        start_addr, _ = address_ranges.get(signal_type, (1001, 1999))
        return start_addr + current_count
    
    def _generate_statistics(self):
        """生成统计信息"""
        # 按信号类型统计
        for point in self.data_points:
            signal_type = point['signal_type']
            self.statistics['signal_types'][signal_type] = \
                self.statistics['signal_types'].get(signal_type, 0) + 1
    
    def export_to_csv(self, output_path: str) -> bool:
        """导出为CSV文件"""
        try:
            with open(output_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['点号', '信号名称', '信号类型', '数据类型', '期望值', '描述', 'SCD路径', 'IED名称']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                # 写入标题行
                writer.writeheader()
                
                # 写入数据行
                for point in self.data_points:
                    writer.writerow({
                        '点号': point['address'],
                        '信号名称': point['signal_name'],
                        '信号类型': point['signal_type'],
                        '数据类型': point['data_type'],
                        '期望值': point['expected_value'],
                        '描述': point['description'],
                        'SCD路径': point['scd_path'],
                        'IED名称': point['ied_name']
                    })
            
            print(f"✅ 点表导出成功: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ 点表导出失败: {e}")
            return False

def test_fixed_converter():
    """测试修复版转换器"""
    print("🧪 测试修复版SCD转换器")
    print("=" * 60)
    
    # 创建转换器实例
    converter = FixedSCDConverter()
    
    # 测试大型SCD文件
    scd_file = "large_substation_2000points.scd"
    
    if not os.path.exists(scd_file):
        print(f"❌ SCD文件不存在: {scd_file}")
        return False
    
    # 解析SCD文件
    result = converter.parse_scd_file(scd_file)
    
    if result['success']:
        # 生成输出文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f"point_table_fixed_{timestamp}.csv"
        
        # 导出点表
        if converter.export_to_csv(output_file):
            print(f"\n🎉 修复版转换器测试成功!")
            print(f"📄 输出文件: {output_file}")
            print(f"📊 提取数据点: {len(result['data_points'])}个")
            
            # 显示信号类型分布
            signal_types = result['statistics']['signal_types']
            print(f"📈 信号类型分布:")
            for signal_type, count in signal_types.items():
                print(f"   {signal_type}: {count}个")
            
            return True
        else:
            print(f"❌ 点表导出失败")
            return False
    else:
        print(f"❌ SCD文件解析失败: {result['error']}")
        return False

if __name__ == "__main__":
    success = test_fixed_converter()
    
    if success:
        print(f"\n✅ 修复版SCD转换器测试完成!")
        print(f"💡 现在应该能正确提取所有数据点了")
    else:
        print(f"\n❌ 修复版SCD转换器测试失败")
        print(f"💡 请检查SCD文件和转换逻辑")
