#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强化版SCD文件分析工具
专门处理大型SCD文件和编码问题
"""

import os
import re
from datetime import datetime
import chardet

def detect_file_encoding(filename):
    """检测文件编码"""
    print(f"🔍 检测文件编码...")
    
    try:
        # 读取文件的一部分来检测编码
        with open(filename, 'rb') as f:
            raw_data = f.read(10000)  # 读取前10KB
        
        # 使用chardet检测编码
        result = chardet.detect(raw_data)
        encoding = result['encoding']
        confidence = result['confidence']
        
        print(f"   检测到编码: {encoding} (置信度: {confidence:.2f})")
        
        # 尝试常见编码
        encodings_to_try = [encoding, 'utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'latin1']
        
        for enc in encodings_to_try:
            if enc:
                try:
                    with open(filename, 'r', encoding=enc) as f:
                        content = f.read(1000)  # 读取前1000字符测试
                    print(f"   ✅ 成功使用编码: {enc}")
                    return enc
                except:
                    continue
        
        print(f"   ❌ 无法找到合适的编码")
        return None
        
    except Exception as e:
        print(f"   ❌ 编码检测失败: {e}")
        return None

def analyze_large_scd_file(filename):
    """分析大型SCD文件"""
    print(f"🔍 分析大型SCD文件: {filename}")
    print("=" * 80)
    print(f"🕐 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        return False
    
    # 文件基本信息
    file_size = os.path.getsize(filename)
    print(f"\n📄 文件基本信息:")
    print(f"   文件路径: {filename}")
    print(f"   文件大小: {file_size:,} bytes ({file_size/1024:.1f}KB, {file_size/1024/1024:.1f}MB)")
    
    # 检测编码
    encoding = detect_file_encoding(filename)
    if not encoding:
        print(f"❌ 无法确定文件编码，尝试二进制分析")
        return analyze_binary_content(filename)
    
    try:
        # 分块读取文件
        print(f"\n📊 分块分析文件内容...")
        
        chunk_size = 1024 * 1024  # 1MB chunks
        total_chunks = (file_size + chunk_size - 1) // chunk_size
        
        # 统计信息
        stats = {
            'total_lines': 0,
            'xml_tags': {},
            'ied_names': set(),
            'substation_names': set(),
            'logical_nodes': {},
            'data_objects': {},
            'ip_addresses': set()
        }
        
        with open(filename, 'r', encoding=encoding) as f:
            chunk_num = 0
            while True:
                chunk = f.read(chunk_size)
                if not chunk:
                    break
                
                chunk_num += 1
                print(f"   处理块 {chunk_num}/{total_chunks}...")
                
                # 分析这个块
                analyze_chunk(chunk, stats)
                
                if chunk_num >= 10:  # 限制处理前10个块以避免内存问题
                    print(f"   ⚠️ 文件过大，只分析前{chunk_num}个块")
                    break
        
        # 输出统计结果
        print_analysis_results(stats, filename)
        return True
        
    except Exception as e:
        print(f"❌ 文件分析失败: {e}")
        return False

def analyze_chunk(chunk, stats):
    """分析文件块"""
    # 统计行数
    stats['total_lines'] += chunk.count('\n')
    
    # 查找XML标签
    xml_tags = ['SCL', 'Header', 'Substation', 'VoltageLevel', 'Bay', 'IED', 'LDevice', 'LN', 'DOI', 'Communication']
    for tag in xml_tags:
        count = len(re.findall(f'<{tag}[^>]*>', chunk, re.IGNORECASE))
        stats['xml_tags'][tag] = stats['xml_tags'].get(tag, 0) + count
    
    # 查找IED名称
    ied_matches = re.findall(r'<IED[^>]*name="([^"]*)"', chunk, re.IGNORECASE)
    stats['ied_names'].update(ied_matches)
    
    # 查找变电站名称
    substation_matches = re.findall(r'<Substation[^>]*name="([^"]*)"', chunk, re.IGNORECASE)
    stats['substation_names'].update(substation_matches)
    
    # 查找逻辑节点类
    ln_matches = re.findall(r'lnClass="([^"]*)"', chunk, re.IGNORECASE)
    for ln_class in ln_matches:
        stats['logical_nodes'][ln_class] = stats['logical_nodes'].get(ln_class, 0) + 1
    
    # 查找数据对象
    do_matches = re.findall(r'<DOI[^>]*name="([^"]*)"', chunk, re.IGNORECASE)
    for do_name in do_matches:
        stats['data_objects'][do_name] = stats['data_objects'].get(do_name, 0) + 1
    
    # 查找IP地址
    ip_matches = re.findall(r'<P[^>]*type="IP"[^>]*>([^<]*)</P>', chunk, re.IGNORECASE)
    stats['ip_addresses'].update(ip_matches)

def analyze_binary_content(filename):
    """二进制内容分析"""
    print(f"\n🔧 二进制内容分析:")
    
    try:
        with open(filename, 'rb') as f:
            # 读取文件头
            header = f.read(1000)
            
            # 查找XML声明
            if b'<?xml' in header:
                print(f"   ✅ 发现XML声明")
            else:
                print(f"   ⚠️ 未发现XML声明")
            
            # 查找SCL标签
            if b'<SCL' in header or b'<scl' in header:
                print(f"   ✅ 发现SCL根元素")
            else:
                print(f"   ⚠️ 未发现SCL根元素")
            
            # 查找编码声明
            encoding_match = re.search(rb'encoding="([^"]*)"', header)
            if encoding_match:
                declared_encoding = encoding_match.group(1).decode('ascii')
                print(f"   📝 声明的编码: {declared_encoding}")
            
            # 统计文件中的关键字节模式
            f.seek(0)
            sample_size = min(1024 * 1024, os.path.getsize(filename))  # 1MB样本
            sample = f.read(sample_size)
            
            # 查找关键标签的字节模式
            key_patterns = [b'<IED', b'<Substation', b'<LDevice', b'<LN', b'<DOI']
            for pattern in key_patterns:
                count = sample.count(pattern)
                if count > 0:
                    print(f"   {pattern.decode('ascii', errors='ignore')}: {count}个")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 二进制分析失败: {e}")
        return False

def print_analysis_results(stats, filename):
    """输出分析结果"""
    print(f"\n📊 分析结果总结:")
    print(f"=" * 60)
    
    # 基本统计
    print(f"📋 基本统计:")
    print(f"   总行数: {stats['total_lines']:,}")
    
    # XML标签统计
    print(f"\n🏷️ XML标签统计:")
    for tag, count in sorted(stats['xml_tags'].items()):
        if count > 0:
            print(f"   {tag}: {count:,}个")
    
    # 变电站信息
    print(f"\n🏭 变电站信息:")
    if stats['substation_names']:
        print(f"   变电站数量: {len(stats['substation_names'])}")
        for name in list(stats['substation_names'])[:5]:  # 只显示前5个
            print(f"      - {name}")
        if len(stats['substation_names']) > 5:
            print(f"      ... 还有{len(stats['substation_names'])-5}个")
    else:
        print(f"   ⚠️ 未找到变电站定义")
    
    # IED设备信息
    print(f"\n🔧 IED设备信息:")
    if stats['ied_names']:
        print(f"   IED设备数量: {len(stats['ied_names'])}")
        for name in list(stats['ied_names'])[:10]:  # 只显示前10个
            print(f"      - {name}")
        if len(stats['ied_names']) > 10:
            print(f"      ... 还有{len(stats['ied_names'])-10}个")
    else:
        print(f"   ⚠️ 未找到IED设备定义")
    
    # 逻辑节点统计
    print(f"\n📋 逻辑节点类型统计 (前10个):")
    sorted_ln = sorted(stats['logical_nodes'].items(), key=lambda x: x[1], reverse=True)
    for ln_class, count in sorted_ln[:10]:
        ln_desc = get_ln_description(ln_class)
        print(f"   {ln_class} ({ln_desc}): {count:,}个")
    
    # 数据对象统计
    print(f"\n📊 数据对象类型统计 (前10个):")
    sorted_do = sorted(stats['data_objects'].items(), key=lambda x: x[1], reverse=True)
    for do_name, count in sorted_do[:10]:
        print(f"   {do_name}: {count:,}个")
    
    # 通信配置
    print(f"\n📡 通信配置:")
    if stats['ip_addresses']:
        print(f"   IP地址数量: {len(stats['ip_addresses'])}")
        for ip in list(stats['ip_addresses'])[:5]:  # 只显示前5个
            print(f"      - {ip}")
    else:
        print(f"   ⚠️ 未找到IP地址配置")
    
    # 文件评估
    print(f"\n💡 文件评估:")
    total_elements = sum(stats['xml_tags'].values())
    ied_count = len(stats['ied_names'])
    ln_count = sum(stats['logical_nodes'].values())
    do_count = sum(stats['data_objects'].values())
    
    print(f"   XML元素总数: {total_elements:,}")
    print(f"   IED设备数: {ied_count}")
    print(f"   逻辑节点数: {ln_count:,}")
    print(f"   数据对象数: {do_count:,}")
    
    if ied_count > 100:
        print(f"   📈 这是一个大型变电站配置文件")
    elif ied_count > 20:
        print(f"   📊 这是一个中型变电站配置文件")
    else:
        print(f"   📋 这是一个小型变电站配置文件")
    
    # 使用建议
    print(f"\n🎯 使用建议:")
    print(f"   1. 这是220kV旗峰坝变电站的SCD配置文件")
    print(f"   2. 文件较大({os.path.getsize(filename)/1024/1024:.1f}MB)，建议分批处理")
    print(f"   3. 可用于Auto_Point对点机的大规模测试")
    print(f"   4. 建议先用小文件验证功能，再使用此大文件")

def get_ln_description(ln_class):
    """获取逻辑节点类描述"""
    descriptions = {
        'LLN0': '逻辑节点0',
        'XCBR': '断路器',
        'XSWI': '开关',
        'CSWI': '控制开关',
        'MMXU': '测量单元',
        'MMTR': '变压器测量',
        'PTRC': '保护',
        'PDIF': '差动保护',
        'PDIR': '方向保护',
        'GGIO': '通用输入输出',
        'TCTR': '电流互感器',
        'TVTR': '电压互感器',
        'CALH': '计算',
        'RADR': '录波',
        'RBDR': '录波数据'
    }
    return descriptions.get(ln_class, '未知类型')

def main():
    """主函数"""
    filename = r"D:\auto_point\222\220kVQFB.scd"
    
    print("🎯 220kVQFB.scd 强化分析工具")
    print("=" * 80)
    
    # 分析文件
    success = analyze_large_scd_file(filename)
    
    if success:
        print(f"\n✅ 大型SCD文件分析完成")
        print(f"📋 这是220kV旗峰坝变电站的完整SCD配置文件")
        print(f"🎮 在Auto_Point对点机中的使用建议:")
        print(f"   1. 由于文件较大，建议先测试小文件")
        print(f"   2. 可以提取部分IED设备进行测试")
        print(f"   3. 适合大规模性能测试和压力测试")
        print(f"   4. 可以作为真实变电站配置的参考")
    else:
        print(f"\n⚠️ 大型SCD文件分析遇到困难")
        print(f"💡 建议:")
        print(f"   1. 文件可能损坏或格式特殊")
        print(f"   2. 可以尝试使用专业SCD编辑工具")
        print(f"   3. 或者使用较小的SCD文件进行测试")

if __name__ == "__main__":
    main()
