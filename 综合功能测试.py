#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Auto_Point 综合功能测试
验证所有核心功能是否正常工作
"""

import os
import sys
import time
import subprocess
from datetime import datetime

def print_header(title):
    """打印测试标题"""
    print(f"\n{'='*80}")
    print(f"🧪 {title}")
    print(f"{'='*80}")

def print_section(title):
    """打印测试章节"""
    print(f"\n{'🔹 ' + title}")
    print(f"{'-'*60}")

def test_file_structure():
    """测试文件结构完整性"""
    print_section("文件结构完整性测试")
    
    required_files = [
        'main_web_functional.py',
        'scd_to_point_converter.py',
        'report_generator.py',
        'substation_optimized.py',
        'large_substation_2000points.scd',
        'large_substation_500points.scd'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            size = os.path.getsize(file) / 1024  # KB
            print(f"   ✅ {file} ({size:.1f}KB)")
        else:
            missing_files.append(file)
            print(f"   ❌ {file} - 文件缺失")
    
    if missing_files:
        print(f"\n⚠️ 缺失文件: {len(missing_files)}个")
        return False
    else:
        print(f"\n✅ 所有核心文件完整 ({len(required_files)}个)")
        return True

def test_scd_converter():
    """测试SCD转换器功能"""
    print_section("SCD转换器功能测试")
    
    try:
        from scd_to_point_converter import SCDToPointConverter
        
        # 测试小型SCD文件
        print("   🔄 测试500点SCD文件转换...")
        converter = SCDToPointConverter()
        
        if os.path.exists('large_substation_500points.scd'):
            result = converter.parse_scd_file('large_substation_500points.scd')
            points_count = len(converter.signal_points)
            print(f"   📊 解析结果: {points_count}个数据点")
            
            if points_count >= 400:  # 允许一定误差
                print(f"   ✅ 500点SCD文件转换正常")
                
                # 测试转换为点表
                output_file = converter.convert_to_point_table('csv')
                if os.path.exists(output_file):
                    print(f"   ✅ 点表生成成功: {output_file}")
                    return True
                else:
                    print(f"   ❌ 点表生成失败")
                    return False
            else:
                print(f"   ❌ 数据点数量不足，期望>=400，实际{points_count}")
                return False
        else:
            print(f"   ❌ 测试SCD文件不存在")
            return False
            
    except Exception as e:
        print(f"   ❌ SCD转换器测试失败: {e}")
        return False

def test_large_scd_converter():
    """测试大型SCD转换器功能"""
    print_section("大型SCD转换器功能测试")
    
    try:
        from scd_to_point_converter import SCDToPointConverter
        
        # 测试大型SCD文件
        print("   🔄 测试2000点SCD文件转换...")
        converter = SCDToPointConverter()
        
        if os.path.exists('large_substation_2000points.scd'):
            result = converter.parse_scd_file('large_substation_2000points.scd')
            points_count = len(converter.signal_points)
            print(f"   📊 解析结果: {points_count}个数据点")
            
            if points_count >= 1800:  # 允许一定误差
                print(f"   ✅ 2000点SCD文件转换正常")
                
                # 测试信号类型分布
                signal_types = {}
                for point in converter.signal_points:
                    signal_type = point.get('signal_type', 'Unknown')
                    signal_types[signal_type] = signal_types.get(signal_type, 0) + 1
                
                print(f"   📈 信号类型分布:")
                for signal_type, count in signal_types.items():
                    print(f"      {signal_type}: {count}个")
                
                return True
            else:
                print(f"   ❌ 数据点数量不足，期望>=1800，实际{points_count}")
                return False
        else:
            print(f"   ❌ 大型测试SCD文件不存在")
            return False
            
    except Exception as e:
        print(f"   ❌ 大型SCD转换器测试失败: {e}")
        return False

def test_report_generator():
    """测试报告生成器功能"""
    print_section("报告生成器功能测试")
    
    try:
        from report_generator import create_test_report
        
        # 创建测试数据
        test_data = {
            'operator': '测试工程师',
            'project_name': '功能测试项目',
            'station_name': '测试变电站',
            'test_mode': '自动对点',
            'test_range': '全部信号',
            'speed_setting': '5级中等',
            'total_points': 100,
            'success_points': 95,
            'failed_points': 5,
            'success_rate': 95.0,
            'test_duration': '2分钟',
            'signal_types': {
                'DI': 40,
                'AI': 30,
                'DO': 20,
                'AO': 10
            }
        }
        
        print("   🔄 生成测试报告...")
        
        # 创建报告目录
        os.makedirs("test_reports", exist_ok=True)
        
        # 生成报告
        reports = create_test_report(test_data, "test_reports")
        
        if reports:
            print(f"   ✅ 报告生成成功:")
            for format_type, path in reports.items():
                if os.path.exists(path):
                    size = os.path.getsize(path) / 1024  # KB
                    print(f"      📄 {format_type.upper()}: {os.path.basename(path)} ({size:.1f}KB)")
                else:
                    print(f"      ❌ {format_type.upper()}: 文件生成失败")
            return True
        else:
            print(f"   ❌ 报告生成失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 报告生成器测试失败: {e}")
        return False

def test_web_interface():
    """测试Web界面启动"""
    print_section("Web界面启动测试")
    
    try:
        print("   🔄 检查Web界面文件...")
        
        if not os.path.exists('main_web_functional.py'):
            print("   ❌ Web界面主文件不存在")
            return False
        
        # 检查依赖模块
        required_modules = ['tkinter', 'csv', 'json', 'datetime']
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
                print(f"   ✅ {module} 模块可用")
            except ImportError:
                missing_modules.append(module)
                print(f"   ❌ {module} 模块缺失")
        
        if missing_modules:
            print(f"   ⚠️ 缺失模块: {missing_modules}")
            return False
        
        print("   ✅ Web界面依赖检查通过")
        print("   💡 可以手动运行: python main_web_functional.py")
        return True
        
    except Exception as e:
        print(f"   ❌ Web界面测试失败: {e}")
        return False

def test_substation_simulator():
    """测试子站模拟器"""
    print_section("子站模拟器测试")
    
    try:
        print("   🔄 检查子站模拟器文件...")
        
        if not os.path.exists('substation_optimized.py'):
            print("   ❌ 子站模拟器文件不存在")
            return False
        
        # 简单的语法检查
        with open('substation_optimized.py', 'r', encoding='utf-8') as f:
            content = f.read()
            if 'class' in content and 'def' in content:
                print("   ✅ 子站模拟器文件格式正常")
            else:
                print("   ❌ 子站模拟器文件格式异常")
                return False
        
        print("   ✅ 子站模拟器检查通过")
        print("   💡 可以手动运行: python substation_optimized.py")
        return True
        
    except Exception as e:
        print(f"   ❌ 子站模拟器测试失败: {e}")
        return False

def generate_test_report(results):
    """生成测试报告"""
    print_section("测试报告生成")
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f"测试报告_{timestamp}.txt"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("Auto_Point 综合功能测试报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            total_tests = len(results)
            passed_tests = sum(1 for result in results.values() if result)
            
            f.write(f"测试总数: {total_tests}\n")
            f.write(f"通过测试: {passed_tests}\n")
            f.write(f"失败测试: {total_tests - passed_tests}\n")
            f.write(f"通过率: {passed_tests/total_tests*100:.1f}%\n\n")
            
            f.write("详细结果:\n")
            f.write("-" * 30 + "\n")
            for test_name, result in results.items():
                status = "✅ 通过" if result else "❌ 失败"
                f.write(f"{test_name}: {status}\n")
        
        print(f"   ✅ 测试报告已生成: {report_file}")
        return True
        
    except Exception as e:
        print(f"   ❌ 测试报告生成失败: {e}")
        return False

def main():
    """主测试函数"""
    print_header("Auto_Point 综合功能测试")
    print(f"🕐 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 测试目录: {os.getcwd()}")
    
    # 执行所有测试
    test_results = {}
    
    test_results['文件结构完整性'] = test_file_structure()
    test_results['SCD转换器功能'] = test_scd_converter()
    test_results['大型SCD转换器'] = test_large_scd_converter()
    test_results['报告生成器功能'] = test_report_generator()
    test_results['Web界面检查'] = test_web_interface()
    test_results['子站模拟器检查'] = test_substation_simulator()
    
    # 生成测试报告
    generate_test_report(test_results)
    
    # 显示测试总结
    print_header("测试总结")
    
    total_tests = len(test_results)
    passed_tests = sum(1 for result in test_results.values() if result)
    failed_tests = total_tests - passed_tests
    
    print(f"📊 测试统计:")
    print(f"   🧪 总测试数: {total_tests}")
    print(f"   ✅ 通过测试: {passed_tests}")
    print(f"   ❌ 失败测试: {failed_tests}")
    print(f"   📈 通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！Auto_Point系统功能完整！")
        print(f"💡 建议:")
        print(f"   1. 运行 python main_web_functional.py 启动Web界面")
        print(f"   2. 运行 python substation_optimized.py 启动子站模拟器")
        print(f"   3. 在Web界面中测试SCD文件转换和对点功能")
    else:
        print(f"\n⚠️ 有{failed_tests}个测试失败，请检查相关功能")
        print(f"💡 建议:")
        print(f"   1. 检查失败的测试项目")
        print(f"   2. 确认相关文件是否存在")
        print(f"   3. 检查Python环境和依赖库")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✅ 综合测试完成 - 系统正常")
        sys.exit(0)
    else:
        print(f"\n❌ 综合测试完成 - 发现问题")
        sys.exit(1)
