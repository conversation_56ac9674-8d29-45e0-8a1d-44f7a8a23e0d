#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宽松解析SCD文件
使用正则表达式提取数据点信息，不依赖完整的XML解析
"""

import re
import pandas as pd
import os
from datetime import datetime

def extract_data_points_regex(file_path):
    """使用正则表达式提取数据点信息"""
    print(f"🔍 使用正则表达式解析: {os.path.basename(file_path)}")
    print("=" * 60)
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        print(f"📊 文件内容长度: {len(content):,} 字符")
        
        data_points = []
        
        # 1. 提取IED信息
        print("\n🔍 提取IED设备信息...")
        ied_pattern = r'<IED[^>]*name="([^"]*)"[^>]*>'
        ieds = re.findall(ied_pattern, content)
        print(f"✅ 找到 {len(ieds)} 个IED设备: {ieds[:5]}...")
        
        # 2. 提取逻辑设备信息
        print("\n🔍 提取逻辑设备信息...")
        ldevice_pattern = r'<LDevice[^>]*inst="([^"]*)"[^>]*>'
        ldevices = re.findall(ldevice_pattern, content)
        print(f"✅ 找到 {len(ldevices)} 个逻辑设备")
        
        # 3. 提取逻辑节点信息
        print("\n🔍 提取逻辑节点信息...")
        ln_pattern = r'<LN[^>]*lnClass="([^"]*)"[^>]*(?:inst="([^"]*)")?[^>]*>'
        lnodes = re.findall(ln_pattern, content)
        print(f"✅ 找到 {len(lnodes)} 个逻辑节点")
        
        # 4. 提取数据对象实例
        print("\n🔍 提取数据对象实例...")
        doi_pattern = r'<DOI[^>]*name="([^"]*)"[^>]*>'
        dois = re.findall(doi_pattern, content)
        print(f"✅ 找到 {len(dois)} 个数据对象实例")
        
        # 5. 更详细的数据点提取
        print("\n🔧 构建数据点信息...")
        
        # 使用更复杂的正则表达式提取完整的数据点信息
        # 查找IED -> LDevice -> LN -> DOI 的层次结构
        
        # 分段处理，查找每个IED的内容
        ied_sections = re.split(r'<IED[^>]*name="([^"]*)"[^>]*>', content)
        
        point_count = 0
        for i in range(1, len(ied_sections), 2):  # 每两个元素一组
            if i + 1 < len(ied_sections):
                ied_name = ied_sections[i]
                ied_content = ied_sections[i + 1]
                
                # 在这个IED中查找数据点
                points = extract_points_from_ied(ied_name, ied_content)
                data_points.extend(points)
                point_count += len(points)
                
                if len(points) > 0:
                    print(f"  IED {ied_name}: {len(points)} 个数据点")
        
        print(f"\n✅ 总共提取到 {len(data_points)} 个数据点")
        
        return data_points
        
    except Exception as e:
        print(f"❌ 正则表达式解析失败: {e}")
        return []

def extract_points_from_ied(ied_name, ied_content):
    """从单个IED的内容中提取数据点"""
    points = []
    
    try:
        # 查找LDevice
        ldevice_pattern = r'<LDevice[^>]*inst="([^"]*)"[^>]*>(.*?)</LDevice>'
        ldevices = re.findall(ldevice_pattern, ied_content, re.DOTALL)
        
        for ld_inst, ld_content in ldevices:
            # 查找LN和LN0
            ln_pattern = r'<LN(?:0)?[^>]*lnClass="([^"]*)"[^>]*(?:inst="([^"]*)")?[^>]*>(.*?)(?:</LN>|</LN0>)'
            lnodes = re.findall(ln_pattern, ld_content, re.DOTALL)
            
            for ln_class, ln_inst, ln_content in lnodes:
                # 查找DOI
                doi_pattern = r'<DOI[^>]*name="([^"]*)"[^>]*>(.*?)</DOI>'
                dois = re.findall(doi_pattern, ln_content, re.DOTALL)
                
                for do_name, doi_content in dois:
                    # 构造信号名称
                    if ln_inst:
                        signal_name = f"{ied_name}_{ld_inst}_{ln_class}{ln_inst}_{do_name}"
                    else:
                        signal_name = f"{ied_name}_{ld_inst}_{ln_class}_{do_name}"
                    
                    # 推断信号类型
                    signal_type = infer_signal_type(ln_class, do_name)
                    
                    # 尝试提取值
                    val_pattern = r'<Val[^>]*>([^<]*)</Val>'
                    vals = re.findall(val_pattern, doi_content)
                    value = vals[0] if vals else ""
                    
                    point = {
                        '信号名称': signal_name,
                        '信号类型': signal_type,
                        'IED名称': ied_name,
                        '逻辑设备': ld_inst,
                        '逻辑节点': f"{ln_class}{ln_inst}" if ln_inst else ln_class,
                        '数据对象': do_name,
                        '当前值': value,
                        '描述': f"{ln_class}类型的{do_name}数据对象"
                    }
                    
                    points.append(point)
    
    except Exception as e:
        print(f"⚠️ IED {ied_name} 解析部分失败: {e}")
    
    return points

def infer_signal_type(ln_class, do_name):
    """推断信号类型"""
    # 基于逻辑节点类型
    if ln_class in ['XCBR', 'XSWI', 'CSWI', 'CILO']:
        return 'DI'  # 开关类
    elif ln_class in ['MMXU', 'MMTR', 'MSQI']:
        return 'AI'  # 测量类
    elif ln_class in ['PTOC', 'PDIF', 'PTUV']:
        return 'DI'  # 保护类
    
    # 基于数据对象名称
    if any(keyword in do_name.upper() for keyword in ['POS', 'ST', 'IND', 'ALM']):
        return 'DI'
    elif any(keyword in do_name.upper() for keyword in ['MAG', 'ANG', 'VAL', 'PCT']):
        return 'AI'
    elif 'CTL' in do_name.upper():
        return 'DO'
    else:
        return 'DI'  # 默认

def generate_point_table_from_regex(data_points, output_file=None):
    """从正则表达式提取的数据生成点表"""
    if not data_points:
        print("❌ 没有数据点，无法生成点表")
        return False
    
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"point_table_regex_{timestamp}.csv"
    
    try:
        # 创建DataFrame
        df = pd.DataFrame(data_points)
        
        # 保存为CSV
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"✅ 点表生成成功: {output_file}")
        print(f"📊 包含 {len(data_points)} 个数据点")
        
        # 统计信号类型
        signal_type_counts = df['信号类型'].value_counts()
        print(f"📋 信号类型分布:")
        for signal_type, count in signal_type_counts.items():
            print(f"  {signal_type}: {count}个")
        
        # 显示前几个数据点
        print(f"\n📋 数据点示例:")
        for i, point in enumerate(data_points[:5]):
            print(f"  {i+1}. {point['信号名称']} ({point['信号类型']})")
        
        return True
        
    except Exception as e:
        print(f"❌ 点表生成失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 宽松SCD文件解析工具")
    print("=" * 60)
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试恢复后的文件
    scd_file = "220kVQFB_recovered.scd"
    
    if not os.path.exists(scd_file):
        print(f"❌ 文件不存在: {scd_file}")
        print("💡 请确保文件在当前目录中")
        return
    
    # 使用正则表达式解析
    data_points = extract_data_points_regex(scd_file)
    
    if data_points:
        print("\n🎉 正则表达式解析成功！")
        
        # 生成点表
        print("\n🔧 生成点表文件...")
        if generate_point_table_from_regex(data_points):
            print("\n✅ 测试完成：恢复文件可以通过正则表达式解析并生成点表！")
            print("💡 虽然XML结构有问题，但数据内容是可以提取的")
        else:
            print("\n❌ 点表生成失败")
    else:
        print("\n❌ 正则表达式解析也失败了")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
