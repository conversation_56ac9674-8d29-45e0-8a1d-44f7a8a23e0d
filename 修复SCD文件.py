#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复SCD文件的XML命名空间和格式问题
解决 "unbound prefix" 错误
"""

import xml.etree.ElementTree as ET
import os
import re
from datetime import datetime

def diagnose_scd_file(filename):
    """诊断SCD文件问题"""
    print(f"🔍 诊断SCD文件: {filename}")
    print("-" * 50)
    
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        return False
    
    file_size = os.path.getsize(filename) / 1024
    print(f"📏 文件大小: {file_size:.1f}KB")
    
    try:
        # 读取文件内容
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"📄 文件编码: UTF-8")
        print(f"📊 文件长度: {len(content)} 字符")
        
        # 检查XML声明
        if content.startswith('<?xml'):
            print("✅ 包含XML声明")
        else:
            print("⚠️ 缺少XML声明")
        
        # 检查根元素
        root_match = re.search(r'<(\w+:?\w*)[^>]*>', content)
        if root_match:
            root_tag = root_match.group(1)
            print(f"📋 根元素: {root_tag}")
        
        # 检查命名空间声明
        ns_patterns = [
            r'xmlns="[^"]*"',
            r'xmlns:\w+="[^"]*"',
            r'http://www\.iec\.ch/61850/2003/SCL'
        ]
        
        for pattern in ns_patterns:
            matches = re.findall(pattern, content)
            if matches:
                print(f"✅ 找到命名空间: {matches[0]}")
            else:
                print(f"⚠️ 未找到命名空间模式: {pattern}")
        
        # 检查未绑定的前缀
        unbound_prefixes = find_unbound_prefixes(content)
        if unbound_prefixes:
            print(f"❌ 发现未绑定前缀: {unbound_prefixes}")
        else:
            print("✅ 未发现未绑定前缀")
        
        # 尝试XML解析
        try:
            tree = ET.parse(filename)
            root = tree.getroot()
            print("✅ XML解析成功")
            
            # 统计元素
            elements = {}
            for elem in root.iter():
                tag = elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag
                elements[tag] = elements.get(tag, 0) + 1
            
            print(f"📊 元素统计: {dict(list(elements.items())[:5])}...")
            
        except ET.ParseError as e:
            print(f"❌ XML解析失败: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        return False

def find_unbound_prefixes(content):
    """查找未绑定的命名空间前缀"""
    # 查找所有使用的前缀
    used_prefixes = set(re.findall(r'<(\w+):', content))
    used_prefixes.update(re.findall(r'</(\w+):', content))
    
    # 查找声明的前缀
    declared_prefixes = set(re.findall(r'xmlns:(\w+)=', content))
    
    # 查找未绑定的前缀
    unbound = used_prefixes - declared_prefixes
    
    # 排除xml前缀（这是预定义的）
    unbound.discard('xml')
    
    return list(unbound)

def fix_scd_file(filename):
    """修复SCD文件"""
    print(f"\n🔧 修复SCD文件: {filename}")
    print("-" * 50)
    
    try:
        # 读取原文件
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复1: 确保有XML声明
        if not content.startswith('<?xml'):
            content = '<?xml version="1.0" encoding="UTF-8"?>\n' + content
            print("✅ 添加XML声明")
        
        # 修复2: 确保根元素有正确的命名空间
        if '<SCL' in content and 'xmlns=' not in content[:500]:
            # 在SCL标签中添加命名空间
            content = content.replace(
                '<SCL',
                '<SCL xmlns="http://www.iec.ch/61850/2003/SCL"'
            )
            print("✅ 添加默认命名空间")
        
        # 修复3: 处理常见的未绑定前缀
        common_prefixes = {
            'scl': 'http://www.iec.ch/61850/2003/SCL',
            'xsi': 'http://www.w3.org/2001/XMLSchema-instance'
        }
        
        unbound_prefixes = find_unbound_prefixes(content)
        for prefix in unbound_prefixes:
            if prefix in common_prefixes:
                # 在根元素中添加命名空间声明
                scl_pattern = r'(<SCL[^>]*)'
                replacement = f'\\1 xmlns:{prefix}="{common_prefixes[prefix]}"'
                content = re.sub(scl_pattern, replacement, content)
                print(f"✅ 绑定前缀 {prefix}")
        
        # 修复4: 清理重复的命名空间声明
        content = re.sub(r'(xmlns:\w+="[^"]*")\s+\1', r'\1', content)
        
        # 修复5: 确保schemaLocation正确
        if 'xsi:schemaLocation' not in content and 'xmlns:xsi' in content:
            scl_pattern = r'(<SCL[^>]*)'
            replacement = r'\1 xsi:schemaLocation="http://www.iec.ch/61850/2003/SCL SCL.xsd"'
            content = re.sub(scl_pattern, replacement, content)
            print("✅ 添加schemaLocation")
        
        # 检查是否有修改
        if content != original_content:
            # 生成修复后的文件名
            base_name = os.path.splitext(filename)[0]
            fixed_filename = f"{base_name}_fixed_{datetime.now().strftime('%Y%m%d_%H%M%S')}.scd"
            
            # 保存修复后的文件
            with open(fixed_filename, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ 修复完成，保存为: {fixed_filename}")
            
            # 验证修复后的文件
            if validate_fixed_file(fixed_filename):
                print("✅ 修复后文件验证通过")
                return fixed_filename
            else:
                print("❌ 修复后文件验证失败")
                return None
        else:
            print("ℹ️ 文件无需修复")
            return filename
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return None

def validate_fixed_file(filename):
    """验证修复后的文件"""
    try:
        tree = ET.parse(filename)
        root = tree.getroot()
        
        # 检查根元素
        if not root.tag.endswith('SCL'):
            return False
        
        # 检查基本结构
        has_header = False
        has_ied = False
        
        for child in root:
            tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
            if tag == 'Header':
                has_header = True
            elif tag == 'IED':
                has_ied = True
        
        return has_header and has_ied
        
    except Exception as e:
        print(f"验证异常: {e}")
        return False

def main():
    """主函数"""
    print("🎯 SCD文件修复工具")
    print("=" * 60)
    print(f"🕐 运行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 查找SCD文件
    scd_files = [f for f in os.listdir('.') if f.endswith('.scd')]
    
    if not scd_files:
        print("❌ 未找到SCD文件")
        return
    
    print(f"📁 找到 {len(scd_files)} 个SCD文件:")
    for i, file in enumerate(scd_files):
        size = os.path.getsize(file) / 1024
        print(f"   {i+1}. {file} ({size:.1f}KB)")
    
    # 处理每个SCD文件
    fixed_files = []
    for scd_file in scd_files:
        print(f"\n{'='*60}")
        
        # 诊断文件
        if diagnose_scd_file(scd_file):
            # 尝试修复
            fixed_file = fix_scd_file(scd_file)
            if fixed_file:
                fixed_files.append(fixed_file)
        else:
            print(f"❌ {scd_file} 诊断失败，跳过修复")
    
    # 总结
    print(f"\n🎉 修复完成总结:")
    print(f"   原始文件: {len(scd_files)}个")
    print(f"   修复成功: {len(fixed_files)}个")
    
    if fixed_files:
        print(f"\n✅ 修复后的文件:")
        for file in fixed_files:
            print(f"   - {file}")
        
        print(f"\n💡 使用建议:")
        print(f"   1. 在对点机中使用修复后的SCD文件")
        print(f"   2. 修复后的文件应该不会再出现命名空间错误")
        print(f"   3. 如果仍有问题，请检查文件内容的完整性")
    else:
        print(f"\n⚠️ 没有文件需要修复或修复失败")

if __name__ == "__main__":
    main()
