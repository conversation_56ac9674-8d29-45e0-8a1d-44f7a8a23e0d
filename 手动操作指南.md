# Auto_Point 手动操作指南

## 🎯 系统概述

Auto_Point系统包含两个主要组件，都需要用户手动操作：

1. **🎮 Auto_Point对点机** - Web界面，用户手动配置和控制测试
2. **🏭 子站模拟器** - GUI界面，用户手动配置和启动服务

## 🖥️ 当前运行状态

### **Auto_Point对点机 (Web界面)**
- ✅ **状态**: 正在运行 (终端ID 53)
- 🌐 **访问方式**: 通过Web浏览器操作
- 🎮 **用户操作**: 手动配置、测试、生成报告

### **子站模拟器**
- ✅ **自动服务**: 正在运行 (终端ID 64，端口102)
- 🖥️ **GUI界面**: 需要用户手动启动和操作
- 🔧 **用户操作**: 手动加载数据、配置参数、启动服务

## 📋 完整手动操作流程

### **第一部分: 子站模拟器手动操作**

#### **步骤1: 启动子站模拟器GUI**
```bash
# 在命令行中运行
python substation_optimized.py
```

#### **步骤2: 在GUI中手动配置**
1. **加载数据文件**:
   - 点击 "加载SCD文件" 或 "浏览"
   - 选择: `large_substation_2000points.scd` 或 `simple_point_table_*.csv`
   - 确认显示数据点数量

2. **配置网络参数**:
   - 服务器地址: `0.0.0.0`
   - 端口: `102`
   - 协议: `IEC 61850`

3. **手动启动服务**:
   - 点击 "启动服务" 或 "启用服务"
   - 观察状态变为 "服务运行中"
   - 确认显示 "端口102监听中"

#### **步骤3: 验证子站服务状态**
- 状态栏应显示: "服务运行中 - 端口102"
- 连接数显示: "当前连接: 0"
- 数据点显示: "数据点: 2000个" (或实际数量)

### **第二部分: Auto_Point对点机手动操作**

#### **步骤1: 访问Web界面**
1. **打开浏览器**
2. **访问地址**: `http://localhost:8080` (或界面显示的地址)
3. **确认界面加载**: 看到Auto_Point Web风格对点机界面

#### **步骤2: 手动配置通信连接**
1. **选择通信配置**:
   - 点击左侧导航 "🔧 通信配置"
   - 选择 "网关配置"

2. **手动输入连接参数**:
   ```
   服务器地址: localhost
   端口: 102
   协议: IEC 61850
   超时时间: 5000ms
   重试次数: 3
   ```

3. **手动测试连接**:
   - 点击 "测试连接" 按钮
   - 等待结果显示
   - 确认显示 "连接成功"

#### **步骤3: 手动配置对点测试**
1. **选择自动对点**:
   - 点击左侧导航 "🎮 自动对点"
   - 进入测试配置页面

2. **手动设置测试参数**:
   ```
   测试模式: 自动对点
   测试速度: 用户选择 (1-10级)
   测试范围: 全部信号 / 部分信号
   数据点选择: 用户手动选择
   ```

3. **手动启动测试**:
   - 检查配置无误
   - 点击 "开始自动对点" 按钮
   - 观察测试进度条

#### **步骤4: 手动监控测试过程**
1. **实时监控**:
   - 观察测试进度百分比
   - 查看当前测试的数据点
   - 监控成功/失败统计

2. **手动干预** (如需要):
   - 暂停测试: 点击 "暂停" 按钮
   - 停止测试: 点击 "停止" 按钮
   - 调整速度: 修改测试速度设置

#### **步骤5: 手动生成报告**
1. **测试完成后**:
   - 查看测试结果摘要
   - 检查成功率和失败点

2. **手动配置报告**:
   - 选择 "📋 报告管理" → "验收报告"
   - 手动填写报告信息:
     ```
     操作人员: [用户输入]
     项目名称: [用户输入]
     变电站名: [用户输入]
     测试日期: [自动填充]
     ```

3. **手动生成报告**:
   - 选择报告格式: Excel/HTML/CSV/JSON
   - 点击 "生成验收报告"
   - 手动下载或查看报告

## 🎮 用户交互要点

### **子站模拟器用户操作**
- ✋ **手动加载**: 用户必须手动选择和加载数据文件
- ⚙️ **手动配置**: 用户需要手动设置网络参数
- 🚀 **手动启动**: 用户必须手动点击启动服务
- 👀 **手动监控**: 用户需要观察连接状态和数据更新

### **Auto_Point对点机用户操作**
- 🔧 **手动配置**: 用户必须手动输入连接参数
- 🧪 **手动测试**: 用户需要手动测试连接
- 🎯 **手动设置**: 用户必须手动配置测试参数
- ▶️ **手动控制**: 用户手动启动、暂停、停止测试
- 📊 **手动生成**: 用户手动填写信息并生成报告

## 🔄 典型用户操作流程

### **准备阶段 (用户手动操作)**
1. 用户启动子站模拟器GUI
2. 用户手动加载SCD文件或点表
3. 用户手动配置网络参数
4. 用户手动启动子站服务
5. 用户打开Auto_Point Web界面

### **连接阶段 (用户手动操作)**
1. 用户在Web界面中配置通信参数
2. 用户手动测试连接
3. 用户确认连接成功
4. 用户检查数据点读取情况

### **测试阶段 (用户手动操作)**
1. 用户选择测试模式和参数
2. 用户手动启动对点测试
3. 用户监控测试进度
4. 用户根据需要调整或干预

### **报告阶段 (用户手动操作)**
1. 用户查看测试结果
2. 用户手动填写报告信息
3. 用户选择报告格式
4. 用户生成和下载报告

## 💡 用户操作建议

### **最佳实践**
1. **按顺序操作**: 先配置子站，再配置对点机
2. **逐步验证**: 每个步骤完成后都要验证结果
3. **保存配置**: 记录有效的配置参数供后续使用
4. **监控状态**: 始终关注系统状态和连接质量

### **用户注意事项**
1. **数据备份**: 测试前备份重要数据
2. **参数记录**: 记录成功的配置参数
3. **错误处理**: 遇到错误时查看详细信息
4. **结果保存**: 及时保存测试结果和报告

### **故障排除**
1. **连接失败**: 检查网络配置和服务状态
2. **测试异常**: 验证数据点配置和协议设置
3. **报告问题**: 确认报告信息填写完整
4. **性能问题**: 调整测试速度和范围

## 🎯 用户操作目标

### **子站模拟器目标**
- ✅ 成功加载数据点 (1000-2000个)
- ✅ 正确配置网络参数
- ✅ 成功启动网络服务
- ✅ 稳定提供数据服务

### **Auto_Point对点机目标**
- ✅ 成功连接到子站模拟器
- ✅ 正确读取数据点信息
- ✅ 完成自动对点测试
- ✅ 生成专业验收报告

---

**🎮 Auto_Point系统设计为用户手动操作模式，提供完整的用户控制和灵活性。用户可以根据实际需求手动配置每个环节，确保测试过程的准确性和可控性。**

*操作指南版本: v1.0*  
*适用于: 手动操作模式的Auto_Point系统*
