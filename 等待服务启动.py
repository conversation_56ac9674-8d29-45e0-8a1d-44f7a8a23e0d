#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
等待子站服务启动
实时监控子站模拟器服务启动状态
"""

import socket
import time
from datetime import datetime

def check_port_status(port):
    """检查单个端口状态"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        return result == 0
    except:
        return False

def monitor_service_startup():
    """监控服务启动"""
    print("🔄 实时监控子站模拟器服务启动")
    print("=" * 60)
    print(f"🕐 开始监控: {datetime.now().strftime('%H:%M:%S')}")
    print("💡 请在子站模拟器GUI中点击'启动服务'按钮")
    print("⏹️ 按 Ctrl+C 停止监控")
    print("-" * 60)
    
    ports_to_monitor = [102, 2404, 8080, 8888]
    check_count = 0
    
    try:
        while True:
            check_count += 1
            current_time = datetime.now().strftime('%H:%M:%S')
            
            active_ports = []
            for port in ports_to_monitor:
                if check_port_status(port):
                    active_ports.append(port)
            
            if active_ports:
                print(f"\n🎉 [{current_time}] 检测到服务启动!")
                for port in active_ports:
                    print(f"   ✅ 端口 {port} 正在监听")
                
                # 测试连接
                for port in active_ports:
                    try:
                        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                        sock.settimeout(2)
                        result = sock.connect_ex(('localhost', port))
                        
                        if result == 0:
                            print(f"   ✅ 端口 {port} 连接测试成功")
                            sock.close()
                            return port
                        else:
                            print(f"   ⚠️ 端口 {port} 监听但连接失败")
                        sock.close()
                    except Exception as e:
                        print(f"   ❌ 端口 {port} 连接测试异常: {e}")
                
                return active_ports[0]  # 返回第一个活跃端口
            else:
                if check_count % 5 == 0:  # 每5次检查显示一次状态
                    print(f"   [{current_time}] 等待服务启动... (检查次数: {check_count})")
            
            time.sleep(2)
            
    except KeyboardInterrupt:
        print(f"\n⏹️ [{datetime.now().strftime('%H:%M:%S')}] 停止监控")
        return None

def main():
    """主函数"""
    active_port = monitor_service_startup()
    
    if active_port:
        print(f"\n🎉 子站模拟器服务启动成功!")
        print(f"📡 活跃端口: {active_port}")
        print(f"🔗 连接地址: localhost:{active_port}")
        
        print(f"\n📋 下一步操作:")
        print(f"   1. 在Auto_Point Web界面中配置通信:")
        print(f"      - 服务器地址: localhost")
        print(f"      - 端口: {active_port}")
        print(f"      - 协议: IEC 61850")
        print(f"   2. 点击'测试连接'验证通信")
        print(f"   3. 开始自动对点测试")
        
        return True
    else:
        print(f"\n⏹️ 监控已停止")
        print(f"💡 如果您已在GUI中启动服务，请运行:")
        print(f"   python 简单检查子站.py")
        
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✅ 服务启动监控完成 - 服务正常")
    else:
        print(f"\n⚠️ 服务启动监控完成 - 未检测到服务")
