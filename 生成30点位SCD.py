#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成30个数据点的SCD文件
然后转换为点表文件
"""

import xml.etree.ElementTree as ET
from datetime import datetime
import os

def create_30_points_scd():
    """创建包含30个数据点的SCD文件"""
    print("🔄 生成30个数据点的SCD文件...")
    
    # 创建根元素
    root = ET.Element("SCL")
    root.set("xmlns", "http://www.iec.ch/61850/2003/SCL")
    root.set("xmlns:xsi", "http://www.w3.org/2001/XMLSchema-instance")
    root.set("xsi:schemaLocation", "http://www.iec.ch/61850/2003/SCL SCL.xsd")
    
    # Header
    header = ET.SubElement(root, "Header")
    header.set("id", "SCD_30Points")
    header.set("version", "1.0")
    header.set("revision", "A")
    header.set("toolID", "Auto_Point_Generator")
    header.set("nameStructure", "IEDName")
    
    text_elem = ET.SubElement(header, "Text")
    text_elem.text = "30个数据点的SCD配置文件"
    
    history = ET.SubElement(header, "History")
    hitem = ET.SubElement(history, "Hitem")
    hitem.set("version", "1.0")
    hitem.set("revision", "A")
    hitem.set("when", datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ'))
    hitem.set("who", "Auto_Point_System")
    hitem.set("what", "初始创建30点位SCD")
    hitem.set("why", "快速测试和验证")
    
    # Substation
    substation = ET.SubElement(root, "Substation")
    substation.set("name", "测试变电站30点")
    substation.set("desc", "包含30个数据点的测试变电站")
    
    # 220kV电压等级
    voltage_level = ET.SubElement(substation, "VoltageLevel")
    voltage_level.set("name", "220kV母线")
    voltage_level.set("desc", "220kV电压等级")
    voltage_level.set("nomFreq", "50")
    voltage_level.set("numPhases", "3")
    
    voltage = ET.SubElement(voltage_level, "Voltage")
    voltage.set("unit", "kV")
    voltage.set("multiplier", "k")
    voltage.text = "220"
    
    # 主变间隔
    bay1 = ET.SubElement(voltage_level, "Bay")
    bay1.set("name", "主变间隔")
    bay1.set("desc", "主变压器间隔")
    
    # 主变断路器
    equipment1 = ET.SubElement(bay1, "ConductingEquipment")
    equipment1.set("name", "QF_T1")
    equipment1.set("type", "CBR")
    equipment1.set("desc", "主变高压侧断路器")
    
    # 线路间隔
    bay2 = ET.SubElement(voltage_level, "Bay")
    bay2.set("name", "线路间隔")
    bay2.set("desc", "220kV线路间隔")
    
    # 线路断路器
    equipment2 = ET.SubElement(bay2, "ConductingEquipment")
    equipment2.set("name", "QF_L1")
    equipment2.set("type", "CBR")
    equipment2.set("desc", "线路断路器")
    
    # 母线间隔
    bay3 = ET.SubElement(voltage_level, "Bay")
    bay3.set("name", "母线间隔")
    bay3.set("desc", "母线分段间隔")
    
    # 母线分段断路器
    equipment3 = ET.SubElement(bay3, "ConductingEquipment")
    equipment3.set("name", "QF_BUS")
    equipment3.set("type", "CBR")
    equipment3.set("desc", "母线分段断路器")
    
    # Communication
    communication = ET.SubElement(root, "Communication")
    subnet = ET.SubElement(communication, "SubNetwork")
    subnet.set("name", "站控层网络")
    subnet.set("type", "8-MMS")
    subnet.set("desc", "站控层通信网络")
    
    # IED连接
    for i in range(1, 4):
        connected_ap = ET.SubElement(subnet, "ConnectedAP")
        connected_ap.set("iedName", f"IED_{i:03d}")
        connected_ap.set("apName", "AP1")
        
        address = ET.SubElement(connected_ap, "Address")
        
        ip_p = ET.SubElement(address, "P")
        ip_p.set("type", "IP")
        ip_p.text = f"192.168.1.{100+i}"
        
        subnet_p = ET.SubElement(address, "P")
        subnet_p.set("type", "IP-SUBNET")
        subnet_p.text = "*************"
        
        gateway_p = ET.SubElement(address, "P")
        gateway_p.set("type", "IP-GATEWAY")
        gateway_p.text = "***********"
    
    return root

def add_ied_definitions(root):
    """添加IED设备定义"""
    print("🔧 添加IED设备定义...")
    
    # IED设备定义
    ied_configs = [
        {"name": "IED_001", "desc": "主变保护装置", "bay": "主变间隔"},
        {"name": "IED_002", "desc": "线路保护装置", "bay": "线路间隔"},
        {"name": "IED_003", "desc": "母线保护装置", "bay": "母线间隔"}
    ]
    
    for ied_config in ied_configs:
        ied = ET.SubElement(root, "IED")
        ied.set("name", ied_config["name"])
        ied.set("type", "保护测控装置")
        ied.set("manufacturer", "Auto_Point")
        ied.set("configVersion", "1.0")
        ied.set("desc", ied_config["desc"])
        
        # AccessPoint
        access_point = ET.SubElement(ied, "AccessPoint")
        access_point.set("name", "AP1")
        
        # Server
        server = ET.SubElement(access_point, "Server")
        
        # Authentication
        auth = ET.SubElement(server, "Authentication")
        auth.set("none", "true")
        
        # LDevice
        ldevice = ET.SubElement(server, "LDevice")
        ldevice.set("inst", "LD0")
        ldevice.set("desc", "逻辑设备")
        
        # LN0
        ln0 = ET.SubElement(ldevice, "LN0")
        ln0.set("lnClass", "LLN0")
        ln0.set("inst", "")
        ln0.set("lnType", "LLN0_Type")
        ln0.set("desc", "逻辑节点0")
        
        # 根据IED类型添加不同的逻辑节点
        if ied_config["name"] == "IED_001":  # 主变保护
            add_transformer_protection_lns(ldevice)
        elif ied_config["name"] == "IED_002":  # 线路保护
            add_line_protection_lns(ldevice)
        elif ied_config["name"] == "IED_003":  # 母线保护
            add_bus_protection_lns(ldevice)

def add_transformer_protection_lns(ldevice):
    """添加主变保护逻辑节点"""
    # XCBR - 断路器
    xcbr = ET.SubElement(ldevice, "LN")
    xcbr.set("lnClass", "XCBR")
    xcbr.set("inst", "1")
    xcbr.set("lnType", "XCBR_Type")
    xcbr.set("desc", "主变断路器")
    
    # 位置
    pos_doi = ET.SubElement(xcbr, "DOI")
    pos_doi.set("name", "Pos")
    pos_dai = ET.SubElement(pos_doi, "DAI")
    pos_dai.set("name", "stVal")
    pos_val = ET.SubElement(pos_dai, "Val")
    pos_val.text = "false"
    
    # 分闸闭锁
    blkopn_doi = ET.SubElement(xcbr, "DOI")
    blkopn_doi.set("name", "BlkOpn")
    blkopn_dai = ET.SubElement(blkopn_doi, "DAI")
    blkopn_dai.set("name", "stVal")
    blkopn_val = ET.SubElement(blkopn_dai, "Val")
    blkopn_val.text = "false"
    
    # 合闸闭锁
    blkcls_doi = ET.SubElement(xcbr, "DOI")
    blkcls_doi.set("name", "BlkCls")
    blkcls_dai = ET.SubElement(blkcls_doi, "DAI")
    blkcls_dai.set("name", "stVal")
    blkcls_val = ET.SubElement(blkcls_dai, "Val")
    blkcls_val.text = "false"
    
    # PTRC - 保护
    ptrc = ET.SubElement(ldevice, "LN")
    ptrc.set("lnClass", "PTRC")
    ptrc.set("inst", "1")
    ptrc.set("lnType", "PTRC_Type")
    ptrc.set("desc", "主变保护")
    
    # 保护启动
    str_doi = ET.SubElement(ptrc, "DOI")
    str_doi.set("name", "Str")
    str_dai = ET.SubElement(str_doi, "DAI")
    str_dai.set("name", "general")
    str_val = ET.SubElement(str_dai, "Val")
    str_val.text = "false"
    
    # 保护动作
    op_doi = ET.SubElement(ptrc, "DOI")
    op_doi.set("name", "Op")
    op_dai = ET.SubElement(op_doi, "DAI")
    op_dai.set("name", "general")
    op_val = ET.SubElement(op_dai, "Val")
    op_val.text = "false"
    
    # MMXU - 测量
    mmxu = ET.SubElement(ldevice, "LN")
    mmxu.set("lnClass", "MMXU")
    mmxu.set("inst", "1")
    mmxu.set("lnType", "MMXU_Type")
    mmxu.set("desc", "测量单元")
    
    # 有功功率
    totw_doi = ET.SubElement(mmxu, "DOI")
    totw_doi.set("name", "TotW")
    totw_dai = ET.SubElement(totw_doi, "DAI")
    totw_dai.set("name", "mag.f")
    totw_val = ET.SubElement(totw_dai, "Val")
    totw_val.text = "220.5"
    
    # 无功功率
    totvar_doi = ET.SubElement(mmxu, "DOI")
    totvar_doi.set("name", "TotVAr")
    totvar_dai = ET.SubElement(totvar_doi, "DAI")
    totvar_dai.set("name", "mag.f")
    totvar_val = ET.SubElement(totvar_dai, "Val")
    totvar_val.text = "45.2"
    
    # 频率
    hz_doi = ET.SubElement(mmxu, "DOI")
    hz_doi.set("name", "Hz")
    hz_dai = ET.SubElement(hz_doi, "DAI")
    hz_dai.set("name", "mag.f")
    hz_val = ET.SubElement(hz_dai, "Val")
    hz_val.text = "50.0"
    
    # A相电压
    ppv_doi = ET.SubElement(mmxu, "DOI")
    ppv_doi.set("name", "PPV")
    ppv_sdi = ET.SubElement(ppv_doi, "SDI")
    ppv_sdi.set("name", "phsA")
    ppv_dai = ET.SubElement(ppv_sdi, "DAI")
    ppv_dai.set("name", "cVal.mag.f")
    ppv_val = ET.SubElement(ppv_dai, "Val")
    ppv_val.text = "220000.0"
    
    # CSWI - 控制开关
    cswi = ET.SubElement(ldevice, "LN")
    cswi.set("lnClass", "CSWI")
    cswi.set("inst", "1")
    cswi.set("lnType", "CSWI_Type")
    cswi.set("desc", "控制开关")
    
    # 控制位置
    pos_doi = ET.SubElement(cswi, "DOI")
    pos_doi.set("name", "Pos")
    pos_dai = ET.SubElement(pos_doi, "DAI")
    pos_dai.set("name", "ctlVal")
    pos_val = ET.SubElement(pos_dai, "Val")
    pos_val.text = "false"

def add_line_protection_lns(ldevice):
    """添加线路保护逻辑节点"""
    # XCBR - 断路器
    xcbr = ET.SubElement(ldevice, "LN")
    xcbr.set("lnClass", "XCBR")
    xcbr.set("inst", "1")
    xcbr.set("lnType", "XCBR_Type")
    xcbr.set("desc", "线路断路器")
    
    # 位置
    pos_doi = ET.SubElement(xcbr, "DOI")
    pos_doi.set("name", "Pos")
    pos_dai = ET.SubElement(pos_doi, "DAI")
    pos_dai.set("name", "stVal")
    pos_val = ET.SubElement(pos_dai, "Val")
    pos_val.text = "true"
    
    # 分闸闭锁
    blkopn_doi = ET.SubElement(xcbr, "DOI")
    blkopn_doi.set("name", "BlkOpn")
    blkopn_dai = ET.SubElement(blkopn_doi, "DAI")
    blkopn_dai.set("name", "stVal")
    blkopn_val = ET.SubElement(blkopn_dai, "Val")
    blkopn_val.text = "false"
    
    # 合闸闭锁
    blkcls_doi = ET.SubElement(xcbr, "DOI")
    blkcls_doi.set("name", "BlkCls")
    blkcls_dai = ET.SubElement(blkcls_doi, "DAI")
    blkcls_dai.set("name", "stVal")
    blkcls_val = ET.SubElement(blkcls_dai, "Val")
    blkcls_val.text = "false"
    
    # PTRC - 保护
    ptrc = ET.SubElement(ldevice, "LN")
    ptrc.set("lnClass", "PTRC")
    ptrc.set("inst", "1")
    ptrc.set("lnType", "PTRC_Type")
    ptrc.set("desc", "线路保护")
    
    # 保护启动
    str_doi = ET.SubElement(ptrc, "DOI")
    str_doi.set("name", "Str")
    str_dai = ET.SubElement(str_doi, "DAI")
    str_dai.set("name", "general")
    str_val = ET.SubElement(str_dai, "Val")
    str_val.text = "false"
    
    # 保护动作
    op_doi = ET.SubElement(ptrc, "DOI")
    op_doi.set("name", "Op")
    op_dai = ET.SubElement(op_doi, "DAI")
    op_dai.set("name", "general")
    op_val = ET.SubElement(op_dai, "Val")
    op_val.text = "false"
    
    # MMXU - 测量
    mmxu = ET.SubElement(ldevice, "LN")
    mmxu.set("lnClass", "MMXU")
    mmxu.set("inst", "1")
    mmxu.set("lnType", "MMXU_Type")
    mmxu.set("desc", "线路测量")
    
    # 有功功率
    totw_doi = ET.SubElement(mmxu, "DOI")
    totw_doi.set("name", "TotW")
    totw_dai = ET.SubElement(totw_doi, "DAI")
    totw_dai.set("name", "mag.f")
    totw_val = ET.SubElement(totw_dai, "Val")
    totw_val.text = "180.3"
    
    # A相电流
    a_doi = ET.SubElement(mmxu, "DOI")
    a_doi.set("name", "A")
    a_sdi = ET.SubElement(a_doi, "SDI")
    a_sdi.set("name", "phsA")
    a_dai = ET.SubElement(a_sdi, "DAI")
    a_dai.set("name", "cVal.mag.f")
    a_val = ET.SubElement(a_dai, "Val")
    a_val.text = "1000.0"
    
    # B相电流
    b_sdi = ET.SubElement(a_doi, "SDI")
    b_sdi.set("name", "phsB")
    b_dai = ET.SubElement(b_sdi, "DAI")
    b_dai.set("name", "cVal.mag.f")
    b_val = ET.SubElement(b_dai, "Val")
    b_val.text = "1000.0"
    
    # C相电流
    c_sdi = ET.SubElement(a_doi, "SDI")
    c_sdi.set("name", "phsC")
    c_dai = ET.SubElement(c_sdi, "DAI")
    c_dai.set("name", "cVal.mag.f")
    c_val = ET.SubElement(c_dai, "Val")
    c_val.text = "1000.0"
    
    # CSWI - 控制开关
    cswi = ET.SubElement(ldevice, "LN")
    cswi.set("lnClass", "CSWI")
    cswi.set("inst", "1")
    cswi.set("lnType", "CSWI_Type")
    cswi.set("desc", "线路控制")
    
    # 控制位置
    pos_doi = ET.SubElement(cswi, "DOI")
    pos_doi.set("name", "Pos")
    pos_dai = ET.SubElement(pos_doi, "DAI")
    pos_dai.set("name", "ctlVal")
    pos_val = ET.SubElement(pos_dai, "Val")
    pos_val.text = "false"

def add_bus_protection_lns(ldevice):
    """添加母线保护逻辑节点"""
    # XCBR - 断路器
    xcbr = ET.SubElement(ldevice, "LN")
    xcbr.set("lnClass", "XCBR")
    xcbr.set("inst", "1")
    xcbr.set("lnType", "XCBR_Type")
    xcbr.set("desc", "母线分段断路器")
    
    # 位置
    pos_doi = ET.SubElement(xcbr, "DOI")
    pos_doi.set("name", "Pos")
    pos_dai = ET.SubElement(pos_doi, "DAI")
    pos_dai.set("name", "stVal")
    pos_val = ET.SubElement(pos_dai, "Val")
    pos_val.text = "true"
    
    # 分闸闭锁
    blkopn_doi = ET.SubElement(xcbr, "DOI")
    blkopn_doi.set("name", "BlkOpn")
    blkopn_dai = ET.SubElement(blkopn_doi, "DAI")
    blkopn_dai.set("name", "stVal")
    blkopn_val = ET.SubElement(blkopn_dai, "Val")
    blkopn_val.text = "false"
    
    # 合闸闭锁
    blkcls_doi = ET.SubElement(xcbr, "DOI")
    blkcls_doi.set("name", "BlkCls")
    blkcls_dai = ET.SubElement(blkcls_doi, "DAI")
    blkcls_dai.set("name", "stVal")
    blkcls_val = ET.SubElement(blkcls_dai, "Val")
    blkcls_val.text = "false"
    
    # PTRC - 保护
    ptrc = ET.SubElement(ldevice, "LN")
    ptrc.set("lnClass", "PTRC")
    ptrc.set("inst", "1")
    ptrc.set("lnType", "PTRC_Type")
    ptrc.set("desc", "母线保护")
    
    # 保护启动
    str_doi = ET.SubElement(ptrc, "DOI")
    str_doi.set("name", "Str")
    str_dai = ET.SubElement(str_doi, "DAI")
    str_dai.set("name", "general")
    str_val = ET.SubElement(str_dai, "Val")
    str_val.text = "false"
    
    # 保护动作
    op_doi = ET.SubElement(ptrc, "DOI")
    op_doi.set("name", "Op")
    op_dai = ET.SubElement(op_doi, "DAI")
    op_dai.set("name", "general")
    op_val = ET.SubElement(op_dai, "Val")
    op_val.text = "false"
    
    # GGIO - 通用输入输出
    ggio = ET.SubElement(ldevice, "LN")
    ggio.set("lnClass", "GGIO")
    ggio.set("inst", "1")
    ggio.set("lnType", "GGIO_Type")
    ggio.set("desc", "通用输入输出")
    
    # PT断线
    ind1_doi = ET.SubElement(ggio, "DOI")
    ind1_doi.set("name", "Ind1")
    ind1_dai = ET.SubElement(ind1_doi, "DAI")
    ind1_dai.set("name", "stVal")
    ind1_val = ET.SubElement(ind1_dai, "Val")
    ind1_val.text = "false"
    
    # 母线接地
    ind2_doi = ET.SubElement(ggio, "DOI")
    ind2_doi.set("name", "Ind2")
    ind2_dai = ET.SubElement(ind2_doi, "DAI")
    ind2_dai.set("name", "stVal")
    ind2_val = ET.SubElement(ind2_dai, "Val")
    ind2_val.text = "false"
    
    # MMXU - 测量
    mmxu = ET.SubElement(ldevice, "LN")
    mmxu.set("lnClass", "MMXU")
    mmxu.set("inst", "1")
    mmxu.set("lnType", "MMXU_Type")
    mmxu.set("desc", "母线测量")
    
    # A相电压
    ppv_doi = ET.SubElement(mmxu, "DOI")
    ppv_doi.set("name", "PPV")
    ppv_sdi_a = ET.SubElement(ppv_doi, "SDI")
    ppv_sdi_a.set("name", "phsA")
    ppv_dai_a = ET.SubElement(ppv_sdi_a, "DAI")
    ppv_dai_a.set("name", "cVal.mag.f")
    ppv_val_a = ET.SubElement(ppv_dai_a, "Val")
    ppv_val_a.text = "110000.0"
    
    # B相电压
    ppv_sdi_b = ET.SubElement(ppv_doi, "SDI")
    ppv_sdi_b.set("name", "phsB")
    ppv_dai_b = ET.SubElement(ppv_sdi_b, "DAI")
    ppv_dai_b.set("name", "cVal.mag.f")
    ppv_val_b = ET.SubElement(ppv_dai_b, "Val")
    ppv_val_b.text = "110000.0"
    
    # CSWI - 控制开关
    cswi = ET.SubElement(ldevice, "LN")
    cswi.set("lnClass", "CSWI")
    cswi.set("inst", "1")
    cswi.set("lnType", "CSWI_Type")
    cswi.set("desc", "母线分段控制")
    
    # 控制位置
    pos_doi = ET.SubElement(cswi, "DOI")
    pos_doi.set("name", "Pos")
    pos_dai = ET.SubElement(pos_doi, "DAI")
    pos_dai.set("name", "ctlVal")
    pos_val = ET.SubElement(pos_dai, "Val")
    pos_val.text = "false"

def save_scd_file(root):
    """保存SCD文件"""
    filename = f"scd_30points_{datetime.now().strftime('%Y%m%d_%H%M%S')}.scd"
    
    # 格式化XML
    ET.indent(root, space="  ", level=0)
    
    # 保存文件
    tree = ET.ElementTree(root)
    tree.write(filename, encoding='utf-8', xml_declaration=True)
    
    return filename

def main():
    """主函数"""
    print("🎯 生成30个数据点的SCD文件")
    print("=" * 60)
    print(f"🕐 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建SCD结构
    root = create_30_points_scd()
    
    # 添加IED定义
    add_ied_definitions(root)
    
    # 保存文件
    filename = save_scd_file(root)
    
    # 验证文件
    file_size = os.path.getsize(filename) / 1024
    
    print(f"\n✅ SCD文件生成完成!")
    print(f"📄 文件名: {filename}")
    print(f"📏 文件大小: {file_size:.1f}KB")
    print(f"🏭 IED设备: 3个")
    print(f"📊 数据点: 30个")
    
    print(f"\n💡 使用方法:")
    print(f"   1. 在对点机Web界面中选择'配置文件管理'")
    print(f"   2. 点击'选择文件'，选择: {filename}")
    print(f"   3. 点击'解析文件'")
    print(f"   4. 点击'转换为点表'")
    print(f"   5. 生成的点表可用于对点测试")
    
    return filename

if __name__ == "__main__":
    result = main()
    
    if result:
        print(f"\n🎉 成功生成30点位SCD文件: {result}")
    else:
        print(f"\n💥 SCD文件生成失败")
