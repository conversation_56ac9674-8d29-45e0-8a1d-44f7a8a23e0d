#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动对点机 - 测试版
简化启动过程，专注于测试宽松解析功能
"""

import sys
import os
from datetime import datetime

def start_simple_server():
    """启动简单的测试服务器"""
    print("🎯 启动对点机测试服务器")
    print("=" * 60)
    print(f"🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 尝试导入主程序
        print("📦 导入主程序模块...")
        import main_web_functional
        print("✅ 主程序模块导入成功")
        
        # 检查必要文件
        print("\n📁 检查测试文件...")
        test_files = [
            "220kVQFB_recovered.scd",
            "point_table_regex_20250705_142137.csv"
        ]
        
        for file in test_files:
            if os.path.exists(file):
                size = os.path.getsize(file) / 1024 / 1024
                print(f"   ✅ {file} ({size:.1f}MB)")
            else:
                print(f"   ❌ {file} (不存在)")
        
        print("\n🌐 准备启动Web服务...")
        print("💡 如果启动成功，请访问: http://localhost:8080")
        print("📋 测试步骤:")
        print("   1. 进入配置文件管理")
        print("   2. 使用同时加载功能")
        print("   3. 选择 220kVQFB_recovered.scd")
        print("   4. 选择 point_table_regex_20250705_142137.csv")
        print("   5. 点击同时加载")
        print("   6. 忽略XML警告，选择继续")
        
        print("\n🚀 启动中...")
        
        # 启动主程序
        if hasattr(main_web_functional, 'main'):
            main_web_functional.main()
        else:
            print("❌ 主程序没有main函数")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("💡 请检查依赖模块是否完整")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("💡 请检查程序配置")

def main():
    """主函数"""
    print("🎯 Auto_Point 对点机 - 测试启动器")
    print("=" * 60)
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"🐍 Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查工作目录
    current_dir = os.getcwd()
    print(f"📁 工作目录: {current_dir}")
    
    # 检查关键文件
    key_files = ["main_web_functional.py", "scd_converter.py", "logic.py"]
    print(f"\n📋 检查关键文件:")
    for file in key_files:
        if os.path.exists(file):
            print(f"   ✅ {file}")
        else:
            print(f"   ❌ {file}")
    
    # 启动服务器
    start_simple_server()

if __name__ == "__main__":
    main()
