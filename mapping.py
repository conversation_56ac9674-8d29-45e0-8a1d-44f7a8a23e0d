#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
点表映射模块
提供SCD信号与点表的自动映射功能
"""

def auto_map_points(scd_signals, point_table, scd_key_fields=None, point_key_fields=None):
    """
    自动映射SCD信号与点表数据
    
    Args:
        scd_signals: SCD解析出的信号列表
        point_table: 点表数据列表
        scd_key_fields: SCD信号的关键字段列表，默认['IED', 'SignalName']
        point_key_fields: 点表的关键字段列表，默认['IED', 'SignalName']
    
    Returns:
        映射后的点表数据列表
    """
    if not scd_signals or not point_table:
        return point_table
    
    scd_key_fields = scd_key_fields or ['IED', 'SignalName']
    point_key_fields = point_key_fields or ['IED', 'SignalName']
    
    # 创建SCD信号的索引
    scd_index = {}
    for signal in scd_signals:
        key = tuple(signal.get(field, '') for field in scd_key_fields)
        if key not in scd_index:
            scd_index[key] = []
        scd_index[key].append(signal)
    
    # 映射点表数据
    mapped_points = []
    for point in point_table:
        point_key = tuple(point.get(field, '') for field in point_key_fields)
        
        if point_key in scd_index:
            scd_matches = scd_index[point_key]
            if len(scd_matches) == 1:
                # 唯一匹配
                scd_signal = scd_matches[0]
                mapped_point = point.copy()
                mapped_point.update(scd_signal)
                mapped_point['match_status'] = '唯一匹配'
                mapped_points.append(mapped_point)
            else:
                # 多重匹配
                mapped_point = point.copy()
                mapped_point['match_status'] = f'多重匹配({len(scd_matches)}个)'
                mapped_points.append(mapped_point)
        else:
            # 无匹配
            mapped_point = point.copy()
            mapped_point['match_status'] = '无匹配'
            mapped_points.append(mapped_point)
    
    return mapped_points

def manual_map_points(scd_signals, point_table, mapping_rules):
    """
    手动映射SCD信号与点表数据
    
    Args:
        scd_signals: SCD解析出的信号列表
        point_table: 点表数据列表
        mapping_rules: 映射规则字典
    
    Returns:
        映射后的点表数据列表
    """
    # 创建SCD信号的索引
    scd_index = {}
    for signal in scd_signals:
        key = f"{signal.get('IED', '')}_{signal.get('SignalName', '')}"
        scd_index[key] = signal
    
    # 根据映射规则进行映射
    mapped_points = []
    for point in point_table:
        mapped_point = point.copy()
        
        # 应用映射规则
        for rule in mapping_rules:
            if rule['condition'](point):
                scd_key = rule['scd_key'](point)
                if scd_key in scd_index:
                    scd_signal = scd_index[scd_key]
                    mapped_point.update(scd_signal)
                    mapped_point['match_status'] = '手动映射'
                    break
        else:
            mapped_point['match_status'] = '无映射规则'
        
        mapped_points.append(mapped_point)
    
    return mapped_points

def validate_mapping(mapped_points):
    """
    验证映射结果
    
    Args:
        mapped_points: 映射后的点表数据
    
    Returns:
        验证结果字典
    """
    total = len(mapped_points)
    unique_match = sum(1 for p in mapped_points if p.get('match_status') == '唯一匹配')
    multiple_match = sum(1 for p in mapped_points if '多重匹配' in p.get('match_status', ''))
    no_match = sum(1 for p in mapped_points if p.get('match_status') in ['无匹配', '无映射规则'])
    
    return {
        'total': total,
        'unique_match': unique_match,
        'multiple_match': multiple_match,
        'no_match': no_match,
        'success_rate': unique_match / total if total > 0 else 0
    } 