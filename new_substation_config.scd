<?xml version="1.0" encoding="UTF-8"?>
<SCL xmlns="http://www.iec.ch/61850/2003/SCL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.iec.ch/61850/2003/SCL SCL.xsd">
  <Header id="NewSubstationSCD" version="1.0" revision="A" toolID="Auto_Point_Generator" nameStructure="IEDName">
    <Text>新编制变电站SCD配置文件</Text>
    <History>
      <Hitem version="1.0" revision="A" when="2025-07-04T14:55:00Z" who="Auto_Point_System" what="初始创建" why="对点测试需要"/>
    </History>
  </Header>
  
  <Substation name="新变电站" desc="220kV智能变电站">
    <VoltageLevel name="220kV母线" desc="220kV电压等级" nomFreq="50" numPhases="3">
      <Voltage unit="kV" multiplier="k">220</Voltage>
      <Bay name="220kV进线1" desc="220kV进线间隔1">
        <ConductingEquipment name="QF1" type="CBR" desc="220kV进线断路器1">
          <Terminal name="T1" connectivityNode="220kV母线/CN1" substationName="新变电站" voltageLevelName="220kV母线" bayName="220kV进线1"/>
        </ConductingEquipment>
        <ConductingEquipment name="QS1" type="DIS" desc="220kV进线隔离开关1">
          <Terminal name="T1" connectivityNode="220kV母线/CN2" substationName="新变电站" voltageLevelName="220kV母线" bayName="220kV进线1"/>
        </ConductingEquipment>
        <ConnectivityNode name="CN1" desc="连接节点1" pathName="新变电站/220kV母线/220kV进线1/CN1"/>
        <ConnectivityNode name="CN2" desc="连接节点2" pathName="新变电站/220kV母线/220kV进线1/CN2"/>
      </Bay>
      
      <Bay name="220kV进线2" desc="220kV进线间隔2">
        <ConductingEquipment name="QF2" type="CBR" desc="220kV进线断路器2">
          <Terminal name="T1" connectivityNode="220kV母线/CN3" substationName="新变电站" voltageLevelName="220kV母线" bayName="220kV进线2"/>
        </ConductingEquipment>
        <ConductingEquipment name="QS2" type="DIS" desc="220kV进线隔离开关2">
          <Terminal name="T1" connectivityNode="220kV母线/CN4" substationName="新变电站" voltageLevelName="220kV母线" bayName="220kV进线2"/>
        </ConductingEquipment>
        <ConnectivityNode name="CN3" desc="连接节点3" pathName="新变电站/220kV母线/220kV进线2/CN3"/>
        <ConnectivityNode name="CN4" desc="连接节点4" pathName="新变电站/220kV母线/220kV进线2/CN4"/>
      </Bay>
      
      <Bay name="主变间隔" desc="主变压器间隔">
        <ConductingEquipment name="QF_T1" type="CBR" desc="主变高压侧断路器">
          <Terminal name="T1" connectivityNode="220kV母线/CN5" substationName="新变电站" voltageLevelName="220kV母线" bayName="主变间隔"/>
        </ConductingEquipment>
        <ConductingEquipment name="T1" type="PTR" desc="主变压器">
          <Terminal name="T1" connectivityNode="220kV母线/CN5" substationName="新变电站" voltageLevelName="220kV母线" bayName="主变间隔"/>
          <Terminal name="T2" connectivityNode="110kV母线/CN1" substationName="新变电站" voltageLevelName="110kV母线" bayName="主变间隔"/>
        </ConductingEquipment>
        <ConnectivityNode name="CN5" desc="连接节点5" pathName="新变电站/220kV母线/主变间隔/CN5"/>
      </Bay>
    </VoltageLevel>
    
    <VoltageLevel name="110kV母线" desc="110kV电压等级" nomFreq="50" numPhases="3">
      <Voltage unit="kV" multiplier="k">110</Voltage>
      <Bay name="110kV出线1" desc="110kV出线间隔1">
        <ConductingEquipment name="QF3" type="CBR" desc="110kV出线断路器1">
          <Terminal name="T1" connectivityNode="110kV母线/CN2" substationName="新变电站" voltageLevelName="110kV母线" bayName="110kV出线1"/>
        </ConductingEquipment>
        <ConductingEquipment name="QS3" type="DIS" desc="110kV出线隔离开关1">
          <Terminal name="T1" connectivityNode="110kV母线/CN3" substationName="新变电站" voltageLevelName="110kV母线" bayName="110kV出线1"/>
        </ConductingEquipment>
        <ConnectivityNode name="CN2" desc="连接节点2" pathName="新变电站/110kV母线/110kV出线1/CN2"/>
        <ConnectivityNode name="CN3" desc="连接节点3" pathName="新变电站/110kV母线/110kV出线1/CN3"/>
      </Bay>
      
      <Bay name="110kV出线2" desc="110kV出线间隔2">
        <ConductingEquipment name="QF4" type="CBR" desc="110kV出线断路器2">
          <Terminal name="T1" connectivityNode="110kV母线/CN4" substationName="新变电站" voltageLevelName="110kV母线" bayName="110kV出线2"/>
        </ConductingEquipment>
        <ConductingEquipment name="QS4" type="DIS" desc="110kV出线隔离开关2">
          <Terminal name="T1" connectivityNode="110kV母线/CN5" substationName="新变电站" voltageLevelName="110kV母线" bayName="110kV出线2"/>
        </ConductingEquipment>
        <ConnectivityNode name="CN4" desc="连接节点4" pathName="新变电站/110kV母线/110kV出线2/CN4"/>
        <ConnectivityNode name="CN5" desc="连接节点5" pathName="新变电站/110kV母线/110kV出线2/CN5"/>
      </Bay>
      
      <Bay name="110kV出线3" desc="110kV出线间隔3">
        <ConductingEquipment name="QF5" type="CBR" desc="110kV出线断路器3">
          <Terminal name="T1" connectivityNode="110kV母线/CN6" substationName="新变电站" voltageLevelName="110kV母线" bayName="110kV出线3"/>
        </ConductingEquipment>
        <ConductingEquipment name="QS5" type="DIS" desc="110kV出线隔离开关3">
          <Terminal name="T1" connectivityNode="110kV母线/CN7" substationName="新变电站" voltageLevelName="110kV母线" bayName="110kV出线3"/>
        </ConductingEquipment>
        <ConnectivityNode name="CN6" desc="连接节点6" pathName="新变电站/110kV母线/110kV出线3/CN6"/>
        <ConnectivityNode name="CN7" desc="连接节点7" pathName="新变电站/110kV母线/110kV出线3/CN7"/>
      </Bay>
    </VoltageLevel>
    
    <VoltageLevel name="35kV母线" desc="35kV电压等级" nomFreq="50" numPhases="3">
      <Voltage unit="kV" multiplier="k">35</Voltage>
      <Bay name="35kV出线1" desc="35kV出线间隔1">
        <ConductingEquipment name="QF6" type="CBR" desc="35kV出线断路器1">
          <Terminal name="T1" connectivityNode="35kV母线/CN1" substationName="新变电站" voltageLevelName="35kV母线" bayName="35kV出线1"/>
        </ConductingEquipment>
        <ConnectivityNode name="CN1" desc="连接节点1" pathName="新变电站/35kV母线/35kV出线1/CN1"/>
      </Bay>
      
      <Bay name="35kV出线2" desc="35kV出线间隔2">
        <ConductingEquipment name="QF7" type="CBR" desc="35kV出线断路器2">
          <Terminal name="T1" connectivityNode="35kV母线/CN2" substationName="新变电站" voltageLevelName="35kV母线" bayName="35kV出线2"/>
        </ConductingEquipment>
        <ConnectivityNode name="CN2" desc="连接节点2" pathName="新变电站/35kV母线/35kV出线2/CN2"/>
      </Bay>
    </VoltageLevel>
  </Substation>
  
  <Communication>
    <SubNetwork name="站控层网络" type="8-MMS" desc="站控层通信网络">
      <ConnectedAP iedName="保护测控IED_001" apName="AP1">
        <Address>
          <P type="IP">***********01</P>
          <P type="IP-SUBNET">*************</P>
          <P type="IP-GATEWAY">***********</P>
          <P type="OSI-TSEL">0001</P>
          <P type="OSI-SSEL">0001</P>
          <P type="OSI-PSEL">00000001</P>
        </Address>
      </ConnectedAP>
      
      <ConnectedAP iedName="保护测控IED_002" apName="AP1">
        <Address>
          <P type="IP">***********02</P>
          <P type="IP-SUBNET">*************</P>
          <P type="IP-GATEWAY">***********</P>
          <P type="OSI-TSEL">0001</P>
          <P type="OSI-SSEL">0001</P>
          <P type="OSI-PSEL">00000001</P>
        </Address>
      </ConnectedAP>
      
      <ConnectedAP iedName="保护测控IED_003" apName="AP1">
        <Address>
          <P type="IP">***********03</P>
          <P type="IP-SUBNET">*************</P>
          <P type="IP-GATEWAY">***********</P>
          <P type="OSI-TSEL">0001</P>
          <P type="OSI-SSEL">0001</P>
          <P type="OSI-PSEL">00000001</P>
        </Address>
      </ConnectedAP>
    </SubNetwork>
  </Communication>

  <IED name="保护测控IED_001" type="保护测控装置" manufacturer="Auto_Point" configVersion="1.0" desc="220kV进线1保护测控装置">
    <AccessPoint name="AP1">
      <Server>
        <Authentication none="true"/>
        <LDevice inst="LD0" desc="逻辑设备">
          <LN0 lnClass="LLN0" inst="" lnType="LLN0_Type" desc="逻辑节点0">
            <DataSet name="DataSet1" desc="数据集1">
              <FCDA ldInst="LD0" lnClass="XCBR" lnInst="1" doName="Pos" daName="stVal" fc="ST"/>
              <FCDA ldInst="LD0" lnClass="XCBR" lnInst="1" doName="BlkOpn" daName="stVal" fc="ST"/>
              <FCDA ldInst="LD0" lnClass="XCBR" lnInst="1" doName="BlkCls" daName="stVal" fc="ST"/>
              <FCDA ldInst="LD0" lnClass="MMXU" lnInst="1" doName="TotW" daName="mag.f" fc="MX"/>
              <FCDA ldInst="LD0" lnClass="MMXU" lnInst="1" doName="TotVAr" daName="mag.f" fc="MX"/>
              <FCDA ldInst="LD0" lnClass="MMXU" lnInst="1" doName="Hz" daName="mag.f" fc="MX"/>
            </DataSet>
            <ReportControl name="Report1" datSet="DataSet1" rptID="Report1" confRev="1" buffered="true" bufTime="100">
              <TrgOps dchg="true" qchg="true" dupd="false" period="true"/>
              <OptFields seqNum="true" timeStamp="true" dataSet="true" reasonCode="true" dataRef="true" entryID="true" configRef="true"/>
              <RptEnabled max="5"/>
            </ReportControl>
          </LN0>

          <LN lnClass="XCBR" inst="1" lnType="XCBR_Type" desc="断路器1">
            <DOI name="Pos" desc="位置">
              <DAI name="stVal" desc="状态值">
                <Val>false</Val>
              </DAI>
              <DAI name="q" desc="质量">
                <Val>good</Val>
              </DAI>
            </DOI>
            <DOI name="BlkOpn" desc="分闸闭锁">
              <DAI name="stVal" desc="状态值">
                <Val>false</Val>
              </DAI>
            </DOI>
            <DOI name="BlkCls" desc="合闸闭锁">
              <DAI name="stVal" desc="状态值">
                <Val>false</Val>
              </DAI>
            </DOI>
          </LN>

          <LN lnClass="MMXU" inst="1" lnType="MMXU_Type" desc="测量单元1">
            <DOI name="TotW" desc="总有功功率">
              <DAI name="mag.f" desc="幅值">
                <Val>220.5</Val>
              </DAI>
              <DAI name="units.SIUnit" desc="单位">
                <Val>W</Val>
              </DAI>
              <DAI name="units.multiplier" desc="倍数">
                <Val>M</Val>
              </DAI>
            </DOI>
            <DOI name="TotVAr" desc="总无功功率">
              <DAI name="mag.f" desc="幅值">
                <Val>45.2</Val>
              </DAI>
            </DOI>
            <DOI name="Hz" desc="频率">
              <DAI name="mag.f" desc="幅值">
                <Val>50.0</Val>
              </DAI>
              <DAI name="units.SIUnit" desc="单位">
                <Val>Hz</Val>
              </DAI>
            </DOI>
            <DOI name="PPV" desc="相间电压">
              <SDI name="phsA">
                <DAI name="cVal.mag.f" desc="A相幅值">
                  <Val>220000.0</Val>
                </DAI>
              </SDI>
              <SDI name="phsB">
                <DAI name="cVal.mag.f" desc="B相幅值">
                  <Val>220000.0</Val>
                </DAI>
              </SDI>
              <SDI name="phsC">
                <DAI name="cVal.mag.f" desc="C相幅值">
                  <Val>220000.0</Val>
                </DAI>
              </SDI>
            </DOI>
          </LN>

          <LN lnClass="CSWI" inst="1" lnType="CSWI_Type" desc="控制开关1">
            <DOI name="Pos" desc="位置控制">
              <DAI name="ctlVal" desc="控制值">
                <Val>false</Val>
              </DAI>
            </DOI>
          </LN>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>

  <IED name="保护测控IED_002" type="保护测控装置" manufacturer="Auto_Point" configVersion="1.0" desc="220kV进线2保护测控装置">
    <AccessPoint name="AP1">
      <Server>
        <Authentication none="true"/>
        <LDevice inst="LD0" desc="逻辑设备">
          <LN0 lnClass="LLN0" inst="" lnType="LLN0_Type" desc="逻辑节点0">
            <DataSet name="DataSet1" desc="数据集1">
              <FCDA ldInst="LD0" lnClass="XCBR" lnInst="1" doName="Pos" daName="stVal" fc="ST"/>
              <FCDA ldInst="LD0" lnClass="MMXU" lnInst="1" doName="TotW" daName="mag.f" fc="MX"/>
              <FCDA ldInst="LD0" lnClass="MMXU" lnInst="1" doName="Hz" daName="mag.f" fc="MX"/>
            </DataSet>
          </LN0>

          <LN lnClass="XCBR" inst="1" lnType="XCBR_Type" desc="断路器2">
            <DOI name="Pos" desc="位置">
              <DAI name="stVal" desc="状态值">
                <Val>false</Val>
              </DAI>
            </DOI>
            <DOI name="BlkOpn" desc="分闸闭锁">
              <DAI name="stVal" desc="状态值">
                <Val>false</Val>
              </DAI>
            </DOI>
          </LN>

          <LN lnClass="MMXU" inst="1" lnType="MMXU_Type" desc="测量单元2">
            <DOI name="TotW" desc="总有功功率">
              <DAI name="mag.f" desc="幅值">
                <Val>180.3</Val>
              </DAI>
            </DOI>
            <DOI name="Hz" desc="频率">
              <DAI name="mag.f" desc="幅值">
                <Val>50.0</Val>
              </DAI>
            </DOI>
          </LN>

          <LN lnClass="CSWI" inst="1" lnType="CSWI_Type" desc="控制开关2">
            <DOI name="Pos" desc="位置控制">
              <DAI name="ctlVal" desc="控制值">
                <Val>false</Val>
              </DAI>
            </DOI>
          </LN>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>

  <IED name="保护测控IED_003" type="保护测控装置" manufacturer="Auto_Point" configVersion="1.0" desc="110kV出线1保护测控装置">
    <AccessPoint name="AP1">
      <Server>
        <Authentication none="true"/>
        <LDevice inst="LD0" desc="逻辑设备">
          <LN0 lnClass="LLN0" inst="" lnType="LLN0_Type" desc="逻辑节点0">
            <DataSet name="DataSet1" desc="数据集1">
              <FCDA ldInst="LD0" lnClass="XCBR" lnInst="1" doName="Pos" daName="stVal" fc="ST"/>
              <FCDA ldInst="LD0" lnClass="MMXU" lnInst="1" doName="TotW" daName="mag.f" fc="MX"/>
            </DataSet>
          </LN0>

          <LN lnClass="XCBR" inst="1" lnType="XCBR_Type" desc="断路器3">
            <DOI name="Pos" desc="位置">
              <DAI name="stVal" desc="状态值">
                <Val>true</Val>
              </DAI>
            </DOI>
          </LN>

          <LN lnClass="MMXU" inst="1" lnType="MMXU_Type" desc="测量单元3">
            <DOI name="TotW" desc="总有功功率">
              <DAI name="mag.f" desc="幅值">
                <Val>195.7</Val>
              </DAI>
            </DOI>
          </LN>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>

  <DataTypeTemplates>
    <LNodeType id="LLN0_Type" lnClass="LLN0" desc="逻辑节点0类型">
      <DO name="Mod" type="INC_Type" desc="模式"/>
      <DO name="Beh" type="INS_Type" desc="行为"/>
      <DO name="Health" type="INS_Type" desc="健康状态"/>
      <DO name="NamPlt" type="LPL_Type" desc="铭牌"/>
    </LNodeType>

    <LNodeType id="XCBR_Type" lnClass="XCBR" desc="断路器类型">
      <DO name="Pos" type="DPC_Type" desc="位置"/>
      <DO name="BlkOpn" type="SPS_Type" desc="分闸闭锁"/>
      <DO name="BlkCls" type="SPS_Type" desc="合闸闭锁"/>
      <DO name="OpCnt" type="INS_Type" desc="操作计数"/>
    </LNodeType>

    <LNodeType id="MMXU_Type" lnClass="MMXU" desc="测量单元类型">
      <DO name="TotW" type="MV_Type" desc="总有功功率"/>
      <DO name="TotVAr" type="MV_Type" desc="总无功功率"/>
      <DO name="Hz" type="MV_Type" desc="频率"/>
      <DO name="PPV" type="WYE_Type" desc="相间电压"/>
      <DO name="A" type="WYE_Type" desc="电流"/>
    </LNodeType>

    <LNodeType id="CSWI_Type" lnClass="CSWI" desc="控制开关类型">
      <DO name="Pos" type="DPC_Type" desc="位置"/>
      <DO name="OpOpn" type="ACT_Type" desc="分闸操作"/>
      <DO name="OpCls" type="ACT_Type" desc="合闸操作"/>
    </LNodeType>

    <DOType id="DPC_Type" cdc="DPC" desc="双点控制">
      <DA name="stVal" fc="ST" dchg="true" bType="Dbpos" desc="状态值"/>
      <DA name="q" fc="ST" qchg="true" bType="Quality" desc="质量"/>
      <DA name="t" fc="ST" bType="Timestamp" desc="时间戳"/>
      <DA name="ctlVal" fc="CO" bType="Dbpos" desc="控制值"/>
      <DA name="origin" fc="CO" bType="Struct" type="Originator_Type" desc="操作源"/>
      <DA name="ctlNum" fc="CO" bType="INT8U" desc="控制编号"/>
      <DA name="T" fc="CO" bType="Timestamp" desc="控制时间"/>
      <DA name="Test" fc="CO" bType="BOOLEAN" desc="测试"/>
      <DA name="Check" fc="CO" bType="Check" desc="检查"/>
    </DOType>

    <DOType id="SPS_Type" cdc="SPS" desc="单点状态">
      <DA name="stVal" fc="ST" dchg="true" bType="BOOLEAN" desc="状态值"/>
      <DA name="q" fc="ST" qchg="true" bType="Quality" desc="质量"/>
      <DA name="t" fc="ST" bType="Timestamp" desc="时间戳"/>
    </DOType>

    <DOType id="MV_Type" cdc="MV" desc="测量值">
      <DA name="mag" fc="MX" dchg="true" bType="Struct" type="AnalogueValue_Type" desc="幅值"/>
      <DA name="q" fc="MX" qchg="true" bType="Quality" desc="质量"/>
      <DA name="t" fc="MX" bType="Timestamp" desc="时间戳"/>
      <DA name="units" fc="CF" bType="Struct" type="Unit_Type" desc="单位"/>
      <DA name="db" fc="CF" bType="INT32U" desc="死区"/>
    </DOType>

    <DOType id="WYE_Type" cdc="WYE" desc="三相测量">
      <DA name="phsA" fc="MX" bType="Struct" type="CMV_Type" desc="A相"/>
      <DA name="phsB" fc="MX" bType="Struct" type="CMV_Type" desc="B相"/>
      <DA name="phsC" fc="MX" bType="Struct" type="CMV_Type" desc="C相"/>
      <DA name="neut" fc="MX" bType="Struct" type="CMV_Type" desc="中性点"/>
    </DOType>

    <DOType id="INS_Type" cdc="INS" desc="整数状态">
      <DA name="stVal" fc="ST" dchg="true" bType="INT32" desc="状态值"/>
      <DA name="q" fc="ST" qchg="true" bType="Quality" desc="质量"/>
      <DA name="t" fc="ST" bType="Timestamp" desc="时间戳"/>
    </DOType>

    <DOType id="INC_Type" cdc="INC" desc="整数控制">
      <DA name="stVal" fc="ST" dchg="true" bType="INT32" desc="状态值"/>
      <DA name="q" fc="ST" qchg="true" bType="Quality" desc="质量"/>
      <DA name="t" fc="ST" bType="Timestamp" desc="时间戳"/>
      <DA name="ctlVal" fc="CO" bType="INT32" desc="控制值"/>
    </DOType>

    <DOType id="ACT_Type" cdc="ACT" desc="活动">
      <DA name="general" fc="ST" dchg="true" bType="BOOLEAN" desc="通用"/>
      <DA name="q" fc="ST" qchg="true" bType="Quality" desc="质量"/>
      <DA name="t" fc="ST" bType="Timestamp" desc="时间戳"/>
    </DOType>

    <DOType id="LPL_Type" cdc="LPL" desc="铭牌">
      <DA name="vendor" fc="DC" bType="VisString255" desc="厂商"/>
      <DA name="swRev" fc="DC" bType="VisString255" desc="软件版本"/>
      <DA name="d" fc="DC" bType="VisString255" desc="描述"/>
    </DOType>

    <DAType id="AnalogueValue_Type">
      <BDA name="f" bType="FLOAT32" desc="浮点值"/>
    </DAType>

    <DAType id="CMV_Type">
      <BDA name="cVal" bType="Struct" type="Vector_Type" desc="复数值"/>
      <BDA name="q" bType="Quality" desc="质量"/>
      <BDA name="t" bType="Timestamp" desc="时间戳"/>
    </DAType>

    <DAType id="Vector_Type">
      <BDA name="mag" bType="Struct" type="AnalogueValue_Type" desc="幅值"/>
      <BDA name="ang" bType="Struct" type="AnalogueValue_Type" desc="角度"/>
    </DAType>

    <DAType id="Unit_Type">
      <BDA name="SIUnit" bType="Enum" type="SIUnitEnum" desc="SI单位"/>
      <BDA name="multiplier" bType="Enum" type="MultiplierEnum" desc="倍数"/>
    </DAType>

    <DAType id="Originator_Type">
      <BDA name="orCat" bType="Enum" type="OriginatorCategoryEnum" desc="操作类别"/>
      <BDA name="orIdent" bType="Octet64" desc="操作标识"/>
    </DAType>

    <EnumType id="SIUnitEnum">
      <EnumVal ord="1">none</EnumVal>
      <EnumVal ord="2">m</EnumVal>
      <EnumVal ord="3">kg</EnumVal>
      <EnumVal ord="4">s</EnumVal>
      <EnumVal ord="5">A</EnumVal>
      <EnumVal ord="23">deg</EnumVal>
      <EnumVal ord="27">Cel</EnumVal>
      <EnumVal ord="28">F</EnumVal>
      <EnumVal ord="33">V</EnumVal>
      <EnumVal ord="38">W</EnumVal>
      <EnumVal ord="42">VAr</EnumVal>
      <EnumVal ord="45">Hz</EnumVal>
    </EnumType>

    <EnumType id="MultiplierEnum">
      <EnumVal ord="-24">y</EnumVal>
      <EnumVal ord="-21">z</EnumVal>
      <EnumVal ord="-18">a</EnumVal>
      <EnumVal ord="-15">f</EnumVal>
      <EnumVal ord="-12">p</EnumVal>
      <EnumVal ord="-9">n</EnumVal>
      <EnumVal ord="-6">µ</EnumVal>
      <EnumVal ord="-3">m</EnumVal>
      <EnumVal ord="-2">c</EnumVal>
      <EnumVal ord="-1">d</EnumVal>
      <EnumVal ord="0"></EnumVal>
      <EnumVal ord="1">da</EnumVal>
      <EnumVal ord="2">h</EnumVal>
      <EnumVal ord="3">k</EnumVal>
      <EnumVal ord="6">M</EnumVal>
      <EnumVal ord="9">G</EnumVal>
      <EnumVal ord="12">T</EnumVal>
      <EnumVal ord="15">P</EnumVal>
      <EnumVal ord="18">E</EnumVal>
      <EnumVal ord="21">Z</EnumVal>
      <EnumVal ord="24">Y</EnumVal>
    </EnumType>

    <EnumType id="OriginatorCategoryEnum">
      <EnumVal ord="0">not-supported</EnumVal>
      <EnumVal ord="1">bay-control</EnumVal>
      <EnumVal ord="2">station-control</EnumVal>
      <EnumVal ord="3">remote-control</EnumVal>
      <EnumVal ord="4">automatic-bay</EnumVal>
      <EnumVal ord="5">automatic-station</EnumVal>
      <EnumVal ord="6">automatic-remote</EnumVal>
      <EnumVal ord="7">maintenance</EnumVal>
      <EnumVal ord="8">process</EnumVal>
    </EnumType>
  </DataTypeTemplates>
</SCL>
