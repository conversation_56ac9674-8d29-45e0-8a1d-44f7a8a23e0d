import xml.etree.ElementTree as ET
import os
import pandas as pd
import math

class SCDParser:
    def __init__(self, scd_path):
        if not os.path.exists(scd_path):
            raise FileNotFoundError(f"SCD文件未找到: {scd_path}")
        self.tree = ET.parse(scd_path)
        self.root = self.tree.getroot()

    def get_ieds(self):
        """提取IED设备列表"""
        ieds = []
        for ied in self.root.findall('.//IED'):
            name = ied.get('name')
            ieds.append(name)
        return ieds

    def get_signals(self):
        """提取信号点信息"""
        signals = []
        try:
            # 定义命名空间
            ns = {'scl': 'http://www.iec.ch/61850/2003/SCL'}

            # 查找所有IED设备
            ieds = self.root.findall('.//scl:IED', ns) if ns else self.root.findall('.//IED')

            for ied in ieds:
                ied_name = ied.get('name', 'Unknown')

                # 查找DataTypeTemplates中的LNodeType
                lnodetypes = self.root.findall('.//scl:LNodeType', ns) if ns else self.root.findall('.//LNodeType')

                for lntype in lnodetypes:
                    lntype_id = lntype.get('id', '')
                    lntype_class = lntype.get('lnClass', '')

                    # 查找数据对象
                    dos = lntype.findall('.//scl:DO', ns) if ns else lntype.findall('.//DO')

                    for do in dos:
                        do_name = do.get('name', '')
                        do_type = do.get('type', '')

                        # 查找对应的DOType
                        dotypes = self.root.findall(f'.//scl:DOType[@id="{do_type}"]', ns) if ns else self.root.findall(f'.//DOType[@id="{do_type}"]')

                        for dotype in dotypes:
                            # 查找数据属性
                            das = dotype.findall('.//scl:DA', ns) if ns else dotype.findall('.//DA')

                            for da in das:
                                da_name = da.get('name', '')
                                da_type = da.get('type', '')
                                da_fc = da.get('fc', '')

                                # 构造信号名称
                                signal_name = f"{ied_name}_{lntype_class}_{do_name}_{da_name}"

                                # 确定信号类型
                                signal_type = self._determine_signal_type(da_fc, da_type, da_name)

                                signals.append({
                                    'IED': ied_name,
                                    'SignalName': signal_name,
                                    'DataType': signal_type,
                                    'LNType': lntype_class,
                                    'DO': do_name,
                                    'DA': da_name,
                                    'FC': da_fc,
                                    'Type': da_type
                                })

        except Exception as e:
            print(f"SCD信号提取异常: {e}")

        return signals

    def _determine_signal_type(self, fc, da_type, da_name):
        """根据功能约束和数据类型确定信号类型"""
        # 遥测信号 (模拟量)
        if fc in ['MX', 'CF'] or da_type in ['FLOAT32', 'FLOAT64', 'INT32', 'INT16']:
            return '遥测'
        # 遥信信号 (状态量)
        elif fc in ['ST', 'MX'] and (da_type == 'BOOLEAN' or 'Pos' in da_name or 'Ind' in da_name):
            return '遥信'
        # 遥控信号 (控制量)
        elif fc in ['CO', 'CF'] or 'Oper' in da_name or 'Ctrl' in da_name:
            return '遥控'
        else:
            return '遥测'  # 默认为遥测

    def convert_to_point_table(self, output_path=None):
        """将SCD数据转换为点表格式"""
        signals = self.get_signals()

        if not signals:
            print("未找到信号数据")
            return None

        # 转换为DataFrame
        df = pd.DataFrame(signals)

        # 添加期望值列 (根据信号类型设置默认值)
        def get_default_value(signal_type):
            if signal_type == '遥信':
                return 'false'
            elif signal_type == '遥控':
                return 'false'
            else:  # 遥测
                return '0.0'

        df['ExpectedValue'] = df['DataType'].apply(get_default_value)
        df['Description'] = df['SignalName']
        df['Unit'] = df['DataType'].apply(lambda x: '状态' if x in ['遥信', '遥控'] else 'V')
        df['Quality'] = 'good'

        # 重新排列列顺序
        columns = ['SignalName', 'ExpectedValue', 'IED', 'DataType', 'Description', 'Unit', 'Quality']
        df = df[columns]

        if output_path:
            df.to_csv(output_path, index=False, encoding='utf-8-sig')
            print(f"点表已保存到: {output_path}")

        return df

    def validate_scd_consistency(self, point_table_path):
        """校核SCD文件与点表的一致性"""
        try:
            # 获取SCD中的信号
            scd_signals = self.get_signals()
            scd_signal_names = {sig['SignalName'] for sig in scd_signals}

            # 读取点表
            point_table = parse_point_table(point_table_path)
            point_signal_names = {pt.get('SignalName', '') for pt in point_table}

            # 比较分析
            only_in_scd = scd_signal_names - point_signal_names
            only_in_point_table = point_signal_names - scd_signal_names
            common_signals = scd_signal_names & point_signal_names

            validation_result = {
                'total_scd_signals': len(scd_signal_names),
                'total_point_table_signals': len(point_signal_names),
                'common_signals': len(common_signals),
                'only_in_scd': list(only_in_scd),
                'only_in_point_table': list(only_in_point_table),
                'consistency_rate': len(common_signals) / max(len(scd_signal_names), len(point_signal_names)) * 100
            }

            return validation_result

        except Exception as e:
            print(f"一致性校核异常: {e}")
            return None

def parse_point_table(file_path):
    """解析点表文件，支持CSV/Excel等格式，返回点表数据列表"""
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"点表文件未找到: {file_path}")
    ext = os.path.splitext(file_path)[-1].lower()
    if ext == '.csv':
        df = pd.read_csv(file_path)
    elif ext in ['.xls', '.xlsx']:
        df = pd.read_excel(file_path)
    else:
        raise ValueError("仅支持CSV或Excel格式的点表文件")
    records = df.to_dict(orient='records')
    def to_bool(x):
        if isinstance(x, bool):
            return x
        if isinstance(x, str):
            return x.strip().lower() == 'true' or x.strip() == '1'
        try:
            return bool(int(float(x)))
        except Exception:
            return False
    # 修正：遥信/遥控类型的点，期望值和实际值都转为布尔型
    for rec in records:
        point_type = str(rec.get('Type') or rec.get('DataType') or rec.get('类型', '')).strip()
        if point_type in ['遥信', '遥控']:
            rec['ExpectedValue'] = to_bool(rec.get('ExpectedValue', 0))
            if 'Value' in rec:
                rec['Value'] = to_bool(rec['Value'])
        # 修正quality字段
        if ('Quality' not in rec or rec['Quality'] is None or
            (isinstance(rec['Quality'], float) and math.isnan(rec['Quality'])) or
            str(rec['Quality']).strip() == ''):
            rec['Quality'] = 'good'
    return records 