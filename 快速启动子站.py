#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动子站模拟器
自动化启动和配置子站模拟器
"""

import os
import time
import socket
import subprocess
import threading
from datetime import datetime

def check_port_available(port):
    """检查端口是否可用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        return result != 0  # 端口不可连接说明可用
    except:
        return True

def wait_for_service(port, timeout=30):
    """等待服务启动"""
    print(f"   🔄 等待端口 {port} 服务启动...")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"   ✅ 端口 {port} 服务已启动")
                return True
        except:
            pass
        
        time.sleep(1)
        print("   ⏳ 等待中...")
    
    print(f"   ❌ 等待超时，端口 {port} 服务未启动")
    return False

def check_substation_status():
    """检查子站模拟器状态"""
    print("🔍 检查子站模拟器当前状态")
    print("-" * 50)
    
    # 检查文件
    if not os.path.exists('substation_optimized.py'):
        print("   ❌ 子站模拟器文件不存在")
        return False
    
    print("   ✅ 子站模拟器文件存在")
    
    # 检查端口
    ports = [102, 2404, 8080]
    active_ports = []
    
    for port in ports:
        if not check_port_available(port):
            active_ports.append(port)
            print(f"   ✅ 端口 {port} 已在使用")
        else:
            print(f"   ❌ 端口 {port} 未使用")
    
    return len(active_ports) > 0

def start_substation_gui():
    """启动子站模拟器GUI"""
    print("\n🚀 启动子站模拟器GUI")
    print("-" * 50)
    
    try:
        # 启动子站模拟器
        print("   🔄 正在启动子站模拟器...")
        
        # 使用subprocess启动GUI程序
        process = subprocess.Popen(
            ['python', 'substation_optimized.py'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )
        
        print(f"   ✅ 子站模拟器已启动 (PID: {process.pid})")
        print("   💡 请在GUI窗口中完成以下操作:")
        print("      1. 配置端口为 102")
        print("      2. 加载SCD文件: large_substation_2000points.scd")
        print("      3. 点击'启动服务'按钮")
        
        return process
    except Exception as e:
        print(f"   ❌ 启动失败: {e}")
        return None

def monitor_service_startup():
    """监控服务启动"""
    print("\n⏳ 监控服务启动状态")
    print("-" * 50)
    
    ports_to_monitor = [102, 2404, 8080]
    
    print("   💡 等待您在GUI中启动服务...")
    print("   🔄 监控端口状态 (按Ctrl+C停止监控):")
    
    try:
        while True:
            for port in ports_to_monitor:
                if not check_port_available(port):
                    print(f"   🎉 检测到端口 {port} 服务已启动!")
                    return port
            
            time.sleep(2)
            print("   ⏳ 继续等待服务启动...")
    except KeyboardInterrupt:
        print("\n   ⏹️ 停止监控")
        return None

def test_connection(port):
    """测试连接"""
    print(f"\n🔗 测试连接到端口 {port}")
    print("-" * 50)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex(('localhost', port))
        
        if result == 0:
            print(f"   ✅ 连接成功!")
            
            # 发送测试数据
            try:
                test_data = b"TEST_CONNECTION"
                sock.send(test_data)
                print(f"   ✅ 数据发送成功")
            except:
                print(f"   ℹ️ 连接正常，协议可能不匹配")
            
            sock.close()
            return True
        else:
            print(f"   ❌ 连接失败")
            sock.close()
            return False
    except Exception as e:
        print(f"   ❌ 连接异常: {e}")
        return False

def show_next_steps():
    """显示后续步骤"""
    print("\n📋 后续操作步骤")
    print("=" * 60)
    
    print("🎯 在Auto_Point Web界面中:")
    print("   1. 选择 '🔧 通信配置' → '网关配置'")
    print("   2. 配置连接参数:")
    print("      - 服务器地址: localhost")
    print("      - 端口: 102")
    print("      - 协议: IEC 61850")
    print("   3. 点击 '测试连接'")
    print("   4. 连接成功后可以开始对点测试")
    
    print("\n🎮 开始对点测试:")
    print("   1. 选择 '🎮 自动对点'")
    print("   2. 配置测试参数")
    print("   3. 点击 '开始自动对点'")
    print("   4. 观察测试结果")
    
    print("\n📊 生成对点报告:")
    print("   1. 测试完成后选择 '📋 报告管理'")
    print("   2. 填写报告信息")
    print("   3. 生成验收报告")

def main():
    """主函数"""
    print("🎯 Auto_Point 子站模拟器快速启动")
    print("=" * 80)
    print(f"🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 1. 检查当前状态
    if check_substation_status():
        print("\n✅ 子站模拟器服务已在运行")
        print("💡 如需重新启动，请先关闭现有服务")
        
        # 测试现有连接
        for port in [102, 2404, 8080]:
            if not check_port_available(port):
                if test_connection(port):
                    print(f"\n🎉 子站模拟器运行正常 (端口 {port})")
                    show_next_steps()
                    return True
        
        print("\n⚠️ 检测到端口占用但连接测试失败")
        print("💡 建议重新启动子站模拟器")
    
    # 2. 启动GUI
    process = start_substation_gui()
    if not process:
        print("\n❌ 子站模拟器启动失败")
        return False
    
    # 3. 等待用户在GUI中启动服务
    print("\n" + "="*60)
    print("⏳ 等待您在GUI中完成配置和启动服务")
    print("="*60)
    
    active_port = monitor_service_startup()
    
    if active_port:
        # 4. 测试连接
        if test_connection(active_port):
            print(f"\n🎉 子站模拟器启动成功!")
            print(f"📡 服务端口: {active_port}")
            print(f"🔗 连接地址: localhost:{active_port}")
            
            # 5. 显示后续步骤
            show_next_steps()
            
            return True
        else:
            print(f"\n⚠️ 服务已启动但连接测试失败")
            print(f"💡 请检查子站模拟器GUI中的配置")
            return False
    else:
        print(f"\n⏹️ 未检测到服务启动")
        print(f"💡 请手动在GUI中启动服务，然后运行:")
        print(f"   python 简单检查子站.py")
        return False

if __name__ == "__main__":
    try:
        success = main()
        
        if success:
            print(f"\n✅ 子站模拟器快速启动完成")
            print(f"🚀 现在可以在Auto_Point中进行对点测试")
        else:
            print(f"\n⚠️ 子站模拟器启动过程中遇到问题")
            print(f"📚 请参考 '子站启动指南.md' 进行手动配置")
    except KeyboardInterrupt:
        print(f"\n⏹️ 用户中断启动过程")
    except Exception as e:
        print(f"\n❌ 启动过程中发生错误: {e}")
        print(f"📚 请参考 '子站启动指南.md' 进行手动配置")
