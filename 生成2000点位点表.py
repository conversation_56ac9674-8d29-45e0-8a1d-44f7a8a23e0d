#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成包含2000个数据点的标准点表文件
用于Auto_Point Web风格对点机的大规模测试
"""

import csv
import random
from datetime import datetime

def generate_2000_points_table():
    """生成包含2000个数据点的点表"""
    
    print("🎯 生成2000个数据点的点表文件")
    print("=" * 60)
    
    # 点表文件名
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f"point_table_2000points_{timestamp}.csv"
    
    # 信号类型配置
    signal_types = {
        'DI': {'count': 800, 'start_addr': 1001, 'data_type': 'BOOL', 'description': '遥信'},
        'AI': {'count': 600, 'start_addr': 2001, 'data_type': 'FLOAT', 'description': '遥测'},
        'DO': {'count': 300, 'start_addr': 3001, 'data_type': 'BOOL', 'description': '遥控'},
        'AO': {'count': 300, 'start_addr': 4001, 'data_type': 'FLOAT', 'description': '遥调'}
    }
    
    # IED设备配置 (50个IED，每个40个点)
    ied_count = 50
    points_per_ied = 40
    
    # 逻辑节点类型
    ln_types = [
        'XCBR',  # 断路器
        'XSWI',  # 开关
        'MMXU',  # 测量单元
        'PTRC',  # 保护装置
        'CSWI',  # 控制开关
        'GGIO',  # 通用输入输出
        'TCTR',  # 电流互感器
        'TVTR',  # 电压互感器
        'YPTR',  # 保护装置
        'PDIS'   # 距离保护
    ]
    
    # 数据对象类型
    do_types = {
        'DI': ['Pos', 'Alm', 'Blk', 'Loc', 'OpCnt', 'Health', 'Mod', 'Beh'],
        'AI': ['TotW', 'TotVAr', 'Hz', 'PhV', 'A', 'TotPF', 'Tmp', 'Prs'],
        'DO': ['Pos', 'OpOpn', 'OpCls', 'Reset', 'BlkOpn', 'BlkCls'],
        'AO': ['TotW', 'TotVAr', 'PhV', 'A', 'SetPnt', 'CtlVal']
    }
    
    # 数据属性
    da_types = {
        'DI': ['stVal', 'q', 't'],
        'AI': ['mag', 'ang', 'q', 't'],
        'DO': ['ctlVal', 'origin', 'ctlNum', 'T'],
        'AO': ['setMag', 'origin', 'ctlNum', 'T']
    }
    
    points = []
    point_id = 1
    
    print(f"📊 开始生成数据点...")
    
    for signal_type, config in signal_types.items():
        count = config['count']
        start_addr = config['start_addr']
        data_type = config['data_type']
        type_desc = config['description']
        
        print(f"   🔄 生成{type_desc}信号: {count}个")
        
        current_addr = start_addr
        
        for i in range(count):
            # 计算IED编号和设备内编号
            ied_num = (i // points_per_ied) + 1
            device_point_num = (i % points_per_ied) + 1
            
            # 选择逻辑节点类型
            ln_type = ln_types[i % len(ln_types)]
            ln_inst = ((i % 10) // 2) + 1  # 1-5
            
            # 选择数据对象
            do_type = do_types[signal_type][i % len(do_types[signal_type])]
            
            # 选择数据属性
            da_type = da_types[signal_type][i % len(da_types[signal_type])]
            
            # 生成信号名称
            signal_name = f"IED_{ied_num:03d}_{ln_type}{ln_inst}_{do_type}_{da_type}"
            
            # 生成期望值
            if data_type == 'BOOL':
                expected_value = random.choice([0, 1])
            else:  # FLOAT
                if 'TotW' in do_type or 'TotVAr' in do_type:
                    expected_value = round(random.uniform(0, 1000), 2)
                elif 'Hz' in do_type:
                    expected_value = round(random.uniform(49.5, 50.5), 2)
                elif 'PhV' in do_type:
                    expected_value = round(random.uniform(220, 230), 1)
                elif 'A' in do_type:
                    expected_value = round(random.uniform(0, 100), 2)
                else:
                    expected_value = round(random.uniform(0, 100), 2)
            
            # 生成描述
            description = f"{type_desc}_{ied_num:03d}_{ln_type}{ln_inst}_{do_type}_{da_type}"
            
            # 生成SCD路径
            scd_path = f"IED_{ied_num:03d}.LD0.{ln_type}{ln_inst}.{do_type}.{da_type}"
            
            # 生成IED名称
            ied_name = f"IED_{ied_num:03d}"
            
            # 添加数据点
            point = {
                '点号': current_addr,
                '信号名称': signal_name,
                '信号类型': signal_type,
                '数据类型': data_type,
                '期望值': expected_value,
                '描述': description,
                'SCD路径': scd_path,
                'IED名称': ied_name
            }
            
            points.append(point)
            current_addr += 1
            point_id += 1
    
    # 写入CSV文件
    print(f"📝 写入点表文件: {filename}")
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        fieldnames = ['点号', '信号名称', '信号类型', '数据类型', '期望值', '描述', 'SCD路径', 'IED名称']
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        
        # 写入标题行
        writer.writeheader()
        
        # 写入数据行
        for point in points:
            writer.writerow(point)
    
    # 统计信息
    total_points = len(points)
    di_count = len([p for p in points if p['信号类型'] == 'DI'])
    ai_count = len([p for p in points if p['信号类型'] == 'AI'])
    do_count = len([p for p in points if p['信号类型'] == 'DO'])
    ao_count = len([p for p in points if p['信号类型'] == 'AO'])
    
    print(f"✅ 点表生成完成!")
    print(f"📊 统计信息:")
    print(f"   📄 文件名: {filename}")
    print(f"   📈 总数据点: {total_points}")
    print(f"   📊 遥信(DI): {di_count}个 ({di_count/total_points*100:.1f}%)")
    print(f"   📊 遥测(AI): {ai_count}个 ({ai_count/total_points*100:.1f}%)")
    print(f"   📊 遥控(DO): {do_count}个 ({do_count/total_points*100:.1f}%)")
    print(f"   📊 遥调(AO): {ao_count}个 ({ao_count/total_points*100:.1f}%)")
    print(f"   🏭 IED设备: {ied_count}个")
    print(f"   📋 每IED点数: {points_per_ied}个")
    
    return filename, total_points

def validate_point_table(filename):
    """验证生成的点表文件"""
    print(f"\n🔍 验证点表文件: {filename}")
    print("-" * 50)
    
    try:
        with open(filename, 'r', encoding='utf-8') as csvfile:
            reader = csv.DictReader(csvfile)
            rows = list(reader)
            
        total_rows = len(rows)
        
        # 验证数据完整性
        required_fields = ['点号', '信号名称', '信号类型', '数据类型', '期望值', '描述', 'SCD路径', 'IED名称']
        
        print(f"✅ 文件读取成功")
        print(f"📊 数据行数: {total_rows}")
        print(f"📋 字段数量: {len(reader.fieldnames)}")
        
        # 检查必需字段
        missing_fields = [field for field in required_fields if field not in reader.fieldnames]
        if missing_fields:
            print(f"❌ 缺少字段: {missing_fields}")
            return False
        else:
            print(f"✅ 所有必需字段存在")
        
        # 统计信号类型
        signal_types = {}
        for row in rows:
            signal_type = row['信号类型']
            signal_types[signal_type] = signal_types.get(signal_type, 0) + 1
        
        print(f"📊 信号类型分布:")
        for signal_type, count in signal_types.items():
            percentage = count / total_rows * 100
            print(f"   {signal_type}: {count}个 ({percentage:.1f}%)")
        
        # 检查点号唯一性
        point_numbers = [row['点号'] for row in rows]
        unique_points = set(point_numbers)
        
        if len(unique_points) == len(point_numbers):
            print(f"✅ 点号唯一性验证通过")
        else:
            print(f"❌ 发现重复点号: {len(point_numbers) - len(unique_points)}个")
        
        # 显示前5行和后5行
        print(f"\n📋 数据预览 (前5行):")
        for i, row in enumerate(rows[:5], 1):
            print(f"   {i}. {row['信号名称']} ({row['信号类型']}) - {row['描述']}")
        
        print(f"\n📋 数据预览 (后5行):")
        for i, row in enumerate(rows[-5:], len(rows)-4):
            print(f"   {i}. {row['信号名称']} ({row['信号类型']}) - {row['描述']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 2000个数据点点表生成器")
    print("=" * 70)
    print(f"🕐 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 目标: 生成包含2000个数据点的标准点表文件")
    print("=" * 70)
    
    try:
        # 生成点表
        filename, total_points = generate_2000_points_table()
        
        # 验证点表
        if validate_point_table(filename):
            print(f"\n🎉 2000个数据点点表生成成功!")
            print("=" * 70)
            print(f"📄 文件名: {filename}")
            print(f"📊 数据点数: {total_points}")
            print(f"✅ 文件验证通过")
            print(f"💡 可以在Auto_Point Web界面中导入此文件进行测试")
            
            return True
        else:
            print(f"\n❌ 点表验证失败")
            return False
            
    except Exception as e:
        print(f"\n❌ 生成过程中发生错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✅ 2000个数据点点表生成完成！")
        print(f"💡 建议在Auto_Point Web界面中测试此大规模点表的处理能力")
    else:
        print(f"\n❌ 点表生成失败")
        print(f"💡 请检查生成逻辑和文件权限")
