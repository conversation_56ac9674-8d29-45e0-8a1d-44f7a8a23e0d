#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的SCD转换器
验证原始转换器是否已经修复
"""

import os
from datetime import datetime
from scd_to_point_converter import SCDToPointConverter

def test_fixed_original_converter():
    """测试修复后的原始转换器"""
    print("🧪 测试修复后的原始SCD转换器")
    print("=" * 60)
    
    # 创建转换器实例
    converter = SCDToPointConverter()
    
    # 测试大型SCD文件
    scd_file = "large_substation_2000points.scd"
    
    if not os.path.exists(scd_file):
        print(f"❌ SCD文件不存在: {scd_file}")
        return False
    
    try:
        # 解析SCD文件
        print(f"🔄 开始解析SCD文件: {scd_file}")
        result = converter.parse_scd_file(scd_file)
        
        print(f"📊 解析结果:")
        print(f"   🏭 IED设备数量: {result.get('total_ieds', 0)}")
        print(f"   📋 信号点数量: {len(converter.signal_points)}")
        
        if len(converter.signal_points) > 0:
            # 转换为点表
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            output_file = f"point_table_original_fixed_{timestamp}.csv"
            
            print(f"🔄 开始转换为点表...")
            output_file = converter.convert_to_point_table('csv')

            print(f"✅ 点表转换成功!")
            print(f"📄 输出文件: {output_file}")
            print(f"📊 转换数据点: {len(converter.signal_points)}")

            # 显示信号类型分布
            signal_types = {}
            for point in converter.signal_points:
                signal_type = point.get('signal_type', 'Unknown')
                signal_types[signal_type] = signal_types.get(signal_type, 0) + 1

            print(f"📈 信号类型分布:")
            for signal_type, count in signal_types.items():
                print(f"   {signal_type}: {count}个")

            # 检查文件内容
            if os.path.exists(output_file):
                with open(output_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    print(f"📋 CSV文件行数: {len(lines)}行 (包含标题)")

                    if len(lines) > 1:
                        print(f"📝 前5行内容:")
                        for i, line in enumerate(lines[:5]):
                            print(f"   {i+1}: {line.strip()}")

            return True
        else:
            print(f"❌ 没有解析到任何信号点")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_results():
    """比较修复前后的结果"""
    print("\n📊 结果对比分析")
    print("=" * 60)
    
    # 检查修复版转换器的结果
    fixed_file = "point_table_fixed_20250704_133638.csv"
    if os.path.exists(fixed_file):
        with open(fixed_file, 'r', encoding='utf-8') as f:
            fixed_lines = len(f.readlines())
        print(f"🔧 修复版转换器结果: {fixed_lines-1}个数据点")
    
    # 检查原始转换器的结果
    original_files = [f for f in os.listdir('.') if f.startswith('point_table_original_fixed_')]
    if original_files:
        latest_file = max(original_files)
        with open(latest_file, 'r', encoding='utf-8') as f:
            original_lines = len(f.readlines())
        print(f"🔄 修复后原始转换器结果: {original_lines-1}个数据点")
        
        if fixed_lines == original_lines:
            print(f"✅ 结果一致! 修复成功!")
        else:
            print(f"⚠️ 结果不一致，需要进一步检查")
    else:
        print(f"❌ 没有找到原始转换器的输出文件")

def main():
    """主函数"""
    print("🎯 SCD转换器修复验证")
    print("=" * 80)
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 目标: 验证原始SCD转换器是否已经修复")
    print("=" * 80)
    
    # 测试修复后的原始转换器
    success = test_fixed_original_converter()
    
    if success:
        # 比较结果
        compare_results()
        
        print(f"\n🎉 测试总结")
        print("=" * 60)
        print(f"✅ 原始SCD转换器修复成功!")
        print(f"🎯 现在可以正确解析大型SCD文件")
        print(f"📊 能够提取所有2000个数据点")
        print(f"🔧 修复了命名空间解析问题")
        
        print(f"\n💡 使用建议:")
        print(f"   📋 在Auto_Point Web界面中使用SCD转换功能")
        print(f"   🔄 选择大型SCD文件进行转换")
        print(f"   📊 验证转换结果的数据点数量")
        print(f"   ✅ 确认信号类型分布正确")
        
    else:
        print(f"\n❌ 测试失败")
        print("💡 可能需要进一步调试转换器逻辑")

if __name__ == "__main__":
    main()
