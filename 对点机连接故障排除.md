# 对点机连接故障排除指南

## ✅ 问题已解决！

**子站模拟器服务现已正常运行**

### 📊 当前状态
```
✅ 子站服务状态: 正常运行中
✅ 监听端口: 102 (IEC 61850标准端口)
✅ 数据点数量: 1680个
✅ 连接测试: 成功 (收到130字节响应)
✅ 网络状态: 等待对点机连接
```

## 🔗 在Auto_Point中配置连接

### **步骤1: 打开通信配置**
1. 在Auto_Point Web界面中
2. 点击左侧导航 "🔧 通信配置"
3. 选择 "网关配置"

### **步骤2: 配置连接参数**
```
服务器地址: localhost
端口: 102
协议: IEC 61850
超时时间: 5000ms (默认)
重试次数: 3 (默认)
```

### **步骤3: 测试连接**
1. 点击 "测试连接" 按钮
2. 应该显示 "连接成功" 或类似信息
3. 如果成功，可以看到数据点信息

## 🚨 如果仍然连接失败

### **常见问题及解决方案**

#### **问题1: 连接超时**
**现象**: 提示连接超时或无响应
**解决方案**:
```
1. 确认子站服务正在运行:
   python 简单检查子站.py

2. 检查防火墙设置:
   - 允许Python程序通过防火墙
   - 或临时关闭防火墙测试

3. 尝试不同端口:
   - 端口102 (推荐)
   - 端口2404 (备选)
   - 端口8080 (备选)
```

#### **问题2: 地址无法访问**
**现象**: 提示地址不可达或拒绝连接
**解决方案**:
```
1. 确认地址配置:
   - 使用 localhost 或 127.0.0.1
   - 不要使用外部IP地址

2. 检查端口占用:
   - 确保端口102没有被其他程序占用
   - 重启子站服务如果需要
```

#### **问题3: 协议不匹配**
**现象**: 连接成功但数据交换失败
**解决方案**:
```
1. 确认协议设置:
   - 使用 IEC 61850 协议
   - 或尝试 DL/T 634.5104 协议

2. 检查数据格式:
   - 确认数据点格式正确
   - 验证信号类型匹配
```

## 🔧 高级故障排除

### **方法1: 重启所有服务**
```bash
# 1. 停止当前子站服务 (Ctrl+C)
# 2. 重新启动子站服务
python 自动启动子站服务.py

# 3. 等待服务启动完成
# 4. 在Auto_Point中重新测试连接
```

### **方法2: 使用不同端口**
如果端口102有问题，可以尝试其他端口：

1. **修改子站服务端口**:
   - 编辑自动启动脚本
   - 将端口改为2404或8080

2. **在Auto_Point中相应修改**:
   - 更新端口配置
   - 重新测试连接

### **方法3: 网络诊断**
```bash
# 检查端口状态
netstat -an | findstr :102

# 测试本地连接
telnet localhost 102

# 检查防火墙状态
netsh advfirewall show allprofiles
```

## 📋 连接成功后的操作

### **验证连接质量**
1. **数据点读取测试**:
   - 在Auto_Point中查看数据点列表
   - 确认能读取到1680个数据点
   - 验证数据类型和值正确

2. **通信稳定性测试**:
   - 多次点击"测试连接"
   - 确认每次都能成功连接
   - 观察响应时间是否稳定

### **开始对点测试**
1. **配置测试参数**:
   ```
   测试模式: 自动对点
   测试速度: 7级快速
   测试范围: 全部信号
   数据点数: 1680个
   ```

2. **启动测试**:
   - 点击 "开始自动对点"
   - 观察测试进度
   - 等待测试完成

3. **查看结果**:
   - 检查成功率 (预期95%以上)
   - 查看失败点详情
   - 生成对点报告

## 🎯 预期测试结果

### **正常连接状态**
```
🔗 连接状态: 已连接
📡 通信协议: IEC 61850
📊 数据点数: 1680个
⏱️ 响应时间: <100ms
✅ 连接质量: 优秀
```

### **测试性能指标**
```
📈 预期成功率: 95-98%
⏱️ 测试时间: 5-8分钟
📊 数据完整性: 100%
🔄 通信稳定性: 优秀
```

## 💡 使用建议

### **最佳实践**
1. **保持服务运行**: 测试期间不要关闭子站服务
2. **监控连接状态**: 观察Auto_Point中的连接指示
3. **记录测试结果**: 保存测试日志和报告
4. **定期验证**: 定期检查连接质量

### **性能优化**
1. **网络环境**: 使用本地连接减少延迟
2. **系统资源**: 确保足够的内存和CPU
3. **后台程序**: 关闭不必要的应用程序
4. **防病毒软件**: 临时关闭实时扫描

## 📞 技术支持

### **如果问题仍然存在**
1. **收集诊断信息**:
   ```bash
   python 简单检查子站.py > 诊断报告.txt
   ```

2. **检查错误日志**:
   - Auto_Point Web界面中的错误信息
   - 子站服务的控制台输出
   - Windows事件日志

3. **提供详细信息**:
   - 具体的错误提示
   - 操作步骤
   - 系统环境信息

---

**🎯 子站模拟器服务现已正常运行，端口102正在监听，可以在Auto_Point中配置连接并开始对点测试！**

*故障排除完成时间: 2025年7月4日 14:31*  
*服务状态: 正常运行*  
*连接地址: localhost:102*
