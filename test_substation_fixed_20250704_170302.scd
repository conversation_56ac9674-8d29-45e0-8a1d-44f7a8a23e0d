<?xml version="1.0" ?>
<SCL xmlns="http://www.iec.ch/61850/2003/SCL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2007" revision="B" release="4" xsi:schemaLocation="http://www.iec.ch/61850/2003/SCL SCL.xsd">
  <Header id="TestSubstation_220kV_SCD" version="1.0" revision="1" toolID="AutoPoint_SCD_Generator" nameStructure="IEDName">
    <History>
      <Hitem version="1.0" revision="1" when="2025-07-03T15:47:36.881971" who="AutoPoint System" what="Initial SCD creation"/>
    </History>
  </Header>
  <Substation name="TestSubstation_220kV" desc="TestSubstation_220kV Configuration">
    <VoltageLevel name="220kV" desc="220kV Voltage Level" nomFreq="50" numPhases="3">
      <Voltage unit="V" multiplier="k">220</Voltage>
      <Bay name="Bay1" desc="Main Transformer Bay"/>
    </VoltageLevel>
  </Substation>
  <IED name="TR1_CTRL" type="XCBR" manufacturer="TestManufacturer" configVersion="1.0" originalSclVersion="2007" originalSclRevision="B">
    <Services>
      <DynAssociation/>
      <SettingGroups/>
      <GetDirectory/>
      <GetDataObjectDefinition/>
      <DataObjectDirectory/>
      <GetDataSetValue/>
      <ReadWrite/>
    </Services>
    <AccessPoint name="AP1">
      <Server>
        <Authentication/>
        <LDevice inst="CTRL">
          <LN lnClass="XCBR" inst="1" lnType="XCBR_1"/>
          <LN lnClass="CSWI" inst="1" lnType="XCBR_1"/>
          <LN lnClass="MMXU" inst="1" lnType="XCBR_1"/>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  <IED name="LINE1_CTRL" type="XCBR" manufacturer="TestManufacturer" configVersion="1.0" originalSclVersion="2007" originalSclRevision="B">
    <Services>
      <DynAssociation/>
      <SettingGroups/>
      <GetDirectory/>
      <GetDataObjectDefinition/>
      <DataObjectDirectory/>
      <GetDataSetValue/>
      <ReadWrite/>
    </Services>
    <AccessPoint name="AP1">
      <Server>
        <Authentication/>
        <LDevice inst="CTRL">
          <LN lnClass="XCBR" inst="1" lnType="XCBR_1"/>
          <LN lnClass="CSWI" inst="1" lnType="XCBR_1"/>
          <LN lnClass="MMXU" inst="1" lnType="XCBR_1"/>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  <IED name="BUS1_CTRL" type="CSWI" manufacturer="TestManufacturer" configVersion="1.0" originalSclVersion="2007" originalSclRevision="B">
    <Services>
      <DynAssociation/>
      <SettingGroups/>
      <GetDirectory/>
      <GetDataObjectDefinition/>
      <DataObjectDirectory/>
      <GetDataSetValue/>
      <ReadWrite/>
    </Services>
    <AccessPoint name="AP1">
      <Server>
        <Authentication/>
        <LDevice inst="CTRL">
          <LN lnClass="XCBR" inst="1" lnType="XCBR_1"/>
          <LN lnClass="CSWI" inst="1" lnType="XCBR_1"/>
          <LN lnClass="MMXU" inst="1" lnType="XCBR_1"/>
        </LDevice>
      </Server>
    </AccessPoint>
  </IED>
  <DataTypeTemplates>
    <LNodeType id="XCBR_1" lnClass="XCBR">
      <DO name="Pos" type="DPC_1"/>
      <DO name="Beh" type="ENS_1"/>
      <DO name="Health" type="ENS_1"/>
    </LNodeType>
    <DOType id="DPC_1" cdc="DPC">
      <DA name="stVal" fc="ST" type="BOOLEAN"/>
      <DA name="q" fc="ST" type="Quality"/>
      <DA name="t" fc="ST" type="Timestamp"/>
      <DA name="ctlVal" fc="CO" type="BOOLEAN"/>
    </DOType>
    <DOType id="ENS_1" cdc="ENS">
      <DA name="stVal" fc="ST" type="INT32"/>
      <DA name="q" fc="ST" type="Quality"/>
      <DA name="t" fc="ST" type="Timestamp"/>
    </DOType>
  </DataTypeTemplates>
</SCL>