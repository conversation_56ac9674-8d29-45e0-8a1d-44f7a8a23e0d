#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大型SCD文件生成器 - 2000个数据点
为Auto_Point Web风格对点机生成大规模测试用SCD文件
"""

import xml.etree.ElementTree as ET
from datetime import datetime
import os

class LargeSCDGenerator:
    """大型SCD文件生成器"""

    def __init__(self):
        self.namespace = "http://www.iec.ch/61850/2003/SCL"
        self.total_points = 0

    def create_scd_root(self):
        """创建SCD文件根元素"""
        root = ET.Element("SCL")
        root.set("xmlns", self.namespace)
        root.set("version", "2007")
        root.set("revision", "B")
        root.set("release", "4")

        # 添加Header
        header = ET.SubElement(root, "Header")
        header.set("id", "Large_Substation_2000Points")
        header.set("version", "1.0")
        header.set("revision", "001")
        header.set("toolID", "Auto_Point_SCD_Generator")
        header.set("nameStructure", "IEDName")

        # 添加History
        history = ET.SubElement(header, "History")
        hitem = ET.SubElement(history, "Hitem")
        hitem.set("version", "1.0")
        hitem.set("revision", "001")
        hitem.set("when", datetime.now().isoformat())
        hitem.set("who", "Auto_Point_Generator")
        hitem.set("what", "Created large SCD file with 2000 data points for testing")

        return root

    def create_substation(self, root):
        """创建变电站结构"""
        substation = ET.SubElement(root, "Substation")
        substation.set("name", "Large_Test_Substation_2000")
        substation.set("desc", "大型测试变电站 - 2000个数据点")

        # 创建多个电压等级
        voltage_levels = [
            ("500kV", "500千伏电压等级"),
            ("220kV", "220千伏电压等级"),
            ("110kV", "110千伏电压等级"),
            ("35kV", "35千伏电压等级"),
            ("10kV", "10千伏电压等级")
        ]

        for vl_name, vl_desc in voltage_levels:
            voltage_level = ET.SubElement(substation, "VoltageLevel")
            voltage_level.set("name", vl_name)
            voltage_level.set("desc", vl_desc)
            voltage_level.set("nomFreq", "50")
            voltage_level.set("numPhases", "3")

            # 为每个电压等级创建多个间隔
            bay_count = self.get_bay_count_for_voltage(vl_name)
            for i in range(1, bay_count + 1):
                self.create_bay(voltage_level, vl_name, i)

        return substation

    def get_bay_count_for_voltage(self, voltage_level):
        """根据电压等级确定间隔数量"""
        bay_counts = {
            "500kV": 8,   # 8个间隔
            "220kV": 12,  # 12个间隔
            "110kV": 16,  # 16个间隔
            "35kV": 20,   # 20个间隔
            "10kV": 24    # 24个间隔
        }
        return bay_counts.get(voltage_level, 10)

    def create_bay(self, voltage_level, vl_name, bay_num):
        """创建间隔"""
        bay = ET.SubElement(voltage_level, "Bay")
        bay.set("name", f"{vl_name}_Bay_{bay_num:02d}")
        bay.set("desc", f"{vl_name}第{bay_num}间隔")

        # 为每个间隔创建设备
        equipment_types = [
            ("CBR", "断路器"),
            ("DIS", "隔离开关"),
            ("CTR", "电流互感器"),
            ("VTR", "电压互感器"),
            ("CAP", "电容器"),
            ("RES", "电阻器")
        ]

        for eq_type, eq_desc in equipment_types:
            equipment = ET.SubElement(bay, "ConductingEquipment")
            equipment.set("name", f"{bay.get('name')}_{eq_type}")
            equipment.set("type", eq_type)
            equipment.set("desc", f"{eq_desc}")

        return bay

    def create_communication_section(self, root):
        """创建通信配置段"""
        communication = ET.SubElement(root, "Communication")

        # 创建子网
        subnet = ET.SubElement(communication, "SubNetwork")
        subnet.set("name", "StationBus")
        subnet.set("desc", "站控层通信网络")
        subnet.set("type", "8-MMS")

        # 为每个IED创建连接访问点
        for i in range(1, 41):  # 40个IED
            self.create_connected_ap(subnet, f"IED_{i:03d}")

        return communication

    def create_connected_ap(self, subnet, ied_name):
        """创建连接访问点"""
        connected_ap = ET.SubElement(subnet, "ConnectedAP")
        connected_ap.set("iedName", ied_name)
        connected_ap.set("apName", "AP1")

        # 添加地址
        address = ET.SubElement(connected_ap, "Address")

        # IP地址
        p_ip = ET.SubElement(address, "P")
        p_ip.set("type", "IP")
        p_ip.text = f"192.168.1.{100 + int(ied_name.split('_')[1])}"

        # IP子网
        p_subnet = ET.SubElement(address, "P")
        p_subnet.set("type", "IP-SUBNET")
        p_subnet.text = "*************"

        # IP网关
        p_gateway = ET.SubElement(address, "P")
        p_gateway.set("type", "IP-GATEWAY")
        p_gateway.text = "***********"

        return connected_ap

    def create_ied_section(self, root):
        """创建IED配置段"""
        # 创建40个IED设备，每个IED包含50个数据点，总计2000个数据点
        target_points = 2000
        ied_count = 40
        points_per_ied = target_points // ied_count  # 50个点每IED

        for i in range(1, ied_count + 1):
            ied_name = f"IED_{i:03d}"
            self.create_ied(root, ied_name, i, points_per_ied)

        print(f"总共创建了 {self.total_points} 个数据点")

    def create_ied(self, root, ied_name, ied_index, target_points=50):
        """创建单个IED"""
        ied = ET.SubElement(root, "IED")
        ied.set("name", ied_name)
        ied.set("desc", f"智能电子设备{ied_index}")
        ied.set("type", "ProtectionIED")
        ied.set("manufacturer", "TestManufacturer")
        ied.set("configVersion", "1.0")
        ied.set("originalSclVersion", "2007")
        ied.set("originalSclRevision", "B")

        # 创建Services
        services = ET.SubElement(ied, "Services")

        # 动态关联
        dyn_assoc = ET.SubElement(services, "DynAssociation")

        # 获取目录
        get_dir = ET.SubElement(services, "GetDirectory")

        # 获取数据对象定义
        get_do_def = ET.SubElement(services, "GetDataObjectDefinition")

        # 数据访问
        data_access = ET.SubElement(services, "DataObjectDirectory")

        # 获取数据集
        get_data_set = ET.SubElement(services, "GetDataSetValue")

        # 设置数据集
        set_data_set = ET.SubElement(services, "SetDataSetValue")

        # 数据集目录
        data_set_dir = ET.SubElement(services, "DataSetDirectory")

        # 读取写入
        read_write = ET.SubElement(services, "ReadWrite")

        # 报告控制
        report_control = ET.SubElement(services, "ReportSettings")
        report_control.set("cbName", "Conf")
        report_control.set("datSet", "Conf")
        report_control.set("rptID", "Conf")
        report_control.set("optFields", "Conf")
        report_control.set("bufTime", "Conf")
        report_control.set("trgOps", "Conf")
        report_control.set("intgPd", "Conf")

        # 创建访问点
        access_point = ET.SubElement(ied, "AccessPoint")
        access_point.set("name", "AP1")
        access_point.set("desc", "访问点1")

        # 创建服务器
        server = ET.SubElement(access_point, "Server")
        server.set("desc", "IED服务器")

        # 创建认证
        authentication = ET.SubElement(server, "Authentication")
        authentication.set("none", "true")

        # 创建逻辑设备
        self.create_logical_device(server, ied_name, ied_index, target_points)

        return ied

    def create_logical_device(self, server, ied_name, ied_index, target_points=50):
        """创建逻辑设备"""
        ld = ET.SubElement(server, "LDevice")
        ld.set("inst", "LD0")
        ld.set("desc", f"{ied_name}逻辑设备")

        # 创建LN0 (逻辑节点0)
        self.create_ln0(ld, ied_name)

        # 创建多个功能逻辑节点，确保达到目标点数
        ln_configs = [
            ("XCBR", 3, 8),   # 3个断路器，每个8个数据点 = 24个点
            ("MMXU", 2, 8),   # 2个测量单元，每个8个数据点 = 16个点
            ("PTRC", 1, 6),   # 1个保护装置，6个数据点 = 6个点
            ("CSWI", 1, 4),   # 1个控制开关，4个数据点 = 4个点
        ]

        points_created = 0
        for ln_class, count, points_per_ln in ln_configs:
            for i in range(1, count + 1):
                points_created += self.create_functional_ln(ld, ied_name, ln_class, i, points_per_ln)

        # 用GGIO补充到目标点数
        remaining_points = target_points - points_created
        if remaining_points > 0:
            ggio_count = (remaining_points + 9) // 10  # 每个GGIO 10个点
            for i in range(1, ggio_count + 1):
                points_to_create = min(10, remaining_points)
                if points_to_create > 0:
                    points_created += self.create_ggio_ln(ld, ied_name, i, points_to_create)
                    remaining_points -= points_to_create
                if remaining_points <= 0:
                    break

        self.total_points += points_created
        return ld

    def create_ln0(self, ld, ied_name):
        """创建LN0逻辑节点"""
        ln0 = ET.SubElement(ld, "LN0")
        ln0.set("lnClass", "LLN0")
        ln0.set("inst", "")
        ln0.set("lnType", "LLN0_Type")
        ln0.set("desc", f"{ied_name}逻辑节点0")

        return ln0

    def create_functional_ln(self, ld, ied_name, ln_class, inst, points_count):
        """创建功能逻辑节点"""
        ln = ET.SubElement(ld, "LN")
        ln.set("lnClass", ln_class)
        ln.set("inst", str(inst))
        ln.set("lnType", f"{ln_class}_Type")
        ln.set("desc", f"{ied_name}_{ln_class}{inst}")

        # 根据逻辑节点类型创建相应的数据对象
        points_created = 0

        if ln_class == "XCBR":  # 断路器
            points_created += self.create_xcbr_data_objects(ln, points_count)
        elif ln_class == "MMXU":  # 测量单元
            points_created += self.create_mmxu_data_objects(ln, points_count)
        elif ln_class == "PTRC":  # 保护装置
            points_created += self.create_ptrc_data_objects(ln, points_count)
        elif ln_class == "CSWI":  # 控制开关
            points_created += self.create_cswi_data_objects(ln, points_count)

        return points_created

    def create_ggio_ln(self, ld, ied_name, inst, points_count):
        """创建通用输入输出逻辑节点"""
        ln = ET.SubElement(ld, "LN")
        ln.set("lnClass", "GGIO")
        ln.set("inst", str(inst))
        ln.set("lnType", "GGIO_Type")
        ln.set("desc", f"{ied_name}_GGIO{inst}")

        points_created = 0

        # 创建单点状态
        for i in range(1, min(points_count + 1, 6)):
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", f"Ind{i}")
            doi.set("desc", f"指示{i}")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "stVal")
            dai.set("desc", "状态值")

            val = ET.SubElement(dai, "Val")
            val.text = "0"

            points_created += 1

        # 创建单点控制
        for i in range(1, min(points_count - points_created + 1, 5)):
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", f"SPCSO{i}")
            doi.set("desc", f"单点控制{i}")

            # 状态值
            dai_st = ET.SubElement(doi, "DAI")
            dai_st.set("name", "stVal")
            dai_st.set("desc", "状态值")

            val_st = ET.SubElement(dai_st, "Val")
            val_st.text = "0"

            # 控制值
            dai_ctl = ET.SubElement(doi, "DAI")
            dai_ctl.set("name", "ctlVal")
            dai_ctl.set("desc", "控制值")

            val_ctl = ET.SubElement(dai_ctl, "Val")
            val_ctl.text = "0"

            points_created += 2
            if points_created >= points_count:
                break

        return min(points_created, points_count)

    def create_xcbr_data_objects(self, ln, points_count):
        """创建断路器数据对象"""
        points_created = 0

        # 位置信息
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "Pos")
            doi.set("desc", "位置")

            # 状态值
            dai_st = ET.SubElement(doi, "DAI")
            dai_st.set("name", "stVal")
            val_st = ET.SubElement(dai_st, "Val")
            val_st.text = "0"
            points_created += 1

            # 控制值
            if points_created < points_count:
                dai_ctl = ET.SubElement(doi, "DAI")
                dai_ctl.set("name", "ctlVal")
                val_ctl = ET.SubElement(dai_ctl, "Val")
                val_ctl.text = "0"
                points_created += 1

        # 分闸闭锁
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "BlkOpn")
            doi.set("desc", "分闸闭锁")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "stVal")
            val = ET.SubElement(dai, "Val")
            val.text = "0"
            points_created += 1

        # 合闸闭锁
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "BlkCls")
            doi.set("desc", "合闸闭锁")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "stVal")
            val = ET.SubElement(dai, "Val")
            val.text = "0"
            points_created += 1

        # 就地/远方
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "Loc")
            doi.set("desc", "就地远方")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "stVal")
            val = ET.SubElement(dai, "Val")
            val.text = "0"
            points_created += 1

        # 操作计数
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "OpCnt")
            doi.set("desc", "操作计数")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "stVal")
            val = ET.SubElement(dai, "Val")
            val.text = "0"
            points_created += 1

        # 故障指示
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "Alm")
            doi.set("desc", "告警")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "stVal")
            val = ET.SubElement(dai, "Val")
            val.text = "0"
            points_created += 1

        # 弹簧储能
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "CBOpCap")
            doi.set("desc", "弹簧储能")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "stVal")
            val = ET.SubElement(dai, "Val")
            val.text = "1"
            points_created += 1

        return points_created

    def create_mmxu_data_objects(self, ln, points_count):
        """创建测量单元数据对象"""
        points_created = 0

        # 有功功率
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "TotW")
            doi.set("desc", "有功功率")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "mag")
            dai.set("desc", "幅值")
            val = ET.SubElement(dai, "Val")
            val.text = "0.0"
            points_created += 1

        # 无功功率
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "TotVAr")
            doi.set("desc", "无功功率")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "mag")
            val = ET.SubElement(dai, "Val")
            val.text = "0.0"
            points_created += 1

        # 频率
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "Hz")
            doi.set("desc", "频率")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "mag")
            val = ET.SubElement(dai, "Val")
            val.text = "50.0"
            points_created += 1

        # A相电压
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "PhV")
            doi.set("desc", "相电压")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "phsA")
            val = ET.SubElement(dai, "Val")
            val.text = "220000.0"
            points_created += 1

        # B相电压
        if points_created < points_count:
            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "phsB")
            val = ET.SubElement(dai, "Val")
            val.text = "220000.0"
            points_created += 1

        # C相电压
        if points_created < points_count:
            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "phsC")
            val = ET.SubElement(dai, "Val")
            val.text = "220000.0"
            points_created += 1

        # A相电流
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "A")
            doi.set("desc", "电流")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "phsA")
            val = ET.SubElement(dai, "Val")
            val.text = "1000.0"
            points_created += 1

        # 功率因数
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "TotPF")
            doi.set("desc", "功率因数")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "mag")
            val = ET.SubElement(dai, "Val")
            val.text = "0.95"
            points_created += 1

        return points_created

    def create_ptrc_data_objects(self, ln, points_count):
        """创建保护装置数据对象"""
        points_created = 0

        # 保护启动
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "Str")
            doi.set("desc", "启动")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "general")
            val = ET.SubElement(dai, "Val")
            val.text = "0"
            points_created += 1

        # 保护动作
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "Op")
            doi.set("desc", "动作")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "general")
            val = ET.SubElement(dai, "Val")
            val.text = "0"
            points_created += 1

        # 跳闸
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "Tr")
            doi.set("desc", "跳闸")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "general")
            val = ET.SubElement(dai, "Val")
            val.text = "0"
            points_created += 1

        # 保护投退
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "Mod")
            doi.set("desc", "模式")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "stVal")
            val = ET.SubElement(dai, "Val")
            val.text = "1"
            points_created += 1

        # 保护闭锁
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "Blk")
            doi.set("desc", "闭锁")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "stVal")
            val = ET.SubElement(dai, "Val")
            val.text = "0"
            points_created += 1

        # 告警
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "Alm")
            doi.set("desc", "告警")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "stVal")
            val = ET.SubElement(dai, "Val")
            val.text = "0"
            points_created += 1

        return points_created

    def create_cswi_data_objects(self, ln, points_count):
        """创建控制开关数据对象"""
        points_created = 0

        # 位置
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "Pos")
            doi.set("desc", "位置")

            # 状态值
            dai_st = ET.SubElement(doi, "DAI")
            dai_st.set("name", "stVal")
            val_st = ET.SubElement(dai_st, "Val")
            val_st.text = "0"
            points_created += 1

            # 控制值
            if points_created < points_count:
                dai_ctl = ET.SubElement(doi, "DAI")
                dai_ctl.set("name", "ctlVal")
                val_ctl = ET.SubElement(dai_ctl, "Val")
                val_ctl.text = "0"
                points_created += 1

        # 就地/远方
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "Loc")
            doi.set("desc", "就地远方")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "stVal")
            val = ET.SubElement(dai, "Val")
            val.text = "0"
            points_created += 1

        # 操作模式
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "Mod")
            doi.set("desc", "操作模式")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "stVal")
            val = ET.SubElement(dai, "Val")
            val.text = "1"
            points_created += 1

        # 健康状态
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "Health")
            doi.set("desc", "健康状态")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "stVal")
            val = ET.SubElement(dai, "Val")
            val.text = "1"
            points_created += 1

        # 行为模式
        if points_created < points_count:
            doi = ET.SubElement(ln, "DOI")
            doi.set("name", "Beh")
            doi.set("desc", "行为模式")

            dai = ET.SubElement(doi, "DAI")
            dai.set("name", "stVal")
            val = ET.SubElement(dai, "Val")
            val.text = "1"
            points_created += 1

        return points_created

    def create_data_type_templates(self, root):
        """创建数据类型模板"""
        data_type_templates = ET.SubElement(root, "DataTypeTemplates")

        # 创建逻辑节点类型模板
        self.create_lnode_type_templates(data_type_templates)

        # 创建数据对象类型模板
        self.create_do_type_templates(data_type_templates)

        # 创建数据属性类型模板
        self.create_da_type_templates(data_type_templates)

        # 创建枚举类型模板
        self.create_enum_type_templates(data_type_templates)

        return data_type_templates

    def create_lnode_type_templates(self, data_type_templates):
        """创建逻辑节点类型模板"""
        # LLN0类型
        lln0_type = ET.SubElement(data_type_templates, "LNodeType")
        lln0_type.set("id", "LLN0_Type")
        lln0_type.set("lnClass", "LLN0")
        lln0_type.set("desc", "逻辑节点0类型")

        # XCBR类型
        xcbr_type = ET.SubElement(data_type_templates, "LNodeType")
        xcbr_type.set("id", "XCBR_Type")
        xcbr_type.set("lnClass", "XCBR")
        xcbr_type.set("desc", "断路器类型")

        # MMXU类型
        mmxu_type = ET.SubElement(data_type_templates, "LNodeType")
        mmxu_type.set("id", "MMXU_Type")
        mmxu_type.set("lnClass", "MMXU")
        mmxu_type.set("desc", "测量单元类型")

        # PTRC类型
        ptrc_type = ET.SubElement(data_type_templates, "LNodeType")
        ptrc_type.set("id", "PTRC_Type")
        ptrc_type.set("lnClass", "PTRC")
        ptrc_type.set("desc", "保护类型")

        # CSWI类型
        cswi_type = ET.SubElement(data_type_templates, "LNodeType")
        cswi_type.set("id", "CSWI_Type")
        cswi_type.set("lnClass", "CSWI")
        cswi_type.set("desc", "控制开关类型")

        # GGIO类型
        ggio_type = ET.SubElement(data_type_templates, "LNodeType")
        ggio_type.set("id", "GGIO_Type")
        ggio_type.set("lnClass", "GGIO")
        ggio_type.set("desc", "通用输入输出类型")

    def create_do_type_templates(self, data_type_templates):
        """创建数据对象类型模板"""
        # SPS类型 (单点状态)
        sps_type = ET.SubElement(data_type_templates, "DOType")
        sps_type.set("id", "SPS_Type")
        sps_type.set("cdc", "SPS")
        sps_type.set("desc", "单点状态类型")

        # DPC类型 (双点控制)
        dpc_type = ET.SubElement(data_type_templates, "DOType")
        dpc_type.set("id", "DPC_Type")
        dpc_type.set("cdc", "DPC")
        dpc_type.set("desc", "双点控制类型")

        # MV类型 (测量值)
        mv_type = ET.SubElement(data_type_templates, "DOType")
        mv_type.set("id", "MV_Type")
        mv_type.set("cdc", "MV")
        mv_type.set("desc", "测量值类型")

        # CMV类型 (复数测量值)
        cmv_type = ET.SubElement(data_type_templates, "DOType")
        cmv_type.set("id", "CMV_Type")
        cmv_type.set("cdc", "CMV")
        cmv_type.set("desc", "复数测量值类型")

    def create_da_type_templates(self, data_type_templates):
        """创建数据属性类型模板"""
        # Vector类型
        vector_type = ET.SubElement(data_type_templates, "DAType")
        vector_type.set("id", "Vector_Type")
        vector_type.set("desc", "向量类型")

        # AnalogueValue类型
        analogue_type = ET.SubElement(data_type_templates, "DAType")
        analogue_type.set("id", "AnalogueValue_Type")
        analogue_type.set("desc", "模拟量类型")

    def create_enum_type_templates(self, data_type_templates):
        """创建枚举类型模板"""
        # Beh枚举
        beh_enum = ET.SubElement(data_type_templates, "EnumType")
        beh_enum.set("id", "Beh_Enum")

        # 添加枚举值
        enum_values = [
            ("1", "on"),
            ("2", "blocked"),
            ("3", "test"),
            ("4", "test/blocked"),
            ("5", "off")
        ]

        for ord_val, val in enum_values:
            enum_val = ET.SubElement(beh_enum, "EnumVal")
            enum_val.set("ord", ord_val)
            enum_val.text = val

    def generate_scd_file(self, filename="large_substation_2000points.scd"):
        """生成SCD文件"""
        print("🔄 开始生成大型SCD文件...")
        print(f"📋 目标: 2000个数据点")
        print(f"📄 文件名: {filename}")
        print("=" * 60)

        # 创建根元素
        root = self.create_scd_root()
        print("✅ 创建SCD根元素")

        # 创建变电站结构
        substation = self.create_substation(root)
        print("✅ 创建变电站结构")

        # 创建通信配置
        communication = self.create_communication_section(root)
        print("✅ 创建通信配置")

        # 创建IED配置 (核心部分)
        print("🔄 创建IED配置和数据点...")
        self.create_ied_section(root)
        print(f"✅ 创建40个IED设备，总计 {self.total_points} 个数据点")

        # 创建数据类型模板
        data_type_templates = self.create_data_type_templates(root)
        print("✅ 创建数据类型模板")

        # 格式化XML
        self.indent_xml(root)

        # 创建XML树并写入文件
        tree = ET.ElementTree(root)

        # 写入文件
        with open(filename, 'wb') as f:
            tree.write(f, encoding='utf-8', xml_declaration=True)

        # 获取文件信息
        file_size = os.path.getsize(filename)

        print("=" * 60)
        print("🎉 SCD文件生成完成！")
        print(f"📄 文件名: {filename}")
        print(f"📊 文件大小: {file_size:,} bytes ({file_size/1024:.1f} KB)")
        print(f"📊 IED数量: 40个")
        print(f"📊 数据点数: {self.total_points}个")
        print(f"📊 平均每IED: {self.total_points//40}个数据点")

        return filename, file_size, self.total_points

    def indent_xml(self, elem, level=0):
        """格式化XML缩进"""
        i = "\n" + level * "  "
        if len(elem):
            if not elem.text or not elem.text.strip():
                elem.text = i + "  "
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
            for elem in elem:
                self.indent_xml(elem, level + 1)
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
        else:
            if level and (not elem.tail or not elem.tail.strip()):
                elem.tail = i

def main():
    """主函数"""
    print("🏭 大型SCD文件生成器 - 2000个数据点")
    print("=" * 60)
    print(f"🕐 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 目标: 生成包含2000个数据点的SCD文件")
    print("=" * 60)

    try:
        # 创建生成器
        generator = LargeSCDGenerator()

        # 生成SCD文件
        filename, file_size, total_points = generator.generate_scd_file()

        # 验证生成结果
        print(f"\n📋 生成结果验证:")
        print(f"   ✅ 文件存在: {os.path.exists(filename)}")
        print(f"   ✅ 文件大小: {file_size:,} bytes")
        print(f"   ✅ 数据点数: {total_points}")

        if total_points >= 2000:
            print(f"   🎯 目标达成: 超过2000个数据点")
        else:
            print(f"   ⚠️ 目标未达成: 仅{total_points}个数据点")

        print(f"\n💡 使用建议:")
        print(f"   1. 在Auto_Point中选择此文件进行解析测试")
        print(f"   2. 验证大规模数据的转换性能")
        print(f"   3. 测试系统在高负载下的稳定性")

        print(f"\n🎉 大型SCD文件生成成功！")

    except Exception as e:
        print(f"\n❌ 生成失败: {str(e)}")
        return False

    return True

if __name__ == "__main__":
    success = main()
    if success:
        print(f"\n✅ 程序执行成功")
    else:
        print(f"\n❌ 程序执行失败")