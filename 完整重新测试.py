#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Auto_Point 完整重新测试
全面验证系统所有功能是否正常工作
"""

import os
import sys
import time
import subprocess
from datetime import datetime

def print_header(title):
    """打印测试标题"""
    print(f"\n{'='*80}")
    print(f"🧪 {title}")
    print(f"{'='*80}")

def print_section(title):
    """打印测试章节"""
    print(f"\n🔹 {title}")
    print(f"{'-'*60}")

def test_environment():
    """测试环境检查"""
    print_section("环境检查")
    
    results = {}
    
    # 检查Python版本
    python_version = sys.version.split()[0]
    print(f"   🐍 Python版本: {python_version}")
    results['Python版本'] = True
    
    # 检查工作目录
    current_dir = os.getcwd()
    print(f"   📁 工作目录: {current_dir}")
    results['工作目录'] = 'auto_point' in current_dir.lower()
    
    # 检查核心文件
    core_files = [
        'main_web_functional.py',
        'scd_to_point_converter.py', 
        'report_generator.py',
        'large_substation_2000points.scd'
    ]
    
    missing_files = []
    for file in core_files:
        if os.path.exists(file):
            size = os.path.getsize(file) / 1024
            print(f"   ✅ {file} ({size:.1f}KB)")
        else:
            missing_files.append(file)
            print(f"   ❌ {file} - 缺失")
    
    results['核心文件'] = len(missing_files) == 0
    
    return results

def test_scd_converter_standalone():
    """独立测试SCD转换器"""
    print_section("SCD转换器独立测试")
    
    try:
        from scd_to_point_converter import SCDToPointConverter
        
        # 测试转换器创建
        print("   🔄 创建SCD转换器实例...")
        converter = SCDToPointConverter()
        print("   ✅ 转换器创建成功")
        
        # 测试SCD文件解析
        scd_file = "large_substation_2000points.scd"
        if os.path.exists(scd_file):
            print(f"   🔄 解析SCD文件: {scd_file}")
            result = converter.parse_scd_file(scd_file)
            
            if result:
                points_count = len(converter.signal_points)
                print(f"   ✅ 解析成功: {points_count}个数据点")
                
                if points_count >= 1800:
                    print("   ✅ 数据点数量正常")
                    
                    # 测试转换为点表
                    print("   🔄 转换为点表...")
                    csv_file = converter.convert_to_point_table('csv')
                    
                    if os.path.exists(csv_file):
                        print(f"   ✅ 点表生成成功: {csv_file}")
                        
                        # 验证CSV文件内容
                        with open(csv_file, 'r', encoding='utf-8') as f:
                            lines = f.readlines()
                            csv_points = len(lines) - 1
                        
                        print(f"   📊 CSV文件: {len(lines)}行, {csv_points}个数据点")
                        
                        if csv_points == points_count:
                            print("   ✅ 数据完整性验证通过")
                            return True
                        else:
                            print(f"   ❌ 数据完整性验证失败: {csv_points} != {points_count}")
                            return False
                    else:
                        print("   ❌ 点表生成失败")
                        return False
                else:
                    print(f"   ❌ 数据点数量不足: {points_count}")
                    return False
            else:
                print("   ❌ SCD文件解析失败")
                return False
        else:
            print(f"   ❌ SCD文件不存在: {scd_file}")
            return False
            
    except Exception as e:
        print(f"   ❌ SCD转换器测试失败: {e}")
        return False

def test_report_generator():
    """测试报告生成器"""
    print_section("报告生成器测试")
    
    try:
        from report_generator import create_test_report
        
        # 创建测试数据
        test_data = {
            'operator': '重新测试工程师',
            'project_name': '完整重新测试项目',
            'station_name': '测试变电站',
            'test_mode': '自动对点',
            'test_range': '全部信号',
            'speed_setting': '7级快速',
            'total_points': 2000,
            'success_points': 1950,
            'failed_points': 50,
            'success_rate': 97.5,
            'test_duration': '6分钟',
            'signal_types': {
                'DI': 1520,
                'AI': 320,
                'DO': 0,
                'AO': 160
            }
        }
        
        print("   🔄 生成测试报告...")
        
        # 创建报告目录
        report_dir = "retest_reports"
        os.makedirs(report_dir, exist_ok=True)
        
        # 生成报告
        reports = create_test_report(test_data, report_dir)
        
        if reports:
            print(f"   ✅ 报告生成成功: {len(reports)}种格式")
            
            all_generated = True
            for format_type, path in reports.items():
                if os.path.exists(path):
                    size = os.path.getsize(path) / 1024
                    print(f"      📄 {format_type.upper()}: {os.path.basename(path)} ({size:.1f}KB)")
                else:
                    print(f"      ❌ {format_type.upper()}: 文件生成失败")
                    all_generated = False
            
            return all_generated
        else:
            print("   ❌ 报告生成失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 报告生成器测试失败: {e}")
        return False

def test_web_interface_import():
    """测试Web界面模块导入"""
    print_section("Web界面模块导入测试")
    
    try:
        # 测试核心模块导入
        print("   🔄 测试SCD转换器导入...")
        from scd_to_point_converter import SCDToPointConverter
        print("   ✅ SCD转换器导入成功")
        
        print("   🔄 测试报告生成器导入...")
        from report_generator import create_test_report, ReportGenerator
        print("   ✅ 报告生成器导入成功")
        
        # 测试Web界面文件语法
        print("   🔄 检查Web界面文件语法...")
        with open('main_web_functional.py', 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 简单语法检查
        if 'SCDToPointConverter_available = True' in content:
            print("   ✅ Web界面模块导入逻辑正确")
            return True
        else:
            print("   ❌ Web界面模块导入逻辑有问题")
            return False
            
    except Exception as e:
        print(f"   ❌ Web界面模块导入测试失败: {e}")
        return False

def test_web_interface_startup():
    """测试Web界面启动"""
    print_section("Web界面启动测试")
    
    try:
        print("   🔄 检查Web界面进程...")
        
        # 检查是否有正在运行的Web界面
        import psutil
        web_running = False
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if proc.info['cmdline'] and 'main_web_functional.py' in ' '.join(proc.info['cmdline']):
                    print(f"   ✅ 发现运行中的Web界面进程: PID {proc.info['pid']}")
                    web_running = True
                    break
            except:
                continue
        
        if not web_running:
            print("   ℹ️ 没有发现运行中的Web界面进程")
            print("   💡 建议手动启动: python main_web_functional.py")
        
        return True
        
    except ImportError:
        print("   ⚠️ psutil模块不可用，跳过进程检查")
        print("   💡 建议手动启动: python main_web_functional.py")
        return True
    except Exception as e:
        print(f"   ❌ Web界面启动测试失败: {e}")
        return False

def test_integration():
    """集成测试"""
    print_section("集成测试")
    
    try:
        # 测试完整的SCD转换流程
        print("   🔄 执行完整SCD转换流程...")
        
        from scd_to_point_converter import SCDToPointConverter
        from report_generator import create_test_report
        
        # 1. SCD文件转换
        converter = SCDToPointConverter()
        scd_file = "large_substation_2000points.scd"
        
        if not os.path.exists(scd_file):
            print(f"   ❌ 测试文件不存在: {scd_file}")
            return False
        
        result = converter.parse_scd_file(scd_file)
        if not result or len(converter.signal_points) == 0:
            print("   ❌ SCD文件解析失败")
            return False
        
        print(f"   ✅ SCD解析成功: {len(converter.signal_points)}个数据点")
        
        # 2. 点表转换
        csv_file = converter.convert_to_point_table('csv')
        if not os.path.exists(csv_file):
            print("   ❌ 点表转换失败")
            return False
        
        print(f"   ✅ 点表转换成功: {csv_file}")
        
        # 3. 模拟测试数据
        signal_types = {}
        for point in converter.signal_points:
            signal_type = point.get('signal_type', 'Unknown')
            signal_types[signal_type] = signal_types.get(signal_type, 0) + 1
        
        # 4. 生成报告
        test_data = {
            'operator': '集成测试工程师',
            'project_name': '集成测试项目',
            'station_name': '集成测试变电站',
            'test_mode': '自动对点',
            'test_range': '全部信号',
            'speed_setting': '7级快速',
            'total_points': len(converter.signal_points),
            'success_points': int(len(converter.signal_points) * 0.975),
            'failed_points': int(len(converter.signal_points) * 0.025),
            'success_rate': 97.5,
            'test_duration': '8分钟',
            'signal_types': signal_types
        }
        
        # 创建报告目录
        os.makedirs("integration_reports", exist_ok=True)
        reports = create_test_report(test_data, "integration_reports")
        if not reports:
            print("   ❌ 报告生成失败")
            return False
        
        print(f"   ✅ 报告生成成功: {len(reports)}种格式")
        
        print("   ✅ 集成测试完全通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 集成测试失败: {e}")
        return False

def generate_test_summary(results):
    """生成测试总结"""
    print_header("测试总结")
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    failed_tests = total_tests - passed_tests
    
    print(f"📊 测试统计:")
    print(f"   🧪 总测试项: {total_tests}")
    print(f"   ✅ 通过测试: {passed_tests}")
    print(f"   ❌ 失败测试: {failed_tests}")
    print(f"   📈 通过率: {passed_tests/total_tests*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    # 生成建议
    print(f"\n💡 使用建议:")
    if passed_tests == total_tests:
        print(f"   🎉 所有测试通过！系统完全就绪")
        print(f"   🚀 可以启动Web界面: python main_web_functional.py")
        print(f"   📁 可以测试SCD文件转换功能")
        print(f"   📊 可以生成对点报告")
    else:
        print(f"   ⚠️ 有{failed_tests}个测试失败")
        print(f"   🔧 请检查失败的测试项")
        print(f"   📚 参考快速入门指南进行排查")
    
    return passed_tests == total_tests

def main():
    """主测试函数"""
    print_header("Auto_Point 完整重新测试")
    print(f"🕐 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 测试目录: {os.getcwd()}")
    print(f"🎯 测试目标: 全面验证系统功能")
    
    # 执行所有测试
    test_results = {}
    
    # 1. 环境检查
    env_results = test_environment()
    for key, value in env_results.items():
        test_results[f"环境-{key}"] = value
    
    # 2. SCD转换器独立测试
    test_results['SCD转换器独立测试'] = test_scd_converter_standalone()
    
    # 3. 报告生成器测试
    test_results['报告生成器测试'] = test_report_generator()
    
    # 4. Web界面模块导入测试
    test_results['Web界面模块导入'] = test_web_interface_import()
    
    # 5. Web界面启动测试
    test_results['Web界面启动检查'] = test_web_interface_startup()
    
    # 6. 集成测试
    test_results['集成测试'] = test_integration()
    
    # 生成测试总结
    success = generate_test_summary(test_results)
    
    # 保存测试报告
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_file = f"完整测试报告_{timestamp}.txt"
    
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("Auto_Point 完整重新测试报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write(f"测试统计:\n")
            f.write(f"总测试项: {len(test_results)}\n")
            f.write(f"通过测试: {sum(1 for r in test_results.values() if r)}\n")
            f.write(f"失败测试: {sum(1 for r in test_results.values() if not r)}\n\n")
            
            f.write("详细结果:\n")
            for test_name, result in test_results.items():
                status = "通过" if result else "失败"
                f.write(f"{test_name}: {status}\n")
        
        print(f"\n📄 测试报告已保存: {report_file}")
    except Exception as e:
        print(f"\n⚠️ 测试报告保存失败: {e}")
    
    return success

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✅ 完整重新测试通过 - 系统正常")
        sys.exit(0)
    else:
        print(f"\n❌ 完整重新测试发现问题 - 需要修复")
        sys.exit(1)
