#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析220kVQFB.scd文件
详细解析SCD文件的结构、内容和数据点
"""

import xml.etree.ElementTree as ET
import os
import re
from datetime import datetime

def analyze_scd_file(filename):
    """分析SCD文件"""
    print(f"🔍 分析SCD文件: {filename}")
    print("=" * 80)
    print(f"🕐 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        return False
    
    # 基本文件信息
    file_size = os.path.getsize(filename)
    print(f"\n📄 文件基本信息:")
    print(f"   文件路径: {filename}")
    print(f"   文件大小: {file_size:,} bytes ({file_size/1024:.1f}KB)")
    
    try:
        # 读取文件内容
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"   文件编码: UTF-8")
        print(f"   内容长度: {len(content):,} 字符")
        
        # 检查文件头
        first_lines = content.split('\n')[:10]
        print(f"\n📋 文件头信息:")
        for i, line in enumerate(first_lines):
            if line.strip():
                print(f"   {i+1:2d}: {line.strip()[:100]}...")
        
        # 尝试XML解析
        try:
            tree = ET.parse(filename)
            root = tree.getroot()
            print(f"\n✅ XML解析成功")
            
            # 分析XML结构
            analyze_xml_structure(root)
            
            # 分析变电站信息
            analyze_substation_info(root)
            
            # 分析IED设备
            analyze_ied_devices(root)
            
            # 分析数据点
            analyze_data_points(root)
            
            # 分析通信配置
            analyze_communication(root)
            
            return True
            
        except ET.ParseError as e:
            print(f"\n❌ XML解析失败: {e}")
            
            # 尝试文本分析
            analyze_text_content(content)
            return False
            
    except UnicodeDecodeError:
        # 尝试其他编码
        try:
            with open(filename, 'r', encoding='gbk') as f:
                content = f.read()
            print(f"   文件编码: GBK")
            print(f"   内容长度: {len(content):,} 字符")
            
            # 重新尝试XML解析
            try:
                tree = ET.parse(filename)
                root = tree.getroot()
                print(f"\n✅ XML解析成功 (GBK编码)")
                analyze_xml_structure(root)
                return True
            except ET.ParseError as e:
                print(f"\n❌ XML解析失败 (GBK编码): {e}")
                analyze_text_content(content)
                return False
                
        except Exception as e:
            print(f"\n❌ 文件读取失败: {e}")
            return False
    
    except Exception as e:
        print(f"\n❌ 分析失败: {e}")
        return False

def analyze_xml_structure(root):
    """分析XML结构"""
    print(f"\n📊 XML结构分析:")
    
    # 根元素信息
    root_tag = root.tag.split('}')[-1] if '}' in root.tag else root.tag
    print(f"   根元素: {root_tag}")
    
    # 命名空间
    if '}' in root.tag:
        namespace = root.tag.split('}')[0][1:]
        print(f"   命名空间: {namespace}")
    
    # 属性
    if root.attrib:
        print(f"   根元素属性:")
        for key, value in root.attrib.items():
            print(f"      {key}: {value}")
    
    # 子元素统计
    child_elements = {}
    for child in root:
        tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
        child_elements[tag] = child_elements.get(tag, 0) + 1
    
    print(f"   子元素统计:")
    for tag, count in child_elements.items():
        print(f"      {tag}: {count}个")

def analyze_substation_info(root):
    """分析变电站信息"""
    print(f"\n🏭 变电站信息:")
    
    # 定义命名空间
    ns = {'scl': 'http://www.iec.ch/61850/2003/SCL'} if 'http://www.iec.ch/61850/2003/SCL' in str(root.tag) else {}
    
    # 查找Substation元素
    substations = root.findall('.//Substation') if not ns else root.findall('.//scl:Substation', ns)
    
    if not substations:
        # 尝试不带命名空间
        substations = root.findall('.//Substation')
    
    print(f"   变电站数量: {len(substations)}")
    
    for i, substation in enumerate(substations):
        name = substation.get('name', f'未命名变电站{i+1}')
        desc = substation.get('desc', '无描述')
        print(f"   变电站 {i+1}: {name}")
        print(f"      描述: {desc}")
        
        # 电压等级
        voltage_levels = substation.findall('.//VoltageLevel')
        print(f"      电压等级数: {len(voltage_levels)}")
        
        for vl in voltage_levels:
            vl_name = vl.get('name', '未命名')
            voltage = vl.find('.//Voltage')
            if voltage is not None:
                voltage_value = voltage.text
                voltage_unit = voltage.get('unit', '')
                voltage_mult = voltage.get('multiplier', '')
                print(f"         - {vl_name}: {voltage_value}{voltage_mult}{voltage_unit}")
            else:
                print(f"         - {vl_name}: 电压值未定义")
            
            # 间隔
            bays = vl.findall('.//Bay')
            print(f"           间隔数: {len(bays)}")
            
            for bay in bays[:3]:  # 只显示前3个
                bay_name = bay.get('name', '未命名间隔')
                equipment = bay.findall('.//ConductingEquipment')
                print(f"             - {bay_name}: {len(equipment)}个设备")

def analyze_ied_devices(root):
    """分析IED设备"""
    print(f"\n🔧 IED设备分析:")
    
    # 查找IED元素
    ieds = root.findall('.//IED')
    print(f"   IED设备数量: {len(ieds)}")
    
    if len(ieds) == 0:
        print("   ⚠️ 未找到IED设备定义")
        return
    
    ied_summary = {}
    total_logical_nodes = 0
    total_data_objects = 0
    
    for i, ied in enumerate(ieds):
        name = ied.get('name', f'IED_{i+1}')
        manufacturer = ied.get('manufacturer', '未知厂商')
        ied_type = ied.get('type', '未知类型')
        desc = ied.get('desc', '无描述')
        
        print(f"\n   IED {i+1}: {name}")
        print(f"      厂商: {manufacturer}")
        print(f"      类型: {ied_type}")
        print(f"      描述: {desc}")
        
        # 逻辑设备
        ldevices = ied.findall('.//LDevice')
        print(f"      逻辑设备数: {len(ldevices)}")
        
        ied_ln_count = 0
        ied_do_count = 0
        
        for ldevice in ldevices:
            ld_inst = ldevice.get('inst', 'LD0')
            
            # 逻辑节点
            lnodes = ldevice.findall('.//LN') + ldevice.findall('.//LN0')
            ied_ln_count += len(lnodes)
            
            ln_types = {}
            for lnode in lnodes:
                ln_class = lnode.get('lnClass', 'Unknown')
                ln_types[ln_class] = ln_types.get(ln_class, 0) + 1
                
                # 数据对象
                dois = lnode.findall('.//DOI')
                ied_do_count += len(dois)
            
            if ln_types:
                print(f"         {ld_inst} 逻辑节点类型: {dict(list(ln_types.items())[:5])}...")
        
        print(f"      总逻辑节点: {ied_ln_count}")
        print(f"      总数据对象: {ied_do_count}")
        
        total_logical_nodes += ied_ln_count
        total_data_objects += ied_do_count
        
        ied_summary[name] = {
            'logical_nodes': ied_ln_count,
            'data_objects': ied_do_count,
            'manufacturer': manufacturer,
            'type': ied_type
        }
    
    print(f"\n   📊 IED设备总计:")
    print(f"      总逻辑节点数: {total_logical_nodes}")
    print(f"      总数据对象数: {total_data_objects}")

def analyze_data_points(root):
    """分析数据点"""
    print(f"\n📋 数据点分析:")
    
    # 统计不同类型的数据点
    signal_types = {}
    logical_node_types = {}
    data_object_types = {}
    
    # 查找所有逻辑节点
    lnodes = root.findall('.//LN') + root.findall('.//LN0')
    
    for lnode in lnodes:
        ln_class = lnode.get('lnClass', 'Unknown')
        logical_node_types[ln_class] = logical_node_types.get(ln_class, 0) + 1
        
        # 查找数据对象实例
        dois = lnode.findall('.//DOI')
        for doi in dois:
            do_name = doi.get('name', 'Unknown')
            data_object_types[do_name] = data_object_types.get(do_name, 0) + 1
            
            # 根据逻辑节点类和数据对象名推断信号类型
            signal_type = determine_signal_type(ln_class, do_name)
            signal_types[signal_type] = signal_types.get(signal_type, 0) + 1
    
    print(f"   逻辑节点类型分布:")
    for ln_type, count in sorted(logical_node_types.items()):
        ln_desc = get_ln_description(ln_type)
        print(f"      {ln_type} ({ln_desc}): {count}个")
    
    print(f"\n   数据对象类型分布 (前10个):")
    sorted_do_types = sorted(data_object_types.items(), key=lambda x: x[1], reverse=True)
    for do_type, count in sorted_do_types[:10]:
        print(f"      {do_type}: {count}个")
    
    print(f"\n   推断的信号类型分布:")
    for signal_type, count in sorted(signal_types.items()):
        type_desc = {'DI': '遥信', 'AI': '遥测', 'AO': '遥调', 'DO': '遥控'}.get(signal_type, signal_type)
        print(f"      {type_desc}({signal_type}): {count}个")

def analyze_communication(root):
    """分析通信配置"""
    print(f"\n📡 通信配置分析:")
    
    # 查找Communication元素
    communication = root.find('.//Communication')
    
    if communication is None:
        print("   ⚠️ 未找到通信配置")
        return
    
    # 子网
    subnets = communication.findall('.//SubNetwork')
    print(f"   子网数量: {len(subnets)}")
    
    for subnet in subnets:
        subnet_name = subnet.get('name', '未命名子网')
        subnet_type = subnet.get('type', '未知类型')
        print(f"      子网: {subnet_name} ({subnet_type})")
        
        # 连接的访问点
        connected_aps = subnet.findall('.//ConnectedAP')
        print(f"         连接的访问点: {len(connected_aps)}")
        
        for cap in connected_aps[:5]:  # 只显示前5个
            ied_name = cap.get('iedName', '未知IED')
            ap_name = cap.get('apName', '未知AP')
            
            # IP地址
            ip_elem = cap.find('.//P[@type="IP"]')
            ip_address = ip_elem.text if ip_elem is not None else '未配置'
            
            print(f"            {ied_name}.{ap_name}: {ip_address}")

def determine_signal_type(ln_class, do_name):
    """根据逻辑节点类和数据对象名确定信号类型"""
    if ln_class in ['XCBR', 'XSWI', 'CSWI']:
        if 'Pos' in do_name:
            return 'DI'  # 位置状态
        elif 'Blk' in do_name:
            return 'DI'  # 闭锁状态
    elif ln_class in ['MMXU', 'MMTR']:
        return 'AI'  # 测量值
    elif ln_class in ['PTRC', 'PDIF', 'PDIR']:
        return 'DI'  # 保护信号
    elif ln_class == 'GGIO':
        if 'Ind' in do_name:
            return 'DI'  # 指示
        elif 'SPCSO' in do_name:
            return 'AO'  # 控制输出
    
    return 'Unknown'

def get_ln_description(ln_class):
    """获取逻辑节点类描述"""
    descriptions = {
        'LLN0': '逻辑节点0',
        'XCBR': '断路器',
        'XSWI': '开关',
        'CSWI': '控制开关',
        'MMXU': '测量单元',
        'MMTR': '变压器测量',
        'PTRC': '保护',
        'PDIF': '差动保护',
        'PDIR': '方向保护',
        'GGIO': '通用输入输出',
        'TCTR': '电流互感器',
        'TVTR': '电压互感器'
    }
    return descriptions.get(ln_class, '未知类型')

def analyze_text_content(content):
    """文本内容分析（当XML解析失败时）"""
    print(f"\n📝 文本内容分析:")
    
    # 查找关键标签
    tags = ['<SCL', '<Header', '<Substation', '<IED', '<LDevice', '<LN', '<DOI']
    
    for tag in tags:
        count = content.count(tag)
        if count > 0:
            print(f"   {tag}: {count}个")
    
    # 查找IED名称
    ied_names = re.findall(r'<IED[^>]*name="([^"]*)"', content)
    if ied_names:
        print(f"\n   发现的IED名称:")
        for name in ied_names[:10]:  # 只显示前10个
            print(f"      - {name}")
    
    # 查找变电站名称
    substation_names = re.findall(r'<Substation[^>]*name="([^"]*)"', content)
    if substation_names:
        print(f"\n   发现的变电站名称:")
        for name in substation_names:
            print(f"      - {name}")

def generate_analysis_report(filename):
    """生成分析报告"""
    report_filename = f"SCD分析报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    print(f"\n📄 生成分析报告: {report_filename}")
    
    # 这里可以添加报告生成逻辑
    # 暂时只是提示
    print(f"   报告将包含完整的SCD文件分析结果")

def main():
    """主函数"""
    filename = r"D:\auto_point\222\220kVQFB.scd"
    
    print("🎯 220kVQFB.scd 文件分析工具")
    print("=" * 80)
    
    # 分析文件
    success = analyze_scd_file(filename)
    
    if success:
        print(f"\n✅ SCD文件分析完成")
        print(f"📋 这是一个220kV旗峰坝变电站的SCD配置文件")
        print(f"💡 可以在Auto_Point对点机中使用此文件进行:")
        print(f"   1. SCD文件解析验证")
        print(f"   2. 数据点提取和转换")
        print(f"   3. 对点测试基准配置")
        print(f"   4. 与实际变电站对点验证")
    else:
        print(f"\n⚠️ SCD文件分析遇到问题")
        print(f"💡 建议:")
        print(f"   1. 检查文件编码格式")
        print(f"   2. 验证XML格式完整性")
        print(f"   3. 使用SCD修复工具处理")
    
    # 生成报告
    generate_analysis_report(filename)

if __name__ == "__main__":
    main()
