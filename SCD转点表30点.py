#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将30点位SCD文件转换为点表文件
验证完整的SCD到点表转换流程
"""

import xml.etree.ElementTree as ET
import csv
from datetime import datetime
import os

def parse_scd_file(scd_filename):
    """解析SCD文件"""
    print(f"🔍 解析SCD文件: {scd_filename}")
    
    try:
        tree = ET.parse(scd_filename)
        root = tree.getroot()
        
        # 定义命名空间
        ns = {'scl': 'http://www.iec.ch/61850/2003/SCL'}
        
        # 提取数据点
        data_points = []
        point_id = 1001
        
        # 查找所有IED
        ieds = root.findall('scl:IED', ns)
        print(f"   📊 找到 {len(ieds)} 个IED设备")
        
        for ied in ieds:
            ied_name = ied.get('name', 'Unknown')
            ied_desc = ied.get('desc', 'No description')
            print(f"   🔧 处理IED: {ied_name} ({ied_desc})")
            
            # 查找逻辑设备
            ldevices = ied.findall('.//scl:LDevice', ns)
            for ldevice in ldevices:
                ld_inst = ldevice.get('inst', 'LD0')
                
                # 查找逻辑节点
                lnodes = ldevice.findall('scl:LN', ns)
                for lnode in lnodes:
                    ln_class = lnode.get('lnClass', 'Unknown')
                    ln_inst = lnode.get('inst', '1')
                    
                    # 查找数据对象实例
                    dois = lnode.findall('scl:DOI', ns)
                    for doi in dois:
                        do_name = doi.get('name', 'Unknown')
                        
                        # 查找数据属性实例
                        dais = doi.findall('.//scl:DAI', ns)
                        for dai in dais:
                            da_name = dai.get('name', 'Unknown')
                            
                            # 获取值
                            val_elem = dai.find('scl:Val', ns)
                            value = val_elem.text if val_elem is not None else '0'
                            
                            # 构建信号名称和SCD路径
                            signal_name = f"{ied_name}_{ln_class}{ln_inst}_{do_name}_{da_name}"
                            scd_path = f"{ied_name}.{ld_inst}.{ln_class}{ln_inst}.{do_name}.{da_name}"
                            
                            # 确定信号类型和数据类型
                            signal_type, data_type, description = determine_signal_info(
                                ln_class, do_name, da_name, value
                            )
                            
                            # 添加数据点
                            data_point = {
                                'point_id': point_id,
                                'signal_name': signal_name,
                                'signal_type': signal_type,
                                'data_type': data_type,
                                'expected_value': value,
                                'description': description,
                                'scd_path': scd_path,
                                'ied_name': ied_name
                            }
                            
                            data_points.append(data_point)
                            point_id += 1
                        
                        # 处理子数据实例(SDI)
                        sdis = doi.findall('.//scl:SDI', ns)
                        for sdi in sdis:
                            sdi_name = sdi.get('name', 'Unknown')
                            
                            sdi_dais = sdi.findall('.//scl:DAI', ns)
                            for dai in sdi_dais:
                                da_name = dai.get('name', 'Unknown')
                                
                                # 获取值
                                val_elem = dai.find('scl:Val', ns)
                                value = val_elem.text if val_elem is not None else '0'
                                
                                # 构建信号名称和SCD路径
                                signal_name = f"{ied_name}_{ln_class}{ln_inst}_{do_name}_{sdi_name}_{da_name}"
                                scd_path = f"{ied_name}.{ld_inst}.{ln_class}{ln_inst}.{do_name}.{sdi_name}.{da_name}"
                                
                                # 确定信号类型和数据类型
                                signal_type, data_type, description = determine_signal_info(
                                    ln_class, do_name, f"{sdi_name}_{da_name}", value
                                )
                                
                                # 添加数据点
                                data_point = {
                                    'point_id': point_id,
                                    'signal_name': signal_name,
                                    'signal_type': signal_type,
                                    'data_type': data_type,
                                    'expected_value': value,
                                    'description': description,
                                    'scd_path': scd_path,
                                    'ied_name': ied_name
                                }
                                
                                data_points.append(data_point)
                                point_id += 1
        
        print(f"   ✅ 解析完成，共提取 {len(data_points)} 个数据点")
        return data_points
        
    except Exception as e:
        print(f"   ❌ SCD文件解析失败: {e}")
        return []

def determine_signal_info(ln_class, do_name, da_name, value):
    """确定信号类型、数据类型和描述"""
    
    # 根据逻辑节点类和数据对象名确定信号类型
    if ln_class == 'XCBR':  # 断路器
        if 'Pos' in do_name and 'stVal' in da_name:
            return 'DI', 'BOOL', '断路器位置状态'
        elif 'BlkOpn' in do_name:
            return 'DI', 'BOOL', '分闸闭锁状态'
        elif 'BlkCls' in do_name:
            return 'DI', 'BOOL', '合闸闭锁状态'
    
    elif ln_class == 'PTRC':  # 保护
        if 'Str' in do_name:
            return 'DI', 'BOOL', '保护启动信号'
        elif 'Op' in do_name:
            return 'DI', 'BOOL', '保护动作信号'
        elif 'Tr' in do_name:
            return 'DI', 'BOOL', '保护跳闸信号'
    
    elif ln_class == 'MMXU':  # 测量单元
        if 'TotW' in do_name:
            return 'AI', 'FLOAT', '总有功功率'
        elif 'TotVAr' in do_name:
            return 'AI', 'FLOAT', '总无功功率'
        elif 'Hz' in do_name:
            return 'AI', 'FLOAT', '频率'
        elif 'PPV' in do_name and 'phsA' in da_name:
            return 'AI', 'FLOAT', 'A相电压'
        elif 'PPV' in do_name and 'phsB' in da_name:
            return 'AI', 'FLOAT', 'B相电压'
        elif 'PPV' in do_name and 'phsC' in da_name:
            return 'AI', 'FLOAT', 'C相电压'
        elif 'A' in do_name and 'phsA' in da_name:
            return 'AI', 'FLOAT', 'A相电流'
        elif 'A' in do_name and 'phsB' in da_name:
            return 'AI', 'FLOAT', 'B相电流'
        elif 'A' in do_name and 'phsC' in da_name:
            return 'AI', 'FLOAT', 'C相电流'
    
    elif ln_class == 'CSWI':  # 控制开关
        if 'Pos' in do_name and 'ctlVal' in da_name:
            return 'AO', 'BOOL', '断路器控制'
    
    elif ln_class == 'GGIO':  # 通用输入输出
        if 'Ind1' in do_name:
            return 'DI', 'BOOL', 'PT断线指示'
        elif 'Ind2' in do_name:
            return 'DI', 'BOOL', '母线接地指示'
        elif 'SPCSO' in do_name:
            return 'AO', 'BOOL', '通用控制输出'
    
    # 默认值
    if value.lower() in ['true', 'false']:
        return 'DI', 'BOOL', '数字信号'
    else:
        try:
            float(value)
            return 'AI', 'FLOAT', '模拟信号'
        except:
            return 'DI', 'STRING', '字符串信号'

def convert_to_point_table(data_points, scd_filename):
    """转换为点表文件"""
    print(f"🔄 转换为点表文件...")
    
    # 生成点表文件名
    base_name = os.path.splitext(scd_filename)[0]
    csv_filename = f"{base_name}_converted_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    # CSV文件头
    fieldnames = ['点号', '信号名称', '信号类型', '数据类型', '期望值', '描述', 'SCD路径', 'IED名称']
    
    try:
        with open(csv_filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            
            for point in data_points:
                writer.writerow({
                    '点号': point['point_id'],
                    '信号名称': point['signal_name'],
                    '信号类型': point['signal_type'],
                    '数据类型': point['data_type'],
                    '期望值': point['expected_value'],
                    '描述': point['description'],
                    'SCD路径': point['scd_path'],
                    'IED名称': point['ied_name']
                })
        
        print(f"   ✅ 点表文件生成成功: {csv_filename}")
        return csv_filename
        
    except Exception as e:
        print(f"   ❌ 点表文件生成失败: {e}")
        return None

def verify_conversion(csv_filename):
    """验证转换结果"""
    print(f"🔍 验证转换结果: {csv_filename}")
    
    try:
        import pandas as pd
        df = pd.read_csv(csv_filename, encoding='utf-8-sig')
        
        total_points = len(df)
        file_size = os.path.getsize(csv_filename) / 1024
        
        print(f"   ✅ 验证成功")
        print(f"   📊 数据点数: {total_points}")
        print(f"   📏 文件大小: {file_size:.1f}KB")
        
        # 统计信号类型
        signal_counts = df['信号类型'].value_counts()
        print(f"   📈 信号类型分布:")
        for signal_type, count in signal_counts.items():
            type_name = {'DI': '遥信', 'AI': '遥测', 'AO': '遥调'}.get(signal_type, signal_type)
            print(f"      {type_name}({signal_type}): {count}个")
        
        # 统计IED分布
        ied_counts = df['IED名称'].value_counts()
        print(f"   🏭 IED设备分布:")
        for ied_name, count in ied_counts.items():
            print(f"      {ied_name}: {count}个数据点")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 SCD文件转换为点表文件")
    print("=" * 60)
    print(f"🕐 转换时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 查找最新的SCD文件
    scd_files = [f for f in os.listdir('.') if f.startswith('scd_30points_') and f.endswith('.scd')]
    
    if not scd_files:
        print("❌ 未找到30点位SCD文件")
        return False
    
    # 使用最新的SCD文件
    scd_filename = sorted(scd_files)[-1]
    print(f"📄 使用SCD文件: {scd_filename}")
    
    # 解析SCD文件
    data_points = parse_scd_file(scd_filename)
    
    if not data_points:
        print("❌ SCD文件解析失败，无法转换")
        return False
    
    # 转换为点表
    csv_filename = convert_to_point_table(data_points, scd_filename)
    
    if not csv_filename:
        print("❌ 点表转换失败")
        return False
    
    # 验证转换结果
    if verify_conversion(csv_filename):
        print(f"\n✅ SCD到点表转换完成!")
        print(f"📄 原SCD文件: {scd_filename}")
        print(f"📋 生成点表: {csv_filename}")
        
        print(f"\n💡 使用方法:")
        print(f"   1. 在对点机Web界面中可以使用两种方式:")
        print(f"      方式A: 加载SCD文件 {scd_filename} → 解析 → 转换")
        print(f"      方式B: 直接加载点表文件 {csv_filename}")
        print(f"   2. 配置通信连接 (localhost:102)")
        print(f"   3. 执行对点测试")
        print(f"   4. 生成测试报告")
        
        return True
    else:
        print("❌ 转换验证失败")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🎉 SCD到点表转换成功完成!")
        print(f"📝 现在有完整的测试文件:")
        print(f"   - SCD文件: 用于标准配置解析")
        print(f"   - 点表文件: 用于直接加载测试")
        print(f"   - 30个数据点: 适合快速验证")
    else:
        print(f"\n💥 SCD到点表转换失败")
