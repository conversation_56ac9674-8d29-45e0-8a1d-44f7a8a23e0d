#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动启动子站服务
直接启动子站模拟器的网络服务，无需GUI操作
"""

import socket
import threading
import json
import pandas as pd
import time
import os
from datetime import datetime

class AutoSubstationService:
    """自动子站服务"""
    
    def __init__(self, host='0.0.0.0', port=102):
        self.host = host
        self.port = port
        self.server_socket = None
        self.clients = []
        self.running = False
        self.data_points = {}
        
    def load_data_points(self):
        """加载数据点"""
        print("📁 加载数据点...")
        
        # 尝试加载SCD文件转换的点表
        csv_files = [f for f in os.listdir('.') if f.startswith('point_table_') and f.endswith('.csv')]
        
        if csv_files:
            # 使用最新的点表文件
            latest_file = max(csv_files, key=os.path.getctime)
            print(f"   📄 使用点表文件: {latest_file}")
            
            try:
                df = pd.read_csv(latest_file, encoding='utf-8-sig')
                
                for index, row in df.iterrows():
                    point_id = row.get('点号', index + 1001)
                    signal_name = row.get('信号名称', f'Signal_{point_id}')
                    signal_type = row.get('信号类型', 'DI')
                    data_type = row.get('数据类型', 'BOOL')
                    expected_value = row.get('期望值', '0')
                    description = row.get('描述', '')
                    
                    self.data_points[point_id] = {
                        'name': signal_name,
                        'type': signal_type,
                        'data_type': data_type,
                        'value': expected_value,
                        'description': description,
                        'timestamp': datetime.now().isoformat()
                    }
                
                print(f"   ✅ 成功加载 {len(self.data_points)} 个数据点")
                return True
                
            except Exception as e:
                print(f"   ❌ 加载点表文件失败: {e}")
        
        # 如果没有点表文件，生成模拟数据点
        print("   💡 生成模拟数据点...")
        self.generate_sample_data()
        return True
    
    def generate_sample_data(self):
        """生成示例数据点"""
        # 生成2000个模拟数据点
        for i in range(2000):
            point_id = 1001 + i
            
            if i < 1520:  # 遥信信号
                signal_type = 'DI'
                data_type = 'BOOL'
                value = '0' if i % 2 == 0 else '1'
                name = f'DI_Signal_{point_id}'
                desc = f'遥信信号_{point_id}'
            elif i < 1840:  # 遥测信号
                signal_type = 'AI'
                data_type = 'FLOAT'
                value = str(round(220.0 + (i % 100) * 0.1, 2))
                name = f'AI_Signal_{point_id}'
                desc = f'遥测信号_{point_id}'
            else:  # 遥调信号
                signal_type = 'AO'
                data_type = 'FLOAT'
                value = str(round(50.0 + (i % 10) * 0.5, 1))
                name = f'AO_Signal_{point_id}'
                desc = f'遥调信号_{point_id}'
            
            self.data_points[point_id] = {
                'name': name,
                'type': signal_type,
                'data_type': data_type,
                'value': value,
                'description': desc,
                'timestamp': datetime.now().isoformat()
            }
        
        print(f"   ✅ 生成 {len(self.data_points)} 个模拟数据点")
    
    def start_service(self):
        """启动服务"""
        try:
            print(f"🚀 启动子站模拟器服务")
            print(f"   📡 监听地址: {self.host}:{self.port}")
            
            # 创建服务器socket
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            
            self.running = True
            print(f"   ✅ 服务启动成功，等待连接...")
            
            # 启动接受连接的线程
            accept_thread = threading.Thread(target=self.accept_connections)
            accept_thread.daemon = True
            accept_thread.start()
            
            return True
            
        except Exception as e:
            print(f"   ❌ 服务启动失败: {e}")
            return False
    
    def accept_connections(self):
        """接受客户端连接"""
        while self.running:
            try:
                client_socket, client_address = self.server_socket.accept()
                print(f"   🔗 新连接: {client_address}")
                
                self.clients.append(client_socket)
                
                # 为每个客户端启动处理线程
                client_thread = threading.Thread(
                    target=self.handle_client, 
                    args=(client_socket, client_address)
                )
                client_thread.daemon = True
                client_thread.start()
                
            except Exception as e:
                if self.running:
                    print(f"   ⚠️ 接受连接异常: {e}")
    
    def handle_client(self, client_socket, client_address):
        """处理客户端请求"""
        try:
            while self.running:
                data = client_socket.recv(1024)
                if not data:
                    break
                
                # 简单的协议处理
                request = data.decode('utf-8', errors='ignore')
                print(f"   📨 收到请求: {request[:50]}...")
                
                # 发送响应数据
                response = self.generate_response(request)
                client_socket.send(response.encode('utf-8'))
                
        except Exception as e:
            print(f"   ⚠️ 客户端处理异常: {e}")
        finally:
            try:
                client_socket.close()
                if client_socket in self.clients:
                    self.clients.remove(client_socket)
                print(f"   🔌 连接断开: {client_address}")
            except:
                pass
    
    def generate_response(self, request):
        """生成响应数据"""
        # 简单的响应格式
        response_data = {
            'status': 'OK',
            'timestamp': datetime.now().isoformat(),
            'data_points_count': len(self.data_points),
            'message': 'Substation simulator response'
        }
        
        return json.dumps(response_data)
    
    def stop_service(self):
        """停止服务"""
        print("⏹️ 停止子站模拟器服务...")
        self.running = False
        
        # 关闭所有客户端连接
        for client in self.clients:
            try:
                client.close()
            except:
                pass
        self.clients.clear()
        
        # 关闭服务器socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        
        print("   ✅ 服务已停止")
    
    def get_status(self):
        """获取服务状态"""
        return {
            'running': self.running,
            'host': self.host,
            'port': self.port,
            'clients_count': len(self.clients),
            'data_points_count': len(self.data_points)
        }

def main():
    """主函数"""
    print("🎯 自动启动子站模拟器服务")
    print("=" * 60)
    print(f"🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建服务实例
    service = AutoSubstationService(host='0.0.0.0', port=102)
    
    try:
        # 加载数据点
        if not service.load_data_points():
            print("❌ 数据点加载失败")
            return False
        
        # 启动服务
        if not service.start_service():
            print("❌ 服务启动失败")
            return False
        
        # 显示状态
        status = service.get_status()
        print(f"\n📊 服务状态:")
        print(f"   🌐 运行状态: {'运行中' if status['running'] else '已停止'}")
        print(f"   📡 监听地址: {status['host']}:{status['port']}")
        print(f"   🔗 当前连接: {status['clients_count']}个")
        print(f"   📊 数据点数: {status['data_points_count']}个")
        
        print(f"\n💡 服务已启动，可以在Auto_Point中配置连接:")
        print(f"   - 服务器地址: localhost")
        print(f"   - 端口: {status['port']}")
        print(f"   - 协议: IEC 61850")
        
        print(f"\n⏹️ 按 Ctrl+C 停止服务")
        
        # 保持服务运行
        while True:
            time.sleep(1)
            
    except KeyboardInterrupt:
        print(f"\n⏹️ 用户中断服务")
    except Exception as e:
        print(f"\n❌ 服务运行异常: {e}")
    finally:
        service.stop_service()
    
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✅ 子站服务运行完成")
    else:
        print(f"\n❌ 子站服务运行失败")
