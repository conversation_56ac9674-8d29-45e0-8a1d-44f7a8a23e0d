from config_parser import SC<PERSON>arser, parse_point_table
from mapping import auto_map_points
from core_iec61850 import IEC61850Adapter
import math

class AutoChecker:
    def __init__(self, scd_path, point_table_path, network_params=None, dll_path=None):
        self.network_params = network_params or {}
        self.protocol = self.network_params.get('protocol', 'IEC 61850')
        self.point_table = parse_point_table(point_table_path)
        self.scd_signals = []
        self.mapped_points = []
        # 仅IEC 61850时加载SCD和自动映射
        if self.protocol == 'IEC 61850' and scd_path:
            self.scd_parser = SCDParser(scd_path)
            self.scd_signals = self.scd_parser.get_signals()
            self.mapped_points = auto_map_points(
                self.scd_signals, self.point_table,
                scd_key_fields=['IED', 'SignalName'],
                point_key_fields=['IED', 'SignalName']
            )
        else:
            # DL/T 634.5104: 直接用点表
            self.mapped_points = self.point_table
        self.results = []
        self.adapter = IEC61850Adapter(dll_path, protocol=self.protocol)

    def run_check(self):
        """主流程：连接IED，批量读取信号并比对，输出对点校核结果"""
        ip = self.network_params.get('ip', '')
        port = self.network_params.get('port', 102)
        subnet_mask = self.network_params.get('subnet_mask', '')
        gateway = self.network_params.get('gateway', '')
        if not self.adapter.connect(ip, port, subnet_mask=subnet_mask, gateway=gateway):
            print("连接IED失败，终止校核")
            return []
        for point in self.mapped_points:
            # DL/T 634.5104点表无自动映射，直接对点
            if self.protocol == 'IEC 61850':
                match_status = point.get('match_status', '唯一匹配')
            else:
                match_status = '唯一匹配'
            if match_status == '唯一匹配':
                variable = point.get('SignalName') or point.get('变量名')
                value = self.adapter.read_data(variable)
                expected = point.get('ExpectedValue')
                if expected is None:
                    expected = point.get('期望值')
                point['actual_value'] = value
                point_type = str(point.get('Type') or point.get('DataType') or point.get('类型', '')).strip()
                def is_empty(x):
                    if x is None:
                        return True
                    if isinstance(x, float) and math.isnan(x):
                        return True
                    if isinstance(x, str) and x.strip() == '':
                        return True
                    return False
                if not is_empty(expected):
                    if point_type in ['遥信', '遥控']:
                        def to_bool(x):
                            if isinstance(x, bool):
                                return x
                            if isinstance(x, str):
                                x_str = x.strip().lower()
                                if x_str in ['true', '1', 'on', 'yes']:
                                    return True
                                elif x_str in ['false', '0', 'off', 'no']:
                                    return False
                                else:
                                    return bool(int(float(x)))
                            try:
                                return bool(int(float(x)))
                            except Exception:
                                return False
                        value_bool = to_bool(value)
                        expected_bool = to_bool(expected)
                        if value_bool == expected_bool:
                            point['check_result'] = '正确'
                        else:
                            point['check_result'] = '错误'
                    else:
                        if str(value) == str(expected):
                            point['check_result'] = '正确'
                        else:
                            point['check_result'] = '错误'
                else:
                    point['check_result'] = f'采集值:{value}'
            else:
                point['check_result'] = f'未唯一匹配({match_status})'
            self.results.append(point)
        return self.results


class PointChecker:
    """单点检查器 - 支持手动单点测试"""

    def __init__(self, ip, port, protocol="IEC 61850"):
        self.ip = ip
        self.port = port
        self.protocol = protocol
        self.adapter = IEC61850Adapter(protocol=protocol)
        self.connected = False

    def connect(self):
        """连接到设备"""
        try:
            self.connected = self.adapter.connect(self.ip, self.port)
            return self.connected
        except Exception as e:
            print(f"连接失败: {e}")
            return False

    def disconnect(self):
        """断开连接"""
        try:
            if self.adapter:
                self.adapter.disconnect()
            self.connected = False
        except Exception:
            pass

    def check_single_point(self, point):
        """检查单个数据点"""
        try:
            # 确保连接
            if not self.connected:
                if not self.connect():
                    return {
                        'actual_value': 'N/A',
                        'is_correct': False,
                        'error': '连接失败'
                    }

            # 获取信号名称和期望值
            signal_name = point.get('SignalName', '')
            expected_value = point.get('ExpectedValue', '')
            signal_type = point.get('DataType', '遥测')

            if not signal_name:
                return {
                    'actual_value': 'N/A',
                    'is_correct': False,
                    'error': '信号名称为空'
                }

            # 读取实际值
            actual_value = self.adapter.read_data(signal_name)

            if actual_value is None:
                return {
                    'actual_value': 'N/A',
                    'is_correct': False,
                    'error': '读取失败'
                }

            # 比较值
            is_correct = self._compare_values(expected_value, actual_value, signal_type)

            return {
                'actual_value': actual_value,
                'is_correct': is_correct,
                'signal_type': signal_type,
                'expected_value': expected_value
            }

        except Exception as e:
            return {
                'actual_value': 'N/A',
                'is_correct': False,
                'error': str(e)
            }

    def _compare_values(self, expected, actual, signal_type):
        """比较期望值和实际值"""
        try:
            # 处理空值
            if self._is_empty(expected):
                return True  # 如果没有期望值，认为正确

            if signal_type in ['遥信', '遥控']:
                # 布尔类型比较
                expected_bool = self._to_bool(expected)
                actual_bool = self._to_bool(actual)
                return expected_bool == actual_bool
            else:
                # 字符串比较
                return str(expected).strip() == str(actual).strip()

        except Exception:
            return False

    def _is_empty(self, value):
        """检查值是否为空"""
        if value is None:
            return True
        if isinstance(value, float) and math.isnan(value):
            return True
        if isinstance(value, str) and value.strip() == '':
            return True
        return False

    def _to_bool(self, value):
        """转换为布尔值"""
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            value_str = value.strip().lower()
            if value_str in ['true', '1', 'on', 'yes']:
                return True
            elif value_str in ['false', '0', 'off', 'no']:
                return False
            else:
                try:
                    return bool(int(float(value)))
                except:
                    return False
        try:
            return bool(int(float(value)))
        except Exception:
            return False