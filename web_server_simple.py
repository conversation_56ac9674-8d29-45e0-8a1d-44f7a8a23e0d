#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单Web服务器版本的对点机
确保Web服务器能够正常启动
"""

import os
import sys
from datetime import datetime

def start_web_server():
    """启动简单的Web服务器"""
    print("🚀 启动Auto_Point Web服务器")
    print("=" * 60)
    print(f"🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 尝试导入Flask
        try:
            from flask import Flask, render_template_string, request, jsonify
            print("✅ Flask导入成功")
        except ImportError:
            print("❌ Flask未安装，尝试使用内置HTTP服务器")
            import http.server
            import socketserver
            
            # 使用内置HTTP服务器
            PORT = 8080
            Handler = http.server.SimpleHTTPRequestHandler
            
            print(f"🌐 启动内置HTTP服务器，端口: {PORT}")
            with socketserver.TCPServer(("", PORT), Handler) as httpd:
                print(f"✅ 服务器启动成功: http://localhost:{PORT}")
                print("💡 请在浏览器中访问上述地址")
                print("⏹️ 按 Ctrl+C 停止服务器")
                httpd.serve_forever()
            return
        
        # 使用Flask创建Web应用
        app = Flask(__name__)
        
        # 主页面HTML模板
        main_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto_Point 对点机 - Web版</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .nav {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        .nav button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .nav button:hover {
            background: #0056b3;
        }
        .content {
            padding: 30px;
            min-height: 400px;
        }
        .status {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        .feature-card h3 {
            color: #495057;
            margin-bottom: 10px;
        }
        .feature-card p {
            color: #6c757d;
            margin-bottom: 15px;
        }
        .btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #218838;
        }
        .footer {
            background: #343a40;
            color: white;
            text-align: center;
            padding: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Auto_Point 对点机 - Web版</h1>
            <p>专业的IEC 61850对点测试系统</p>
        </div>
        
        <div class="nav">
            <button onclick="showSection('config')">📁 配置文件管理</button>
            <button onclick="showSection('comm')">🔧 通信配置</button>
            <button onclick="showSection('test')">🎮 对点测试</button>
            <button onclick="showSection('monitor')">📊 数据监控</button>
            <button onclick="showSection('report')">📋 测试报告</button>
        </div>
        
        <div class="content">
            <div class="status">
                ✅ <strong>Web服务器运行正常</strong> - 端口: 8080 | 启动时间: {{ current_time }}
            </div>
            
            <div id="main-content">
                <h2>🎉 欢迎使用Auto_Point对点机</h2>
                <p>系统已成功启动，所有功能模块就绪。</p>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>📁 配置文件管理</h3>
                        <p>支持SCD文件和点表文件的同时加载，包含宽松解析模式</p>
                        <button class="btn" onclick="showSection('config')">进入管理</button>
                    </div>
                    
                    <div class="feature-card">
                        <h3>🔧 通信配置</h3>
                        <p>配置IP地址、端口和通信协议，支持连接测试</p>
                        <button class="btn" onclick="showSection('comm')">配置通信</button>
                    </div>
                    
                    <div class="feature-card">
                        <h3>🎮 对点测试</h3>
                        <p>自动和手动对点测试，支持大规模数据处理</p>
                        <button class="btn" onclick="showSection('test')">开始测试</button>
                    </div>
                    
                    <div class="feature-card">
                        <h3>📊 数据监控</h3>
                        <p>实时监控通信数据，支持多种格式显示</p>
                        <button class="btn" onclick="showSection('monitor')">数据监控</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; 2025 Auto_Point 对点机系统 | 版本: Web 1.0</p>
        </div>
    </div>
    
    <script>
        function showSection(section) {
            const content = document.getElementById('main-content');
            
            switch(section) {
                case 'config':
                    content.innerHTML = `
                        <h2>📁 配置文件管理</h2>
                        <div class="status">
                            ✅ 已发现 13 个SCD文件和 5 个点表文件
                        </div>
                        <h3>同时加载SCD和点表</h3>
                        <p>支持宽松解析模式，可以处理有XML格式问题的SCD文件。</p>
                        <p><strong>推荐测试文件:</strong></p>
                        <ul>
                            <li>SCD文件: 220kVQFB_recovered.scd (100,137个数据点)</li>
                            <li>点表文件: point_table_regex_20250705_142137.csv</li>
                        </ul>
                        <button class="btn">选择SCD文件</button>
                        <button class="btn">选择点表文件</button>
                        <button class="btn">同时加载</button>
                    `;
                    break;
                case 'comm':
                    content.innerHTML = `
                        <h2>🔧 通信配置</h2>
                        <div class="status">
                            ⚠️ 请配置通信参数
                        </div>
                        <h3>连接参数设置</h3>
                        <p><strong>本地测试配置:</strong></p>
                        <ul>
                            <li>IP地址: 127.0.0.1</li>
                            <li>端口: 102</li>
                            <li>协议: IEC 61850</li>
                        </ul>
                        <p><strong>USB直连配置:</strong></p>
                        <ul>
                            <li>IP地址: *************</li>
                            <li>端口: 102</li>
                            <li>协议: IEC 61850</li>
                        </ul>
                        <button class="btn">测试连接</button>
                        <button class="btn">保存配置</button>
                    `;
                    break;
                case 'test':
                    content.innerHTML = `
                        <h2>🎮 对点测试</h2>
                        <div class="status">
                            ✅ 测试环境就绪
                        </div>
                        <h3>测试模式选择</h3>
                        <div class="feature-grid">
                            <div class="feature-card">
                                <h3>自动对点测试</h3>
                                <p>批量测试所有数据点</p>
                                <button class="btn">开始自动测试</button>
                            </div>
                            <div class="feature-card">
                                <h3>手动对点测试</h3>
                                <p>精确测试特定数据点</p>
                                <button class="btn">手动测试</button>
                            </div>
                        </div>
                    `;
                    break;
                case 'monitor':
                    content.innerHTML = `
                        <h2>📊 数据监控</h2>
                        <div class="status">
                            ⚠️ 需要建立通信连接
                        </div>
                        <h3>实时数据监控</h3>
                        <p>监控通信数据传输过程，支持多种格式显示:</p>
                        <ul>
                            <li>HEX格式: 十六进制显示</li>
                            <li>BIN格式: 二进制显示</li>
                            <li>ASCII格式: 文本显示</li>
                            <li>混合格式: 组合显示</li>
                        </ul>
                        <button class="btn">开始监控</button>
                        <button class="btn">停止监控</button>
                    `;
                    break;
                case 'report':
                    content.innerHTML = `
                        <h2>📋 测试报告</h2>
                        <div class="status">
                            ✅ 报告生成功能就绪
                        </div>
                        <h3>报告格式选择</h3>
                        <p>支持多种专业报告格式:</p>
                        <ul>
                            <li>Excel格式 (.xlsx)</li>
                            <li>HTML格式 (.html)</li>
                            <li>CSV格式 (.csv)</li>
                            <li>JSON格式 (.json)</li>
                        </ul>
                        <button class="btn">生成报告</button>
                        <button class="btn">查看历史报告</button>
                    `;
                    break;
            }
        }
    </script>
</body>
</html>
        """
        
        @app.route('/')
        def index():
            return render_template_string(main_template, 
                current_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
        
        @app.route('/api/status')
        def api_status():
            return jsonify({
                'status': 'running',
                'time': datetime.now().isoformat(),
                'version': 'Web 1.0'
            })
        
        # 启动Flask应用
        print("🌐 启动Flask Web服务器...")
        print("✅ 服务器配置:")
        print("   - 主机: localhost")
        print("   - 端口: 8080")
        print("   - 调试模式: 关闭")
        print("💡 请在浏览器中访问: http://localhost:8080")
        print("⏹️ 按 Ctrl+C 停止服务器")
        
        app.run(host='0.0.0.0', port=8080, debug=False)
        
    except Exception as e:
        print(f"❌ Web服务器启动失败: {e}")
        print("💡 请检查端口8080是否被占用")

def main():
    """主函数"""
    print("🎯 Auto_Point Web服务器启动器")
    print("=" * 60)
    
    # 检查工作目录
    current_dir = os.getcwd()
    print(f"📁 工作目录: {current_dir}")
    
    # 启动Web服务器
    start_web_server()

if __name__ == "__main__":
    main()
