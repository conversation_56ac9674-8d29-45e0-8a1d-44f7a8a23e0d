#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复编码中断的SCD文件
尝试恢复部分可用内容
"""

import os
import sys

def fix_encoding_interruption(input_file, output_file=None):
    """修复编码中断问题"""
    if output_file is None:
        name, ext = os.path.splitext(input_file)
        output_file = f"{name}_recovered{ext}"
    
    print(f"🔧 修复编码中断文件: {input_file}")
    print("=" * 60)
    
    try:
        # 读取文件的原始字节
        with open(input_file, 'rb') as f:
            raw_data = f.read()
        
        print(f"📏 原始文件大小: {len(raw_data):,} 字节")
        
        # 方法1: 找到最后一个完整的XML元素
        print("\n🔍 方法1: 查找完整XML结构...")
        
        # 转换为文本，忽略错误
        text = raw_data.decode('utf-8', errors='ignore')
        print(f"📊 忽略错误后文本长度: {len(text):,} 字符")
        
        # 查找最后一个完整的XML结束标签
        last_complete_pos = find_last_complete_xml_element(text)
        
        if last_complete_pos > 0:
            # 截取到最后一个完整元素
            recovered_text = text[:last_complete_pos]
            
            # 确保XML结构完整
            recovered_text = ensure_xml_completeness(recovered_text)
            
            # 保存修复后的文件
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(recovered_text)
            
            print(f"✅ 修复完成: {output_file}")
            print(f"📊 恢复的内容长度: {len(recovered_text):,} 字符")
            print(f"📈 恢复比例: {len(recovered_text)/len(text)*100:.1f}%")
            
            # 验证修复结果
            if verify_xml_structure(output_file):
                print("✅ XML结构验证通过")
                return True
            else:
                print("❌ XML结构验证失败")
                return False
        else:
            print("❌ 未找到完整的XML结构")
            return False
            
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def find_last_complete_xml_element(text):
    """查找最后一个完整的XML元素位置"""
    print("🔍 查找最后一个完整的XML元素...")
    
    # 查找常见的IEC 61850元素结束标签
    end_tags = [
        '</SCL>',
        '</Substation>',
        '</VoltageLevel>',
        '</Bay>',
        '</LDevice>',
        '</LN>',
        '</DOI>',
        '</DAI>',
        '</Val>',
        '</Text>',
        '</Private>'
    ]
    
    last_pos = 0
    found_tag = None
    
    for tag in end_tags:
        pos = text.rfind(tag)
        if pos > last_pos:
            last_pos = pos + len(tag)
            found_tag = tag
    
    if found_tag:
        print(f"✅ 找到最后完整元素: {found_tag} (位置: {last_pos:,})")
    else:
        print("❌ 未找到完整的XML元素")
    
    return last_pos

def ensure_xml_completeness(text):
    """确保XML结构完整"""
    print("🔧 确保XML结构完整...")
    
    # 检查是否以</SCL>结尾
    if not text.rstrip().endswith('</SCL>'):
        print("⚠️ 缺少</SCL>结束标签，尝试添加...")
        
        # 查找最后一个开放的标签
        open_tags = []
        import re
        
        # 简单的标签匹配
        tag_pattern = r'<(/?)(\w+)[^>]*>'
        matches = re.findall(tag_pattern, text)
        
        for is_closing, tag_name in matches:
            if is_closing:
                if open_tags and open_tags[-1] == tag_name:
                    open_tags.pop()
            else:
                if tag_name not in ['Header', 'History', 'Hitem']:  # 自闭合标签
                    open_tags.append(tag_name)
        
        # 添加缺失的结束标签
        for tag in reversed(open_tags):
            text += f'</{tag}>\n'
            print(f"   添加结束标签: </{tag}>")
    
    return text

def verify_xml_structure(file_path):
    """验证XML结构"""
    try:
        import xml.etree.ElementTree as ET
        tree = ET.parse(file_path)
        root = tree.getroot()
        print(f"✅ XML验证成功，根元素: {root.tag}")
        return True
    except ET.ParseError as e:
        print(f"❌ XML验证失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证异常: {e}")
        return False

def main():
    """主函数"""
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    else:
        input_file = input("请输入要修复的文件路径: ").strip()
    
    if not input_file:
        print("❌ 未指定文件路径")
        return
    
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        return
    
    print("🎯 编码中断文件修复工具")
    print("=" * 60)
    
    success = fix_encoding_interruption(input_file)
    
    if success:
        print("\n🎉 修复成功！")
        print("💡 建议:")
        print("   1. 验证修复后的文件内容")
        print("   2. 与原始文件对比检查")
        print("   3. 在对点机中测试加载")
    else:
        print("\n❌ 修复失败")
        print("💡 建议:")
        print("   1. 检查原始文件是否完整")
        print("   2. 尝试从其他来源获取文件")
        print("   3. 使用专业的文件恢复工具")

if __name__ == "__main__":
    main()
