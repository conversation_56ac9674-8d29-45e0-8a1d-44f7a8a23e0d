#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查子站模拟器运行状态
验证子站模拟器是否正常启动和运行
"""

import socket
import time
import psutil
import os
from datetime import datetime

def check_process_running():
    """检查子站模拟器进程是否运行"""
    print("🔍 检查子站模拟器进程状态")
    print("-" * 50)
    
    substation_processes = []
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'create_time']):
            try:
                if proc.info['cmdline']:
                    cmdline = ' '.join(proc.info['cmdline'])
                    if 'substation_optimized.py' in cmdline:
                        create_time = datetime.fromtimestamp(proc.info['create_time'])
                        substation_processes.append({
                            'pid': proc.info['pid'],
                            'name': proc.info['name'],
                            'cmdline': cmdline,
                            'create_time': create_time,
                            'status': proc.status()
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
    except Exception as e:
        print(f"   ⚠️ 进程检查失败: {e}")
        return False
    
    if substation_processes:
        print(f"   ✅ 找到 {len(substation_processes)} 个子站模拟器进程:")
        for proc in substation_processes:
            print(f"      PID: {proc['pid']}")
            print(f"      状态: {proc['status']}")
            print(f"      启动时间: {proc['create_time'].strftime('%H:%M:%S')}")
            print(f"      命令行: {proc['cmdline']}")
        return True
    else:
        print("   ❌ 未找到子站模拟器进程")
        return False

def check_port_listening():
    """检查子站模拟器端口是否监听"""
    print("\n🔍 检查端口监听状态")
    print("-" * 50)
    
    # 检查常用端口
    ports_to_check = [102, 2404, 8080, 8888]
    listening_ports = []
    
    for port in ports_to_check:
        try:
            # 尝试连接端口
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"   ✅ 端口 {port} 正在监听")
                listening_ports.append(port)
            else:
                print(f"   ❌ 端口 {port} 未监听")
        except Exception as e:
            print(f"   ⚠️ 端口 {port} 检查失败: {e}")
    
    return listening_ports

def check_network_connections():
    """检查网络连接状态"""
    print("\n🔍 检查网络连接状态")
    print("-" * 50)
    
    try:
        connections = psutil.net_connections(kind='inet')
        substation_connections = []
        
        for conn in connections:
            if conn.laddr and conn.laddr.port in [102, 2404, 8080, 8888]:
                substation_connections.append(conn)
        
        if substation_connections:
            print(f"   ✅ 找到 {len(substation_connections)} 个相关网络连接:")
            for conn in substation_connections:
                status = conn.status if hasattr(conn, 'status') else 'UNKNOWN'
                print(f"      地址: {conn.laddr.ip}:{conn.laddr.port}")
                print(f"      状态: {status}")
                if conn.raddr:
                    print(f"      远程: {conn.raddr.ip}:{conn.raddr.port}")
        else:
            print("   ❌ 未找到相关网络连接")
        
        return len(substation_connections) > 0
    except Exception as e:
        print(f"   ⚠️ 网络连接检查失败: {e}")
        return False

def test_connection():
    """测试连接子站模拟器"""
    print("\n🔍 测试连接子站模拟器")
    print("-" * 50)
    
    # 尝试连接常用端口
    ports_to_test = [102, 2404, 8080, 8888]
    
    for port in ports_to_test:
        try:
            print(f"   🔄 尝试连接 localhost:{port}...")
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex(('localhost', port))
            
            if result == 0:
                print(f"   ✅ 成功连接到端口 {port}")
                
                # 尝试发送简单数据
                try:
                    test_data = b"TEST_CONNECTION"
                    sock.send(test_data)
                    print(f"   ✅ 数据发送成功")
                except:
                    print(f"   ⚠️ 数据发送失败，但连接正常")
                
                sock.close()
                return True
            else:
                print(f"   ❌ 连接端口 {port} 失败")
            
            sock.close()
        except Exception as e:
            print(f"   ❌ 连接端口 {port} 异常: {e}")
    
    return False

def check_gui_window():
    """检查GUI窗口是否存在"""
    print("\n🔍 检查GUI窗口状态")
    print("-" * 50)
    
    try:
        # 在Windows上检查窗口
        if os.name == 'nt':
            import subprocess
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True)
            if 'python.exe' in result.stdout:
                print("   ✅ 发现Python进程运行中")
                print("   💡 子站模拟器GUI可能已启动")
                return True
            else:
                print("   ❌ 未发现Python进程")
                return False
        else:
            print("   ℹ️ 非Windows系统，跳过GUI窗口检查")
            return True
    except Exception as e:
        print(f"   ⚠️ GUI窗口检查失败: {e}")
        return True  # 不影响整体判断

def main():
    """主检查函数"""
    print("🎯 子站模拟器状态检查")
    print("=" * 80)
    print(f"🕐 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 检查目标: 验证子站模拟器运行状态")
    
    results = {}
    
    # 1. 检查进程状态
    results['进程运行'] = check_process_running()
    
    # 2. 检查端口监听
    listening_ports = check_port_listening()
    results['端口监听'] = len(listening_ports) > 0
    
    # 3. 检查网络连接
    results['网络连接'] = check_network_connections()
    
    # 4. 测试连接
    results['连接测试'] = test_connection()
    
    # 5. 检查GUI窗口
    results['GUI窗口'] = check_gui_window()
    
    # 生成总结
    print("\n" + "=" * 80)
    print("📊 检查结果总结")
    print("=" * 80)
    
    total_checks = len(results)
    passed_checks = sum(1 for result in results.values() if result)
    
    print(f"📈 检查统计:")
    print(f"   🧪 总检查项: {total_checks}")
    print(f"   ✅ 通过检查: {passed_checks}")
    print(f"   ❌ 失败检查: {total_checks - passed_checks}")
    print(f"   📊 通过率: {passed_checks/total_checks*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for check_name, result in results.items():
        status = "✅ 正常" if result else "❌ 异常"
        print(f"   {check_name}: {status}")
    
    # 给出建议
    print(f"\n💡 状态分析:")
    if results['进程运行']:
        print(f"   ✅ 子站模拟器进程正在运行")
    else:
        print(f"   ❌ 子站模拟器进程未运行")
        print(f"   💡 建议: 检查启动命令或重新启动")
    
    if results['端口监听'] or results['连接测试']:
        print(f"   ✅ 子站模拟器网络服务正常")
        if listening_ports:
            print(f"   📡 监听端口: {', '.join(map(str, listening_ports))}")
    else:
        print(f"   ❌ 子站模拟器网络服务异常")
        print(f"   💡 建议: 检查端口配置或防火墙设置")
    
    if results['GUI窗口']:
        print(f"   ✅ GUI界面可能已启动")
        print(f"   💡 建议: 检查桌面是否有子站模拟器窗口")
    
    # 总体状态判断
    if passed_checks >= total_checks * 0.6:  # 60%以上通过认为正常
        print(f"\n🎉 子站模拟器状态: 正常运行")
        print(f"✅ 可以进行对点机连接测试")
    else:
        print(f"\n⚠️ 子站模拟器状态: 可能异常")
        print(f"💡 建议检查启动过程或重新启动")
    
    return passed_checks >= total_checks * 0.6

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✅ 子站模拟器状态检查完成 - 运行正常")
    else:
        print(f"\n❌ 子站模拟器状态检查完成 - 发现问题")
