#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动功能测试脚本
测试新增的手动单点测试功能
"""

import sys
import os
from logic import Point<PERSON>he<PERSON>
from config_parser import parse_point_table

def test_manual_point_checker():
    """测试手动单点检查器"""
    print("=" * 60)
    print("🔧 手动单点测试功能验证")
    print("=" * 60)
    
    # 测试参数
    ip = "127.0.0.1"
    port = 102
    protocol = "IEC 61850"
    
    # 检查点表文件
    point_files = [
        "test_points_fixed.csv",
        "large_points_500.csv"
    ]
    
    available_file = None
    for file_path in point_files:
        if os.path.exists(file_path):
            available_file = file_path
            break
    
    if not available_file:
        print("❌ 未找到测试点表文件")
        print("请确保以下文件之一存在:")
        for file_path in point_files:
            print(f"   - {file_path}")
        return False
    
    print(f"📋 使用点表文件: {available_file}")
    
    try:
        # 解析点表
        points = parse_point_table(available_file)
        print(f"✅ 成功解析点表，包含 {len(points)} 个数据点")
        
        # 创建单点检查器
        checker = PointChecker(ip, port, protocol)
        print(f"✅ 创建单点检查器: {ip}:{port} ({protocol})")
        
        # 测试连接
        print("\n🔌 测试连接...")
        if checker.connect():
            print("✅ 连接成功")
        else:
            print("❌ 连接失败，请确保子站模拟器正在运行")
            return False
        
        # 选择几个测试点进行手动测试
        test_points = points[:5]  # 测试前5个点
        print(f"\n🧪 开始手动测试 {len(test_points)} 个数据点...")
        
        success_count = 0
        for i, point in enumerate(test_points, 1):
            signal_name = point.get('SignalName', f'Point_{i}')
            signal_type = point.get('DataType', '遥测')
            expected_value = point.get('ExpectedValue', '')
            
            print(f"\n[{i}/{len(test_points)}] 测试信号: {signal_name}")
            print(f"   类型: {signal_type}")
            print(f"   期望值: {expected_value}")
            
            # 执行单点测试
            result = checker.check_single_point(point)
            
            if result:
                actual_value = result.get('actual_value', 'N/A')
                is_correct = result.get('is_correct', False)
                error = result.get('error', '')
                
                if error:
                    print(f"   ❌ 测试失败: {error}")
                else:
                    print(f"   实际值: {actual_value}")
                    if is_correct:
                        print(f"   ✅ 结果: 正确")
                        success_count += 1
                    else:
                        print(f"   ❌ 结果: 错误")
            else:
                print(f"   ❌ 测试异常")
        
        # 断开连接
        checker.disconnect()
        print(f"\n📊 测试完成:")
        print(f"   总测试点数: {len(test_points)}")
        print(f"   成功点数: {success_count}")
        print(f"   成功率: {success_count/len(test_points)*100:.1f}%")
        
        return success_count == len(test_points)
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_signal_list_parsing():
    """测试信号列表解析功能"""
    print("\n" + "=" * 60)
    print("📋 信号列表解析功能验证")
    print("=" * 60)
    
    point_files = [
        "test_points_fixed.csv",
        "large_points_500.csv"
    ]
    
    for file_path in point_files:
        if os.path.exists(file_path):
            print(f"\n📁 解析文件: {file_path}")
            
            try:
                points = parse_point_table(file_path)
                print(f"✅ 解析成功，包含 {len(points)} 个数据点")
                
                # 统计信号类型
                type_stats = {}
                for point in points:
                    signal_type = point.get('DataType', '未知')
                    type_stats[signal_type] = type_stats.get(signal_type, 0) + 1
                
                print("📊 信号类型统计:")
                for signal_type, count in type_stats.items():
                    print(f"   {signal_type}: {count} 个")
                
                # 显示前几个信号示例
                print("\n🔍 信号示例 (前5个):")
                for i, point in enumerate(points[:5], 1):
                    signal_name = point.get('SignalName', f'Point_{i}')
                    signal_type = point.get('DataType', '未知')
                    expected_value = point.get('ExpectedValue', '')
                    print(f"   [{i}] {signal_name} ({signal_type}) = {expected_value}")
                
            except Exception as e:
                print(f"❌ 解析失败: {e}")

def main():
    """主测试函数"""
    print("🧪 Auto_Point手动功能测试")
    print("测试新增的手动单点测试功能")
    
    # 测试1: 信号列表解析
    test_signal_list_parsing()
    
    # 测试2: 手动单点检查器
    success = test_manual_point_checker()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 手动功能测试完全成功！")
        print("✅ 信号列表解析正常")
        print("✅ 单点检查器工作正常")
        print("✅ 手动测试功能就绪")
    else:
        print("⚠️  手动功能测试部分失败")
        print("请检查:")
        print("   1. 子站模拟器是否正在运行")
        print("   2. 网络连接是否正常")
        print("   3. 点表文件是否存在")
    
    print("\n💡 使用说明:")
    print("1. 启动子站模拟器: python substation_optimized.py")
    print("2. 启动对点机: python main_optimized.py")
    print("3. 在对点机中:")
    print("   - 选择点表文件")
    print("   - 测试连接")
    print("   - 在手动测试区域选择信号")
    print("   - 点击'测试此点'按钮")
    print("4. 查看手动测试结果")

if __name__ == "__main__":
    main()
