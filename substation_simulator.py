import socket
import threading
import time
import json
import xml.etree.ElementTree as ET
from datetime import datetime
import random
import pandas as pd
import os
import tkinter as tk
from tkinter import filedialog, messagebox
from tkinter import ttk, scrolledtext
from logic import AutoChecker

class SubstationSimulator:
    def __init__(self, host='0.0.0.0', port=102, protocol='IEC 61850', subnet_mask='*************', gateway=''):
        self.host = host
        self.port = port
        self.protocol = protocol
        self.subnet_mask = subnet_mask
        self.gateway = gateway
        self.server_socket = None
        self.clients = []
        self.running = False
        self.connection_status = "未启动"
        
        # 模拟的IED数据点
        self.data_points = {
            'IED1': {
                'Test1': {'value': 100, 'type': '遥测', 'quality': 'good'},
                'Test2': {'value': 200, 'type': '遥测', 'quality': 'good'},
                'Test3': {'value': 1, 'type': '遥信', 'quality': 'good'},
                'Test4': {'value': 0, 'type': '遥信', 'quality': 'good'},
            },
            'IED2': {
                'Test5': {'value': 300, 'type': '遥测', 'quality': 'good'},
                'Test6': {'value': 400, 'type': '遥测', 'quality': 'good'},
                'Test7': {'value': 1, 'type': '遥信', 'quality': 'good'},
                'Test8': {'value': 0, 'type': '遥信', 'quality': 'good'},
            }
        }
        
        # 日志
        self.log_messages = []
        
    def log(self, message):
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_messages.append(log_entry)
        print(log_entry)
        
    def set_network_config(self, host, port, protocol, subnet_mask='*************', gateway=''):
        """设置网络配置"""
        self.host = host
        self.port = port
        self.protocol = protocol
        self.subnet_mask = subnet_mask
        self.gateway = gateway
        self.log(f"网络配置更新: {host}:{port}, 协议: {protocol}, 掩码: {subnet_mask}, 网关: {gateway}")
        
    def get_connection_status(self):
        """获取连接状态"""
        if self.running:
            return f"运行中 - {self.host}:{self.port} ({self.protocol})"
        else:
            return "未启动"
        
    def test_connection(self):
        """测试网络连接"""
        try:
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.settimeout(3)
            test_socket.connect((self.host, self.port))
            test_socket.close()
            return True, "连接测试成功"
        except Exception as e:
            return False, f"连接测试失败: {e}"
        
    def load_point_table(self, file_path):
        """从点表文件加载数据点"""
        try:
            ext = os.path.splitext(file_path)[-1].lower()
            if ext == '.csv':
                df = pd.read_csv(file_path)
            elif ext in ['.xls', '.xlsx']:
                df = pd.read_excel(file_path)
            else:
                raise ValueError("仅支持CSV或Excel格式的点表文件")
            # 清空现有数据点
            self.data_points = {}
            # 解析点表数据
            for _, row in df.iterrows():
                ied_name = str(row.get('IED', 'IED1'))
                point_name = str(row.get('SignalName', row.get('点名', '')))
                expected_value = row.get('ExpectedValue', row.get('期望值', 0))
                point_type = str(
                    row.get('Type', row.get('DataType', row.get('类型', '遥测')))
                )
                if point_type.strip() == '遥信':
                    try:
                        # 处理多种遥信值格式
                        if isinstance(expected_value, str):
                            expected_str = expected_value.strip().lower()
                            if expected_str in ['true', '1', 'on', 'yes']:
                                value = True
                            elif expected_str in ['false', '0', 'off', 'no']:
                                value = False
                            else:
                                # 尝试转换为数字再判断
                                value = bool(int(float(expected_value)))
                        else:
                            # 数字类型直接转换
                            value = bool(int(float(expected_value)))
                    except Exception:
                        # 默认为False
                        value = False
                elif point_type.strip() == '遥控':
                    try:
                        # 遥控点也使用布尔值处理，与遥信点相同
                        if isinstance(expected_value, str):
                            expected_str = expected_value.strip().lower()
                            if expected_str in ['true', '1', 'on', 'yes']:
                                value = True
                            elif expected_str in ['false', '0', 'off', 'no']:
                                value = False
                            else:
                                # 尝试转换为数字再判断
                                value = bool(int(float(expected_value)))
                        else:
                            # 数字类型直接转换
                            value = bool(int(float(expected_value)))
                    except Exception:
                        # 默认为False
                        value = False
                else:
                    try:
                        value = float(expected_value)
                    except Exception:
                        value = 0.0
                if ied_name and point_name:
                    if ied_name not in self.data_points:
                        self.data_points[ied_name] = {}
                    self.data_points[ied_name][point_name] = {
                        'value': value,
                        'type': point_type,
                        'quality': 'good'
                    }
            self.log(f"成功加载点表: {file_path}, 共{len(self.data_points)}个IED, {sum(len(points) for points in self.data_points.values())}个数据点")
            # 调试：打印所有点的value和expectedValue
            for ied, points in self.data_points.items():
                for name, data in points.items():
                    if data['type'].strip() in ['遥信', '遥控']:
                        print(f"[调试] {ied}.{name}: value={data['value']} ({type(data['value'])}), type={data['type']}")
                        self.log(f"[调试] {data['type']}点加载: {ied}.{name} = {data['value']} ({type(data['value'])})")
            return True
        except Exception as e:
            self.log(f"加载点表失败: {e}")
            return False
        
    def start_server(self):
        """启动子站服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            self.running = True
            self.connection_status = "运行中"
            
            self.log(f"子站服务器启动成功 - {self.host}:{self.port} ({self.protocol})")
            self.log("等待对点机连接...")
            
            # 启动数据更新线程
            data_thread = threading.Thread(target=self.update_data_points)
            data_thread.daemon = True
            data_thread.start()
            
            while self.running:
                try:
                    client_socket, client_address = self.server_socket.accept()
                    self.log(f"对点机连接: {client_address}")
                    
                    client_thread = threading.Thread(
                        target=self.handle_client, 
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                    self.clients.append(client_socket)
                    
                except Exception as e:
                    if self.running:
                        self.log(f"接受连接异常: {e}")
                        
        except Exception as e:
            self.log(f"服务器启动失败: {e}")
            self.connection_status = "启动失败"
            
    def update_data_points(self):
        """定期更新数据点值，保持与点表一致，不再模拟波动"""
        while self.running:
            try:
                # 不再修改点值，保持为加载点表时的ExpectedValue
                pass  # 保持所有点的value不变
                time.sleep(5)  # 保持原有定时结构
            except Exception as e:
                self.log(f"数据更新异常: {e}")
                
    def handle_client(self, client_socket, client_address):
        """处理对点机连接"""
        try:
            while self.running:
                data = client_socket.recv(1024)
                if not data:
                    break
                request = data.decode('utf-8', errors='ignore')
                print(f"[调试] handle_client 收到原始请求: {request}")
                self.log(f"[调试] handle_client 收到原始请求: {request}")
                # 根据协议处理请求
                if self.protocol == 'IEC 61850':
                    response = self.process_iec61850_request(request)
                else:  # DL/T 634.5104
                    response = self.process_dlt634_request(request)
                if response:
                    client_socket.send(response.encode('utf-8'))
        except Exception as e:
            self.log(f"客户端处理异常: {e}")
        finally:
            try:
                client_socket.close()
                if client_socket in self.clients:
                    self.clients.remove(client_socket)
                self.log(f"客户端断开: {client_address}")
            except:
                pass
                
    def process_iec61850_request(self, request):
        print(f"[调试] process_iec61850_request 收到: {request}")
        self.log(f"[调试] process_iec61850_request 收到: {request}")
        try:
            # 解析JSON格式的请求
            if request.strip().startswith('{'):
                request_data = json.loads(request)
                request_type = request_data.get('type', '')
                if request_type == 'read':
                    return self.handle_read_request(request_data)
                elif request_type == 'write':
                    return self.handle_write_request(request_data)
                elif request_type == 'scan':
                    return self.handle_scan_request(request_data)
                elif request_type == 'get_points':
                    return self.handle_get_points_request(request_data)
                else:
                    return json.dumps({'error': '未知请求类型'})
            else:
                # 兼容旧格式
                return self.process_request(request)
        except Exception as e:
            self.log(f"IEC 61850请求处理异常: {e}")
            return json.dumps({'error': str(e)})
            
    def process_dlt634_request(self, request):
        print(f"[调试] process_dlt634_request 收到: {request}")
        self.log(f"[调试] process_dlt634_request 收到: {request}")
        try:
            # DL/T 634.5104使用不同的请求格式
            if 'READ' in request.upper():
                return self.handle_read_request({'data': {'point_name': request.split()[-1]}})
            elif 'WRITE' in request.upper():
                parts = request.split()
                if len(parts) >= 3:
                    point_name = parts[1]
                    value = parts[2].split('=')[-1]
                    return self.handle_write_request({'data': {'point_name': point_name, 'value': value}})
            elif 'SCAN' in request.upper():
                return self.handle_scan_request({})
            else:
                return self.get_all_data_points()
        except Exception as e:
            self.log(f"DL/T 634.5104请求处理异常: {e}")
            return json.dumps({'error': str(e)})
                
    def process_request(self, request):
        print(f"[调试] process_request 收到: {request}")
        self.log(f"[调试] process_request 收到: {request}")
        try:
            # 简单的请求解析（实际应用中需要解析IEC 61850 MMS协议）
            if 'READ' in request.upper():
                # 读取数据点请求
                return self.handle_read_request({'data': {'point_name': request.split()[-1]}})
            elif 'WRITE' in request.upper():
                # 写入数据点请求
                parts = request.split()
                if len(parts) >= 3:
                    point_name = parts[1]
                    value = parts[2].split('=')[-1]
                    return self.handle_write_request({'data': {'point_name': point_name, 'value': value}})
            elif 'SCAN' in request.upper():
                # 扫描数据点请求
                return self.handle_scan_request({})
            else:
                # 默认返回所有数据点
                return self.get_all_data_points()
        except Exception as e:
            self.log(f"请求处理异常: {e}")
            return json.dumps({'error': str(e)})
            
    def handle_read_request(self, request_data):
        """处理读取请求"""
        point_name = request_data.get('data', {}).get('point_name', '')
        # 调试日志：收到的点名
        print(f"[调试] handle_read_request 收到点名: '{point_name}'")
        self.log(f"[调试] handle_read_request 收到点名: '{point_name}'")
        # 调试日志：已加载所有点名（前10个）
        loaded_names = []
        for ied_name, points in self.data_points.items():
            loaded_names.extend(list(points.keys()))
        print(f"[调试] 已加载点名总数: {len(loaded_names)}，前10个: {loaded_names[:10]}")
        self.log(f"[调试] 已加载点名总数: {len(loaded_names)}，前10个: {loaded_names[:10]}")
        # 查找数据点
        for ied_name, points in self.data_points.items():
            if point_name in points:
                point_data = points[point_name]
                # 修正：遥信/遥控点读取时始终为布尔型
                if point_data['type'].strip() in ['遥信', '遥控']:
                    # 确保返回的是真正的布尔值，而不是数字
                    if isinstance(point_data['value'], bool):
                        value = point_data['value']
                    else:
                        # 如果存储的不是布尔值，转换为布尔值
                        try:
                            value = bool(int(point_data['value']))
                        except:
                            value = bool(point_data['value'])
                else:
                    value = point_data['value']
                print(f"[调试] 命中点名: '{point_name}' in IED: '{ied_name}'，原始值: {point_data['value']} ({type(point_data['value'])}), 返回值: {value} ({type(value)})")
                self.log(f"[调试] 遥信读取: '{point_name}' 原始值: {point_data['value']}, 返回值: {value}")
                response = {
                    'status': 'success',
                    'data': {
                        'point_name': point_name,
                        'value': value,
                        'type': point_data['type'],
                        'quality': point_data['quality'],
                        'timestamp': datetime.now().isoformat()
                    }
                }
                return json.dumps(response, ensure_ascii=False)
        # 尝试strip后再查找
        alt_name = point_name.strip()
        if alt_name != point_name:
            for ied_name, points in self.data_points.items():
                if alt_name in points:
                    point_data = points[alt_name]
                    # 修正：遥信/遥控点读取时始终为布尔型
                    if point_data['type'].strip() in ['遥信', '遥控']:
                        # 确保返回的是真正的布尔值，而不是数字
                        if isinstance(point_data['value'], bool):
                            value = point_data['value']
                        else:
                            # 如果存储的不是布尔值，转换为布尔值
                            try:
                                value = bool(int(point_data['value']))
                            except:
                                value = bool(point_data['value'])
                    else:
                        value = point_data['value']
                    print(f"[调试] strip后命中点名: '{alt_name}' in IED: '{ied_name}'，原始值: {point_data['value']} ({type(point_data['value'])}), 返回值: {value} ({type(value)})")
                    self.log(f"[调试] 遥信读取(strip): '{alt_name}' 原始值: {point_data['value']}, 返回值: {value}")
                    response = {
                        'status': 'success',
                        'data': {
                            'point_name': alt_name,
                            'value': value,
                            'type': point_data['type'],
                            'quality': point_data['quality'],
                            'timestamp': datetime.now().isoformat()
                        }
                    }
                    return json.dumps(response, ensure_ascii=False)
        print(f"[调试] 查找失败: '{point_name}' (strip后: '{alt_name}')")
        self.log(f"[调试] 查找失败: '{point_name}' (strip后: '{alt_name}')")
        return json.dumps({'status': 'error', 'message': f'数据点 {point_name} 不存在'})
            
    def handle_write_request(self, request_data):
        """处理写入请求"""
        data = request_data.get('data', {})
        point_name = data.get('point_name', '')
        value = data.get('value', '')
        
        # 查找并更新数据点
        for ied_name, points in self.data_points.items():
            if point_name in points:
                try:
                    point_type = str(points[point_name].get('type', '')).strip()
                    if point_type in ['遥信', '遥控']:
                        # 支持多种格式的布尔值写入
                        if isinstance(value, bool):
                            points[point_name]['value'] = value
                        elif isinstance(value, str):
                            value_str = value.strip().lower()
                            if value_str in ['true', '1', 'on', 'yes']:
                                points[point_name]['value'] = True
                            elif value_str in ['false', '0', 'off', 'no']:
                                points[point_name]['value'] = False
                            else:
                                # 尝试转换为数字再判断
                                points[point_name]['value'] = bool(int(float(value)))
                        else:
                            # 数字类型直接转换
                            points[point_name]['value'] = bool(int(float(value)))
                    else:
                        points[point_name]['value'] = float(value)
                    response = {
                        'status': 'success',
                        'message': f'数据点 {point_name} 写入成功: {value}'
                    }
                    return json.dumps(response, ensure_ascii=False)
                except ValueError:
                    return json.dumps({'status': 'error', 'message': '无效的数值'})
        
        return json.dumps({'status': 'error', 'message': f'数据点 {point_name} 不存在'})
            
    def handle_scan_request(self, request_data):
        """处理扫描请求"""
        response = {
            'status': 'success',
            'data': {
                'points': self.get_all_data_points_dict()
            }
        }
        return json.dumps(response, ensure_ascii=False)
        
    def handle_get_points_request(self, request_data):
        """处理获取点表请求"""
        response = {
            'status': 'success',
            'data': {
                'points': self.get_all_data_points_dict()
            }
        }
        return json.dumps(response, ensure_ascii=False)
            
    def get_all_data_points(self):
        """获取所有数据点（旧格式）"""
        result = []
        for ied_name, points in self.data_points.items():
            for point_name, point_data in points.items():
                result.append(f"{point_name}={point_data['value']}")
        return "\n".join(result)
        
    def get_all_data_points_dict(self):
        """获取所有数据点（新格式）"""
        result = []
        for ied_name, points in self.data_points.items():
            for point_name, point_data in points.items():
                result.append({
                    'name': point_name,
                    'value': point_data['value'],
                    'type': point_data['type'],
                    'quality': point_data['quality'],
                    'ied': ied_name,
                    'timestamp': datetime.now().isoformat()
                })
        return result
        
    def stop_server(self):
        """停止子站服务器"""
        self.running = False
        self.connection_status = "已停止"
        
        # 关闭所有客户端连接
        for client in self.clients:
            try:
                client.close()
            except:
                pass
        self.clients.clear()
        
        # 关闭服务器socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
            
        self.log("子站服务器已停止")
        
    def get_logs(self):
        """获取日志"""
        return self.log_messages
        
    def add_data_point(self, ied_name, point_name, value, point_type='遥测'):
        """添加数据点"""
        if ied_name not in self.data_points:
            self.data_points[ied_name] = {}
            
        self.data_points[ied_name][point_name] = {
            'value': value,
            'type': point_type,
            'quality': 'good'
        }
        self.log(f"添加数据点: {ied_name}.{point_name} = {value} ({point_type})")

class SubstationGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("模拟子站 - 网络配置版")
        self.root.geometry("1000x700")
        
        # 创建模拟子站实例
        self.substation = SubstationSimulator()
        self.server_thread = None
        
        self.create_widgets()
        
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # 网络配置区域
        network_frame = ttk.LabelFrame(main_frame, text="网络配置", padding="10")
        network_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # IP地址
        ttk.Label(network_frame, text="监听IP:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.ip_var = tk.StringVar(value="0.0.0.0")
        self.ip_entry = ttk.Entry(network_frame, textvariable=self.ip_var, width=15)
        self.ip_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        
        # 端口
        ttk.Label(network_frame, text="端口:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.port_var = tk.StringVar(value="102")
        self.port_entry = ttk.Entry(network_frame, textvariable=self.port_var, width=10)
        self.port_entry.grid(row=0, column=3, sticky=tk.W, padx=(0, 20))
        
        # 协议选择
        ttk.Label(network_frame, text="协议:").grid(row=0, column=4, sticky=tk.W, padx=(0, 5))
        self.protocol_var = tk.StringVar(value="IEC 61850")
        self.protocol_combo = ttk.Combobox(network_frame, textvariable=self.protocol_var, 
                                          values=["IEC 61850", "DL/T 634.5104"], 
                                          state="readonly", width=15)
        self.protocol_combo.grid(row=0, column=5, sticky=tk.W, padx=(0, 20))
        
        # 子网掩码
        ttk.Label(network_frame, text="子网掩码:").grid(row=0, column=6, sticky=tk.W, padx=(0, 5))
        self.subnet_mask_var = tk.StringVar(value="*************")
        self.subnet_mask_entry = ttk.Entry(network_frame, textvariable=self.subnet_mask_var, width=15)
        self.subnet_mask_entry.grid(row=0, column=7, sticky=tk.W, padx=(0, 20))
        
        # 网关
        ttk.Label(network_frame, text="网关:").grid(row=0, column=8, sticky=tk.W, padx=(0, 5))
        self.gateway_var = tk.StringVar(value="")
        self.gateway_entry = ttk.Entry(network_frame, textvariable=self.gateway_var, width=15)
        self.gateway_entry.grid(row=0, column=9, sticky=tk.W, padx=(0, 20))
        
        # 连接状态
        ttk.Label(network_frame, text="状态:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(10, 0))
        self.status_label = ttk.Label(network_frame, text="未启动", foreground="red")
        self.status_label.grid(row=1, column=1, sticky=tk.W, padx=(0, 20), pady=(10, 0))
        
        # 测试连接按钮
        self.test_btn = ttk.Button(network_frame, text="测试连接", command=self.test_connection)
        self.test_btn.grid(row=1, column=2, sticky=tk.W, padx=(0, 20), pady=(10, 0))
        
        # 控制区域
        control_frame = ttk.LabelFrame(main_frame, text="控制", padding="10")
        control_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 启动/停止按钮
        self.start_btn = ttk.Button(control_frame, text="启动子站", command=self.start_substation)
        self.start_btn.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_btn = ttk.Button(control_frame, text="停止子站", command=self.stop_substation, state="disabled")
        self.stop_btn.grid(row=0, column=1, padx=(0, 10))
        
        # 点表操作
        ttk.Button(control_frame, text="加载点表", command=self.load_point_table).grid(row=0, column=2, padx=(0, 10))
        
        # 数据点管理区域
        data_frame = ttk.LabelFrame(main_frame, text="数据点管理", padding="10")
        data_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 添加数据点
        ttk.Label(data_frame, text="IED名称:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.ied_var = tk.StringVar(value="IED1")
        self.ied_entry = ttk.Entry(data_frame, textvariable=self.ied_var, width=10)
        self.ied_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))
        
        ttk.Label(data_frame, text="点名:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.point_var = tk.StringVar()
        self.point_entry = ttk.Entry(data_frame, textvariable=self.point_var, width=10)
        self.point_entry.grid(row=0, column=3, sticky=tk.W, padx=(0, 10))
        
        ttk.Label(data_frame, text="值:").grid(row=0, column=4, sticky=tk.W, padx=(0, 5))
        self.value_var = tk.StringVar(value="0")
        self.value_entry = ttk.Entry(data_frame, textvariable=self.value_var, width=10)
        self.value_entry.grid(row=0, column=5, sticky=tk.W, padx=(0, 10))
        
        ttk.Label(data_frame, text="类型:").grid(row=0, column=6, sticky=tk.W, padx=(0, 5))
        self.type_var = tk.StringVar(value="遥测")
        self.type_combo = ttk.Combobox(data_frame, textvariable=self.type_var, 
                                      values=["遥测", "遥信"], state="readonly", width=8)
        self.type_combo.grid(row=0, column=7, sticky=tk.W, padx=(0, 10))
        
        ttk.Button(data_frame, text="添加", command=self.add_point).grid(row=0, column=8, padx=(0, 10))
        ttk.Button(data_frame, text="刷新显示", command=self.update_data_display).grid(row=0, column=9)
        
        # 数据显示区域
        display_frame = ttk.LabelFrame(main_frame, text="数据点显示", padding="10")
        display_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        display_frame.columnconfigure(0, weight=1)
        display_frame.rowconfigure(0, weight=1)
        
        # 数据点表格
        columns = ('IED', '点名', '值', '类型', '质量')
        self.data_tree = ttk.Treeview(display_frame, columns=columns, show='headings', height=10)
        
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=100)
        
        self.data_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        scrollbar = ttk.Scrollbar(display_frame, orient=tk.VERTICAL, command=self.data_tree.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.data_tree.configure(yscrollcommand=scrollbar.set)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="日志", padding="10")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 初始化显示
        self.update_data_display()
        
    def test_connection(self):
        """测试网络连接"""
        try:
            host = self.ip_var.get()
            port = int(self.port_var.get())
            success, message = self.substation.test_connection()
            if success:
                messagebox.showinfo("连接测试", f"连接测试成功\n{host}:{port}")
            else:
                messagebox.showerror("连接测试", f"连接测试失败\n{message}")
        except Exception as e:
            messagebox.showerror("错误", f"测试连接时出错: {e}")
        
    def start_substation(self):
        """启动子站"""
        try:
            host = self.ip_var.get()
            port = int(self.port_var.get())
            protocol = self.protocol_var.get()
            subnet_mask = self.subnet_mask_var.get()
            gateway = self.gateway_var.get()
            
            # 更新子站配置
            self.substation.set_network_config(host, port, protocol, subnet_mask, gateway)
            
            # 启动服务器线程
            self.server_thread = threading.Thread(target=self.substation.start_server)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            # 更新界面状态
            self.start_btn.config(state="disabled")
            self.stop_btn.config(state="normal")
            self.status_label.config(text="运行中", foreground="green")
            
            self.log("子站启动成功")
            
        except Exception as e:
            messagebox.showerror("错误", f"启动子站失败: {e}")
            self.log(f"启动失败: {e}")
        
    def stop_substation(self):
        """停止子站"""
        try:
            self.substation.stop_server()
            
            # 更新界面状态
            self.start_btn.config(state="normal")
            self.stop_btn.config(state="disabled")
            self.status_label.config(text="已停止", foreground="red")
            
            self.log("子站已停止")
            
        except Exception as e:
            messagebox.showerror("错误", f"停止子站失败: {e}")
            self.log(f"停止失败: {e}")
        
    def load_point_table(self):
        """加载点表文件"""
        file_path = filedialog.askopenfilename(
            title="选择点表文件",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xls;*.xlsx"), ("所有文件", "*.*")]
        )
        
        if file_path:
            if self.substation.load_point_table(file_path):
                self.update_data_display()
                messagebox.showinfo("成功", "点表加载成功")
            else:
                messagebox.showerror("错误", "点表加载失败")
        
    def add_point(self):
        """添加数据点"""
        try:
            ied_name = self.ied_var.get()
            point_name = self.point_var.get()
            value = float(self.value_var.get())
            point_type = self.type_var.get()
            
            if not ied_name or not point_name:
                messagebox.showwarning("警告", "请输入IED名称和点名")
                return
                
            self.substation.add_data_point(ied_name, point_name, value, point_type)
            self.update_data_display()
            
            # 清空输入框
            self.point_var.set("")
            self.value_var.set("0")
            
        except ValueError:
            messagebox.showerror("错误", "请输入有效的数值")
        except Exception as e:
            messagebox.showerror("错误", f"添加数据点失败: {e}")
        
    def update_data_display(self):
        """更新数据显示"""
        # 清空现有数据
        for item in self.data_tree.get_children():
            self.data_tree.delete(item)
        
        # 添加数据点
        for ied_name, points in self.substation.data_points.items():
            for point_name, point_data in points.items():
                self.data_tree.insert('', 'end', values=(
                    ied_name,
                    point_name,
                    point_data['value'],
                    point_data['type'],
                    point_data['quality']
                ))
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        
    def run(self):
        """运行GUI"""
        self.root.mainloop()

if __name__ == "__main__":
    gui = SubstationGUI()
    gui.run() 
