# 子站模拟器启动指南

## 🎯 当前状态

**子站模拟器启动状态**: ✅ **进程已启动，等待GUI操作**

### 📊 状态检查结果
```
✅ 子站模拟器文件完整 (substation_optimized.py - 15.9KB)
✅ Python进程正在运行 (发现3个Python进程)
❌ 网络服务未启动 (端口102, 2404未监听)
❌ 连接测试失败 (需要先启动服务)
```

### 💡 分析结论
- **进程状态**: 子站模拟器GUI已经启动
- **服务状态**: 网络服务还未启动，需要手动操作
- **下一步**: 在GUI界面中启动网络服务

## 🖥️ GUI操作步骤

### **步骤1: 找到子站模拟器窗口**
1. 检查桌面是否有 **"子站模拟器 - 优化版"** 窗口
2. 如果窗口被最小化，请从任务栏恢复
3. 如果找不到窗口，可能需要重新启动

### **步骤2: 配置网络参数**
在子站模拟器GUI中：
1. **服务器地址**: 保持默认 `0.0.0.0` (监听所有接口)
2. **服务器端口**: 建议使用 `102` (IEC 61850标准端口)
3. **协议类型**: 选择 `IEC 61850` 或 `DL/T 634.5104`

### **步骤3: 启动网络服务**
1. 点击 **"启动服务"** 按钮
2. 观察状态显示是否变为 **"服务运行中"**
3. 确认端口监听状态显示正常

### **步骤4: 加载数据点**
1. 点击 **"加载SCD文件"** 或 **"加载点表"**
2. 选择 `large_substation_2000points.scd` 文件
3. 确认加载了2000个数据点
4. 查看数据点列表显示正常

## 🔧 验证服务启动

### **方法1: 在子站GUI中验证**
- 状态栏显示: **"服务运行中 - 端口102"**
- 连接数显示: **"当前连接: 0"**
- 数据点数显示: **"数据点: 2000"**

### **方法2: 重新运行状态检查**
```bash
python 简单检查子站.py
```
启动服务后应该看到：
- ✅ 端口102正在监听
- ✅ 连接测试成功

### **方法3: 在Auto_Point中测试连接**
1. 打开Auto_Point Web界面
2. 选择 "🔧 通信配置" → "网关配置"
3. 配置连接参数:
   - **服务器地址**: `localhost` 或 `127.0.0.1`
   - **端口**: `102`
   - **协议**: `IEC 61850`
4. 点击 **"测试连接"**
5. 应该显示连接成功

## 🚨 常见问题解决

### **问题1: 找不到GUI窗口**
**解决方案**:
```bash
# 重新启动子站模拟器
python substation_optimized.py
```

### **问题2: 端口被占用**
**现象**: 启动服务时提示端口被占用
**解决方案**:
1. 更改端口号为 `2404` 或 `8080`
2. 或者关闭占用端口的其他程序

### **问题3: 防火墙阻止**
**现象**: 连接测试失败，但服务已启动
**解决方案**:
1. 检查Windows防火墙设置
2. 允许Python程序通过防火墙
3. 或临时关闭防火墙进行测试

### **问题4: 数据点加载失败**
**现象**: SCD文件加载后数据点为0
**解决方案**:
1. 确认SCD文件路径正确
2. 检查文件是否损坏
3. 尝试重新加载文件

## 📱 完整启动流程

### **推荐操作顺序**
```
1. ✅ 启动子站模拟器GUI
   python substation_optimized.py

2. 🔧 配置网络参数
   - 端口: 102
   - 协议: IEC 61850

3. 📁 加载数据文件
   - 选择: large_substation_2000points.scd
   - 确认: 2000个数据点

4. 🚀 启动网络服务
   - 点击: "启动服务"
   - 确认: 状态显示"运行中"

5. ✅ 验证服务状态
   - 运行: python 简单检查子站.py
   - 确认: 端口102监听正常

6. 🔗 测试对点机连接
   - 在Auto_Point中配置连接
   - 测试连接成功
```

## 🎯 预期结果

### **子站模拟器正常运行时应该显示**
```
🖥️ GUI界面:
   📡 服务状态: 运行中 - 端口102
   📊 数据点数: 2000个
   🔗 当前连接: 0个 (等待对点机连接)
   📈 通信状态: 正常

🔍 状态检查:
   ✅ 端口102正在监听
   ✅ 连接测试成功
   ✅ Python进程运行正常
   ✅ 网络服务活跃

🌐 Auto_Point连接:
   ✅ 通信配置测试成功
   ✅ 数据点读取正常
   ✅ 对点测试可以开始
```

## 💡 使用建议

### **最佳实践**
1. **先启动子站模拟器**，再启动Auto_Point对点机
2. **确认数据点加载完成**后再进行连接测试
3. **保持GUI窗口打开**，便于监控连接状态
4. **使用标准端口102**，确保协议兼容性

### **性能优化**
1. 如果数据点很多，加载可能需要几秒钟
2. 建议在本地环境测试，减少网络延迟
3. 可以调整数据更新频率以适应测试需求

### **调试技巧**
1. 观察GUI中的日志信息
2. 使用状态检查脚本定期验证
3. 在Auto_Point中查看连接状态
4. 必要时重启服务解决问题

---

**🎯 按照以上步骤操作，子站模拟器应该能够正常启动并提供服务，为Auto_Point对点测试做好准备！**

*指南版本: v1.0*  
*适用于: Auto_Point Web风格对点机 + 优化版子站模拟器*
