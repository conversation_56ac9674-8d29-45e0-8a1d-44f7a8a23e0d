#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能文件匹配检查
为Auto_Point对点机提供SCD和点表文件的兼容性检查
"""

import os
import xml.etree.ElementTree as ET
import pandas as pd
from datetime import datetime
import re

class FileCompatibilityChecker:
    """文件兼容性检查器"""
    
    def __init__(self):
        self.scd_info = {}
        self.table_info = {}
        self.compatibility_issues = []
    
    def check_scd_file(self, scd_path):
        """检查SCD文件"""
        print(f"🔍 检查SCD文件: {scd_path}")
        
        try:
            # 基本文件检查
            if not os.path.exists(scd_path):
                return False, "SCD文件不存在"
            
            file_size = os.path.getsize(scd_path)
            if file_size == 0:
                return False, "SCD文件为空"
            
            # XML格式检查
            try:
                tree = ET.parse(scd_path)
                root = tree.getroot()
                print(f"✅ XML解析成功")
            except ET.ParseError as e:
                error_msg = str(e)
                if "unbound prefix" in error_msg:
                    return False, f"XML命名空间错误: {error_msg}"
                else:
                    return False, f"XML格式错误: {error_msg}"
            
            # 提取SCD信息
            self.scd_info = self.extract_scd_info(root, scd_path)
            
            # 检查关键元素
            if self.scd_info['ied_count'] == 0:
                return False, "SCD文件中未找到IED设备"
            
            if self.scd_info['logical_node_count'] == 0:
                return False, "SCD文件中未找到逻辑节点"
            
            print(f"✅ SCD文件检查通过")
            return True, "SCD文件格式正确"
            
        except Exception as e:
            return False, f"SCD文件检查异常: {str(e)}"
    
    def check_table_file(self, table_path):
        """检查点表文件"""
        print(f"🔍 检查点表文件: {table_path}")
        
        try:
            # 基本文件检查
            if not os.path.exists(table_path):
                return False, "点表文件不存在"
            
            file_size = os.path.getsize(table_path)
            if file_size == 0:
                return False, "点表文件为空"
            
            # 读取点表文件
            try:
                df = pd.read_csv(table_path, encoding='utf-8-sig')
                print(f"✅ 点表文件读取成功")
            except Exception as e:
                try:
                    df = pd.read_csv(table_path, encoding='gbk')
                    print(f"✅ 点表文件读取成功 (GBK编码)")
                except:
                    return False, f"点表文件读取失败: {str(e)}"
            
            # 提取点表信息
            self.table_info = self.extract_table_info(df, table_path)
            
            # 检查关键列
            required_columns = ['信号名称', '信号类型']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                return False, f"点表文件缺少必要列: {missing_columns}"
            
            if len(df) == 0:
                return False, "点表文件没有数据行"
            
            print(f"✅ 点表文件检查通过")
            return True, "点表文件格式正确"
            
        except Exception as e:
            return False, f"点表文件检查异常: {str(e)}"
    
    def check_compatibility(self, scd_path, table_path):
        """检查SCD和点表文件的兼容性"""
        print(f"🔍 检查文件兼容性...")
        
        self.compatibility_issues = []
        
        # 先检查单个文件
        scd_ok, scd_msg = self.check_scd_file(scd_path)
        table_ok, table_msg = self.check_table_file(table_path)
        
        if not scd_ok:
            self.compatibility_issues.append(f"SCD文件问题: {scd_msg}")
        
        if not table_ok:
            self.compatibility_issues.append(f"点表文件问题: {table_msg}")
        
        if not (scd_ok and table_ok):
            return False, self.compatibility_issues
        
        # 检查兼容性
        self.check_data_consistency()
        self.check_signal_mapping()
        self.check_ied_correspondence()
        
        if self.compatibility_issues:
            return False, self.compatibility_issues
        else:
            return True, ["文件兼容性检查通过"]
    
    def extract_scd_info(self, root, scd_path):
        """提取SCD文件信息"""
        info = {
            'file_path': scd_path,
            'file_size': os.path.getsize(scd_path),
            'ied_count': 0,
            'ied_names': [],
            'logical_node_count': 0,
            'logical_node_types': {},
            'data_object_count': 0,
            'substation_name': '',
            'communication_ips': []
        }
        
        try:
            # 统计IED设备
            ieds = root.findall('.//IED')
            info['ied_count'] = len(ieds)
            info['ied_names'] = [ied.get('name', 'Unknown') for ied in ieds[:10]]  # 只取前10个
            
            # 统计逻辑节点
            lnodes = root.findall('.//LN') + root.findall('.//LN0')
            info['logical_node_count'] = len(lnodes)
            
            for lnode in lnodes:
                ln_class = lnode.get('lnClass', 'Unknown')
                info['logical_node_types'][ln_class] = info['logical_node_types'].get(ln_class, 0) + 1
            
            # 统计数据对象
            dois = root.findall('.//DOI')
            info['data_object_count'] = len(dois)
            
            # 获取变电站名称
            substation = root.find('.//Substation')
            if substation is not None:
                info['substation_name'] = substation.get('name', 'Unknown')
            
            # 获取通信IP
            ips = root.findall('.//P[@type="IP"]')
            info['communication_ips'] = [ip.text for ip in ips if ip.text][:5]  # 只取前5个
            
        except Exception as e:
            print(f"⚠️ 提取SCD信息时出错: {e}")
        
        return info
    
    def extract_table_info(self, df, table_path):
        """提取点表文件信息"""
        info = {
            'file_path': table_path,
            'file_size': os.path.getsize(table_path),
            'total_points': len(df),
            'signal_types': {},
            'ied_names': [],
            'signal_names': [],
            'columns': list(df.columns)
        }
        
        try:
            # 统计信号类型
            if '信号类型' in df.columns:
                info['signal_types'] = df['信号类型'].value_counts().to_dict()
            
            # 提取IED名称
            if 'IED名称' in df.columns:
                info['ied_names'] = df['IED名称'].unique().tolist()[:10]  # 只取前10个
            elif '信号名称' in df.columns:
                # 从信号名称中提取IED名称
                signal_names = df['信号名称'].tolist()
                ied_names = set()
                for name in signal_names:
                    if '_' in name:
                        ied_name = name.split('_')[0]
                        ied_names.add(ied_name)
                info['ied_names'] = list(ied_names)[:10]
            
            # 提取信号名称样本
            if '信号名称' in df.columns:
                info['signal_names'] = df['信号名称'].tolist()[:20]  # 只取前20个
            
        except Exception as e:
            print(f"⚠️ 提取点表信息时出错: {e}")
        
        return info
    
    def check_data_consistency(self):
        """检查数据一致性"""
        # 检查数据点数量是否合理
        scd_data_objects = self.scd_info.get('data_object_count', 0)
        table_points = self.table_info.get('total_points', 0)
        
        if scd_data_objects > 0 and table_points > 0:
            ratio = table_points / scd_data_objects
            if ratio < 0.1 or ratio > 10:
                self.compatibility_issues.append(
                    f"数据点数量差异较大: SCD有{scd_data_objects}个数据对象，点表有{table_points}个数据点"
                )
    
    def check_signal_mapping(self):
        """检查信号映射"""
        # 检查信号类型分布是否合理
        signal_types = self.table_info.get('signal_types', {})
        
        if not signal_types:
            self.compatibility_issues.append("点表文件中未找到信号类型信息")
            return
        
        # 检查是否有基本的信号类型
        basic_types = ['DI', 'AI']
        found_types = [t for t in basic_types if t in signal_types]
        
        if not found_types:
            self.compatibility_issues.append("点表文件中未找到基本信号类型 (DI/AI)")
    
    def check_ied_correspondence(self):
        """检查IED对应关系"""
        scd_ieds = set(self.scd_info.get('ied_names', []))
        table_ieds = set(self.table_info.get('ied_names', []))
        
        if scd_ieds and table_ieds:
            # 检查是否有交集
            common_ieds = scd_ieds.intersection(table_ieds)
            
            if not common_ieds:
                self.compatibility_issues.append(
                    f"SCD和点表中的IED名称不匹配: SCD有{list(scd_ieds)[:3]}..., 点表有{list(table_ieds)[:3]}..."
                )
    
    def generate_compatibility_report(self):
        """生成兼容性报告"""
        report = f"文件兼容性检查报告\n"
        report += f"{'='*60}\n"
        report += f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # SCD文件信息
        report += f"SCD文件信息:\n"
        report += f"  文件路径: {self.scd_info.get('file_path', 'N/A')}\n"
        report += f"  文件大小: {self.scd_info.get('file_size', 0)/1024:.1f}KB\n"
        report += f"  IED设备数: {self.scd_info.get('ied_count', 0)}\n"
        report += f"  逻辑节点数: {self.scd_info.get('logical_node_count', 0)}\n"
        report += f"  数据对象数: {self.scd_info.get('data_object_count', 0)}\n"
        report += f"  变电站名称: {self.scd_info.get('substation_name', 'N/A')}\n\n"
        
        # 点表文件信息
        report += f"点表文件信息:\n"
        report += f"  文件路径: {self.table_info.get('file_path', 'N/A')}\n"
        report += f"  文件大小: {self.table_info.get('file_size', 0)/1024:.1f}KB\n"
        report += f"  数据点数: {self.table_info.get('total_points', 0)}\n"
        report += f"  信号类型: {self.table_info.get('signal_types', {})}\n"
        report += f"  IED设备: {self.table_info.get('ied_names', [])[:5]}\n\n"
        
        # 兼容性问题
        if self.compatibility_issues:
            report += f"⚠️ 发现的问题:\n"
            for i, issue in enumerate(self.compatibility_issues, 1):
                report += f"  {i}. {issue}\n"
        else:
            report += f"✅ 未发现兼容性问题\n"
        
        return report

def main():
    """测试主函数"""
    checker = FileCompatibilityChecker()
    
    # 测试220kVQFB.scd文件
    scd_file = r"222\220kVQFB.scd"
    table_file = "point_table_20250704_163350.csv"  # 假设的点表文件
    
    print("🎯 文件兼容性检查测试")
    print("=" * 60)
    
    # 检查兼容性
    compatible, messages = checker.check_compatibility(scd_file, table_file)
    
    print(f"\n📊 检查结果:")
    if compatible:
        print("✅ 文件兼容性检查通过")
    else:
        print("❌ 发现兼容性问题")
    
    for msg in messages:
        print(f"   - {msg}")
    
    # 生成报告
    report = checker.generate_compatibility_report()
    print(f"\n{report}")

if __name__ == "__main__":
    main()
