#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成2000个数据点的GUI兼容点表文件
专门为子站模拟器GUI优化
"""

import csv
from datetime import datetime

def generate_2000_points():
    """生成2000个数据点"""
    print("🔄 生成2000个数据点的GUI兼容文件...")
    
    # CSV文件头
    fieldnames = ['点号', '信号名称', '信号类型', '数据类型', '期望值', '描述', 'SCD路径', 'IED名称']
    
    # 生成文件名
    filename = f"gui_2000points_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        point_id = 1001
        
        # 生成40个IED，每个IED 50个数据点，总共2000个
        for ied_num in range(1, 41):  # IED_001 到 IED_040
            ied_name = f"IED_{ied_num:03d}"
            
            for point_in_ied in range(50):  # 每个IED 50个点
                
                if point_in_ied < 30:  # 前30个是遥信(DI)
                    signal_type = 'DI'
                    data_type = 'BOOL'
                    value = '0' if point_in_ied % 2 == 0 else '1'
                    
                    if point_in_ied < 10:
                        signal_name = f"{ied_name}_XCBR{point_in_ied+1}_Pos_stVal"
                        description = f"断路器{point_in_ied+1}位置状态"
                        scd_path = f"{ied_name}.LD0.XCBR{point_in_ied+1}.Pos.stVal"
                    elif point_in_ied < 20:
                        signal_name = f"{ied_name}_XCBR{point_in_ied-9}_BlkOpn_stVal"
                        description = f"断路器{point_in_ied-9}分闸闭锁"
                        scd_path = f"{ied_name}.LD0.XCBR{point_in_ied-9}.BlkOpn.stVal"
                    else:
                        signal_name = f"{ied_name}_XCBR{point_in_ied-19}_BlkCls_stVal"
                        description = f"断路器{point_in_ied-19}合闸闭锁"
                        scd_path = f"{ied_name}.LD0.XCBR{point_in_ied-19}.BlkCls.stVal"
                
                elif point_in_ied < 45:  # 中间15个是遥测(AI)
                    signal_type = 'AI'
                    data_type = 'FLOAT'
                    
                    if point_in_ied < 35:
                        value = str(round(220.0 + (point_in_ied % 10) * 0.5, 1))
                        signal_name = f"{ied_name}_MMXU{point_in_ied-29}_TotW_mag_f"
                        description = f"有功功率测量{point_in_ied-29}"
                        scd_path = f"{ied_name}.LD0.MMXU{point_in_ied-29}.TotW.mag.f"
                    elif point_in_ied < 40:
                        value = str(round(50.0 + (point_in_ied % 5) * 0.1, 1))
                        signal_name = f"{ied_name}_MMXU{point_in_ied-34}_Hz_mag_f"
                        description = f"频率测量{point_in_ied-34}"
                        scd_path = f"{ied_name}.LD0.MMXU{point_in_ied-34}.Hz.mag.f"
                    else:
                        value = str(round(220.0 + (point_in_ied % 5) * 2.0, 1))
                        signal_name = f"{ied_name}_MMXU{point_in_ied-39}_PPV_phsA_cVal_mag_f"
                        description = f"A相电压{point_in_ied-39}"
                        scd_path = f"{ied_name}.LD0.MMXU{point_in_ied-39}.PPV.phsA.cVal.mag.f"
                
                else:  # 最后5个是遥调(AO)
                    signal_type = 'AO'
                    data_type = 'BOOL'
                    value = '0'
                    signal_name = f"{ied_name}_XCBR{point_in_ied-44}_Pos_ctlVal"
                    description = f"断路器{point_in_ied-44}控制"
                    scd_path = f"{ied_name}.LD0.XCBR{point_in_ied-44}.Pos.ctlVal"
                
                # 写入数据点
                writer.writerow({
                    '点号': point_id,
                    '信号名称': signal_name,
                    '信号类型': signal_type,
                    '数据类型': data_type,
                    '期望值': value,
                    '描述': description,
                    'SCD路径': scd_path,
                    'IED名称': ied_name
                })
                
                point_id += 1
    
    return filename

def verify_file(filename):
    """验证生成的文件"""
    print(f"🔍 验证文件: {filename}")
    
    try:
        with open(filename, 'r', encoding='utf-8-sig') as f:
            lines = f.readlines()
            total_lines = len(lines)
            data_lines = total_lines - 1  # 减去标题行
        
        print(f"   ✅ 文件生成成功")
        print(f"   📊 总行数: {total_lines} (含标题)")
        print(f"   📋 数据点数: {data_lines}")
        print(f"   📏 文件大小: {round(len(open(filename, 'rb').read()) / 1024, 1)}KB")
        
        # 读取前几行验证格式
        import pandas as pd
        df = pd.read_csv(filename, encoding='utf-8-sig')
        
        print(f"   📝 数据预览:")
        for i, row in df.head(3).iterrows():
            print(f"      {i+1}. 点号:{row['点号']}, 名称:{row['信号名称']}, 类型:{row['信号类型']}")
        
        # 统计信号类型
        signal_counts = df['信号类型'].value_counts()
        print(f"   📈 信号类型分布:")
        for signal_type, count in signal_counts.items():
            type_name = {'DI': '遥信', 'AI': '遥测', 'AO': '遥调'}.get(signal_type, signal_type)
            print(f"      {type_name}({signal_type}): {count}个")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 文件验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 生成2000个数据点的GUI兼容文件")
    print("=" * 60)
    print(f"🕐 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 生成文件
    filename = generate_2000_points()
    
    # 验证文件
    if verify_file(filename):
        print(f"\n✅ 2000点位文件生成完成!")
        print(f"📄 文件名: {filename}")
        print(f"💡 使用方法:")
        print(f"   1. 在子站模拟器GUI中点击'加载点表'")
        print(f"   2. 选择文件: {filename}")
        print(f"   3. 确认显示2000个数据点")
        print(f"   4. 点击'启动服务器'")
        
        return filename
    else:
        print(f"\n❌ 文件生成失败")
        return None

if __name__ == "__main__":
    result = main()
    
    if result:
        print(f"\n🎉 成功生成2000点位文件: {result}")
    else:
        print(f"\n💥 文件生成过程中出现问题")
