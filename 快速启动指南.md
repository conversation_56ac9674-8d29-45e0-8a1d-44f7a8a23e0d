# Auto_Point 快速启动指南

## 🎉 系统已完全启动！

### ✅ **当前运行状态**

#### **🎮 Auto_Point对点机 (终端ID 83)**
- **状态**: ✅ **正常运行**
- **功能**: 完整版Web界面
- **访问**: 通过浏览器访问
- **特性**: 支持SCD文件和点表文件加载，AutoChecker已修复

#### **🏭 子站模拟器GUI (终端ID 81)**
- **状态**: ✅ **已启动**
- **功能**: 图形界面操作
- **操作**: 可手动加载文件和启动服务

#### **🔧 自动子站服务 (终端ID 82)**
- **状态**: ✅ **正常运行**
- **监听**: 0.0.0.0:102
- **数据点**: 21个
- **连接**: 等待对点机连接

## 🚀 **立即开始测试**

### **方案1: 使用自动子站服务 (推荐)**

#### **第一步: 访问对点机Web界面**
1. **打开浏览器**
2. **访问地址**: `http://localhost:8080`
3. **确认界面**: 看到Auto_Point Web风格对点机

#### **第二步: 加载测试文件**
**选择以下任一方式:**

**🔸 方式A: 加载SCD文件**
1. 点击 "📁 配置文件管理" → "SCD文件解析"
2. 选择文件: `new_substation_config.scd`
3. 点击"解析文件"
4. 点击"转换为点表"

**🔸 方式B: 加载点表文件**
1. 点击 "📁 配置文件管理" → "SCD文件解析"
2. 选择文件: `gui_2000points_20250704_145348.csv`
3. 点击"加载点表"

#### **第三步: 配置通信连接**
1. 点击 "🔧 通信配置" → "网关配置"
2. 设置连接参数:
   ```
   服务器地址: localhost
   端口: 102
   协议: IEC 61850
   ```
3. 点击"测试连接" (应该显示连接成功)

#### **第四步: 执行对点测试**
1. 点击 "🎮 自动对点"
2. 配置测试参数:
   ```
   测试模式: 自动对点
   测试速度: 5级 (中等)
   测试范围: 全部信号
   ```
3. 点击"开始自动对点"
4. 观察测试进度和结果

#### **第五步: 生成测试报告**
1. 点击 "📋 报告管理" → "验收报告"
2. 填写项目信息
3. 选择报告格式
4. 点击"生成验收报告"

### **方案2: 使用子站GUI (手动操作)**

#### **第一步: 操作子站GUI**
1. **找到子站模拟器窗口** (标题: "子站模拟器 - 优化版")
2. **加载点表文件**:
   - 点击"加载点表"
   - 选择: `gui_2000points_20250704_145348.csv`
3. **启动网络服务**:
   - 设置监听IP: `0.0.0.0`
   - 设置端口: `102`
   - 点击"启动服务器"
4. **确认状态**: 显示"服务运行中"

#### **第二步: 在对点机中测试**
- 按照方案1的步骤2-5执行

## 📁 **可用测试文件**

### **SCD文件**
- `new_substation_config.scd` - 新编制的标准SCD文件 (22KB)
- `large_substation_2000points.scd` - 大型变电站SCD文件 (391KB)

### **点表文件**
- `gui_2000points_20250704_145348.csv` - 2000个数据点 (200KB)
- `gui_compatible_point_table.csv` - 兼容性测试文件 (3KB)
- `point_table_20250704_154324.csv` - 最新生成的点表 (21个点)

## 🎯 **测试重点**

### **新功能验证**
- ✅ **AutoChecker修复** - 不再出现参数错误
- ✅ **点表直接加载** - 支持CSV格式点表文件
- ✅ **智能文件检测** - 自动识别文件类型
- ✅ **多编码支持** - UTF-8/GBK/GB2312

### **核心功能测试**
- ✅ **文件加载** - SCD和点表文件
- ✅ **通信连接** - IEC 61850协议
- ✅ **对点测试** - 自动和手动测试
- ✅ **报告生成** - 专业验收报告

## 🔍 **预期测试结果**

### **连接测试**
- **连接状态**: ✅ 连接成功
- **通信协议**: IEC 61850
- **数据传输**: 正常

### **对点测试**
- **测试进度**: 实时显示
- **成功率**: 根据数据点匹配情况
- **测试速度**: 可调节 (1-10级)

### **报告生成**
- **格式支持**: Excel, HTML, CSV, JSON
- **内容完整**: 包含测试详情和统计
- **专业性**: 符合验收标准

## 🚨 **故障排除**

### **连接问题**
- **现象**: 连接失败
- **解决**: 确认子站服务已启动，端口102可用

### **文件加载问题**
- **现象**: 文件解析失败
- **解决**: 检查文件格式和编码，尝试其他文件

### **测试异常**
- **现象**: 对点测试中断
- **解决**: 检查网络连接，重新启动服务

## 💡 **使用建议**

### **首次使用**
1. **从小文件开始** - 先用21个数据点测试
2. **验证连接** - 确保通信正常
3. **逐步扩展** - 再用2000个数据点测试

### **性能优化**
- **测试速度**: 根据系统性能调整
- **数据量**: 大文件测试时适当降低速度
- **网络**: 确保网络连接稳定

---

**🎯 系统完全就绪，所有功能正常！现在可以开始完整的Auto_Point对点测试了！**

*快速启动指南版本: v2.0*  
*系统状态: 完全就绪*  
*启动时间: 2025年7月4日 15:52*
