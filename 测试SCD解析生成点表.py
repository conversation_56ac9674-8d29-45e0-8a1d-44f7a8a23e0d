#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SCD文件解析并生成点表
专门测试恢复后的220kVQFB_recovered.scd文件
"""

import xml.etree.ElementTree as ET
import pandas as pd
import os
from datetime import datetime

def test_scd_parsing(scd_file):
    """测试SCD文件解析"""
    print(f"🔍 测试SCD文件解析: {os.path.basename(scd_file)}")
    print("=" * 60)
    
    if not os.path.exists(scd_file):
        print(f"❌ 文件不存在: {scd_file}")
        return False, None
    
    file_size = os.path.getsize(scd_file) / 1024 / 1024
    print(f"📏 文件大小: {file_size:.1f}MB")
    
    try:
        # 尝试解析XML
        print("🔍 尝试XML解析...")
        tree = ET.parse(scd_file)
        root = tree.getroot()
        print(f"✅ XML解析成功，根元素: {root.tag}")
        
        # 提取基本信息
        print("\n📊 SCD文件结构分析:")
        print("-" * 40)
        
        # 统计各种元素
        elements_count = {}
        for elem in root.iter():
            tag = elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag
            elements_count[tag] = elements_count.get(tag, 0) + 1
        
        # 显示主要元素统计
        important_elements = ['IED', 'LDevice', 'LN', 'DOI', 'DAI', 'Val', 'Substation', 'VoltageLevel', 'Bay']
        for elem in important_elements:
            count = elements_count.get(elem, 0)
            if count > 0:
                print(f"  {elem}: {count}个")
        
        print(f"\n📋 总元素数: {sum(elements_count.values())}")
        
        # 尝试提取数据点信息
        print("\n🎯 提取数据点信息:")
        print("-" * 40)
        
        data_points = extract_data_points(root)
        
        if data_points:
            print(f"✅ 成功提取 {len(data_points)} 个数据点")
            
            # 显示前几个数据点
            print("\n📋 数据点示例:")
            for i, point in enumerate(data_points[:5]):
                print(f"  {i+1}. {point['信号名称']} ({point['信号类型']})")
            
            if len(data_points) > 5:
                print(f"  ... 还有 {len(data_points) - 5} 个数据点")
            
            return True, data_points
        else:
            print("❌ 未能提取到数据点信息")
            return False, None
            
    except ET.ParseError as e:
        print(f"❌ XML解析失败: {e}")
        return False, None
    except Exception as e:
        print(f"❌ 解析异常: {e}")
        return False, None

def extract_data_points(root):
    """从SCD文件中提取数据点信息"""
    data_points = []
    
    try:
        # 查找所有IED设备
        ieds = root.findall('.//{http://www.iec.ch/61850/2003/SCL}IED')
        if not ieds:
            # 尝试不带命名空间
            ieds = root.findall('.//IED')
        
        print(f"🔍 找到 {len(ieds)} 个IED设备")
        
        for ied in ieds:
            ied_name = ied.get('name', 'Unknown')
            print(f"  处理IED: {ied_name}")
            
            # 查找逻辑设备
            ldevices = ied.findall('.//{http://www.iec.ch/61850/2003/SCL}LDevice')
            if not ldevices:
                ldevices = ied.findall('.//LDevice')
            
            for ldevice in ldevices:
                ld_inst = ldevice.get('inst', 'Unknown')
                
                # 查找逻辑节点
                lnodes = ldevice.findall('.//{http://www.iec.ch/61850/2003/SCL}LN')
                if not lnodes:
                    lnodes = ldevice.findall('.//LN')
                
                # 也查找LN0
                ln0s = ldevice.findall('.//{http://www.iec.ch/61850/2003/SCL}LN0')
                if not ln0s:
                    ln0s = ldevice.findall('.//LN0')
                
                all_lnodes = lnodes + ln0s
                
                for lnode in all_lnodes:
                    ln_class = lnode.get('lnClass', 'Unknown')
                    ln_inst = lnode.get('inst', '')
                    
                    # 查找数据对象实例
                    dois = lnode.findall('.//{http://www.iec.ch/61850/2003/SCL}DOI')
                    if not dois:
                        dois = lnode.findall('.//DOI')
                    
                    for doi in dois:
                        do_name = doi.get('name', 'Unknown')
                        
                        # 构造信号名称
                        if ln_inst:
                            signal_name = f"{ied_name}_{ld_inst}_{ln_class}{ln_inst}_{do_name}"
                        else:
                            signal_name = f"{ied_name}_{ld_inst}_{ln_class}_{do_name}"
                        
                        # 推断信号类型
                        signal_type = infer_signal_type(ln_class, do_name)
                        
                        # 查找数据属性
                        dais = doi.findall('.//{http://www.iec.ch/61850/2003/SCL}DAI')
                        if not dais:
                            dais = doi.findall('.//DAI')
                        
                        # 获取值信息
                        value = ""
                        for dai in dais:
                            vals = dai.findall('.//{http://www.iec.ch/61850/2003/SCL}Val')
                            if not vals:
                                vals = dai.findall('.//Val')
                            
                            if vals and vals[0].text:
                                value = vals[0].text
                                break
                        
                        data_point = {
                            '信号名称': signal_name,
                            '信号类型': signal_type,
                            'IED名称': ied_name,
                            '逻辑设备': ld_inst,
                            '逻辑节点': f"{ln_class}{ln_inst}",
                            '数据对象': do_name,
                            '当前值': value,
                            '描述': f"{ln_class}类型的{do_name}数据对象"
                        }
                        
                        data_points.append(data_point)
        
        print(f"✅ 总共提取到 {len(data_points)} 个数据点")
        return data_points
        
    except Exception as e:
        print(f"❌ 数据点提取失败: {e}")
        return []

def infer_signal_type(ln_class, do_name):
    """根据逻辑节点类型和数据对象名称推断信号类型"""
    # 常见的信号类型映射
    if 'Pos' in do_name or 'St' in do_name:
        return 'DI'  # 数字输入
    elif 'Mag' in do_name or 'Val' in do_name:
        return 'AI'  # 模拟输入
    elif 'Ctl' in do_name:
        return 'DO'  # 数字输出
    elif ln_class in ['MMXU', 'MMTR']:
        return 'AI'  # 测量类通常是模拟量
    elif ln_class in ['XCBR', 'XSWI']:
        return 'DI'  # 开关类通常是数字量
    else:
        return 'DI'  # 默认为数字输入

def generate_point_table(data_points, output_file=None):
    """生成点表文件"""
    if not data_points:
        print("❌ 没有数据点，无法生成点表")
        return False
    
    if output_file is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"point_table_recovered_{timestamp}.csv"
    
    try:
        # 创建DataFrame
        df = pd.DataFrame(data_points)
        
        # 保存为CSV
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"✅ 点表生成成功: {output_file}")
        print(f"📊 包含 {len(data_points)} 个数据点")
        
        # 统计信号类型
        signal_type_counts = df['信号类型'].value_counts()
        print(f"📋 信号类型分布:")
        for signal_type, count in signal_type_counts.items():
            print(f"  {signal_type}: {count}个")
        
        return True
        
    except Exception as e:
        print(f"❌ 点表生成失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 SCD文件解析测试工具")
    print("=" * 60)
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试恢复后的文件
    scd_file = "220kVQFB_recovered.scd"
    
    if not os.path.exists(scd_file):
        print(f"❌ 文件不存在: {scd_file}")
        print("💡 请确保文件在当前目录中")
        return
    
    # 测试解析
    success, data_points = test_scd_parsing(scd_file)
    
    if success and data_points:
        print("\n🎉 解析成功！")
        
        # 生成点表
        print("\n🔧 生成点表文件...")
        if generate_point_table(data_points):
            print("\n✅ 测试完成：恢复文件可以成功解析并生成点表！")
        else:
            print("\n❌ 点表生成失败")
    else:
        print("\n❌ 解析失败：恢复文件无法正常解析")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
