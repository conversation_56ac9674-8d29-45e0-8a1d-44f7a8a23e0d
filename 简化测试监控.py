#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Auto_Point Web风格对点机简化测试监控
不依赖外部库的轻量级监控和用户指导
"""

import os
import time
import socket
from datetime import datetime

class SimpleMonitor:
    """简化监控器"""
    
    def __init__(self):
        self.start_time = datetime.now()
        
    def print_header(self):
        """打印监控头部"""
        print("🔍 Auto_Point Web风格对点机测试监控")
        print("=" * 60)
        print(f"🕐 监控时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 目标: 验证Web界面功能可操作性")
        print("=" * 60)
    
    def check_network_connectivity(self):
        """检查网络连接"""
        print(f"\n🌐 网络连接检查")
        print("-" * 40)
        
        # 检查关键端口
        ports_to_check = [
            (102, "IEC 61850端口 (子站模拟器)"),
            (80, "HTTP端口"),
            (8080, "备用HTTP端口")
        ]
        
        active_connections = 0
        
        for port, description in ports_to_check:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex(('127.0.0.1', port))
                sock.close()
                
                if result == 0:
                    print(f"   ✅ 端口 {port}: {description} - 活跃")
                    active_connections += 1
                else:
                    print(f"   ❌ 端口 {port}: {description} - 未响应")
            except Exception as e:
                print(f"   ⚠️ 端口 {port}: {description} - 检查异常")
        
        return active_connections
    
    def check_test_files(self):
        """检查测试文件"""
        print(f"\n📁 测试文件检查")
        print("-" * 40)
        
        test_files = [
            ("large_substation_500points.scd", "500点SCD文件"),
            ("large_points_500.csv", "500点CSV文件"),
            ("test_substation.scd", "标准SCD文件"),
            ("demo_scd_points.csv", "演示CSV文件")
        ]
        
        available_files = []
        total_size = 0
        
        for file, description in test_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                total_size += size
                available_files.append((file, size))
                print(f"   ✅ {file}")
                print(f"      📄 {description} ({size:,} bytes)")
            else:
                print(f"   ❌ {file} - {description} (不存在)")
        
        print(f"\n   📊 统计: {len(available_files)}/{len(test_files)} 个文件可用")
        print(f"   📊 总大小: {total_size:,} bytes")
        
        return available_files
    
    def check_program_files(self):
        """检查程序文件"""
        print(f"\n🐍 程序文件检查")
        print("-" * 40)
        
        program_files = [
            ("main_web_functional.py", "功能完整版Web界面"),
            ("main_web_style.py", "展示版Web界面"),
            ("substation_optimized.py", "优化版子站模拟器"),
            ("用户操作指南.md", "用户操作说明")
        ]
        
        for file, description in program_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                mtime = datetime.fromtimestamp(os.path.getmtime(file))
                print(f"   ✅ {file}")
                print(f"      📝 {description} ({size:,} bytes)")
                print(f"      🕐 修改时间: {mtime.strftime('%H:%M:%S')}")
            else:
                print(f"   ❌ {file} - {description} (不存在)")
    
    def show_current_status(self):
        """显示当前状态"""
        print(f"\n📊 当前系统状态")
        print("=" * 60)
        
        # 检查各项状态
        active_ports = self.check_network_connectivity()
        available_files = self.check_test_files()
        self.check_program_files()
        
        # 状态评估
        print(f"\n🎯 状态评估:")
        
        if len(available_files) >= 3:
            print(f"   ✅ 测试文件: 充足 ({len(available_files)} 个)")
        else:
            print(f"   ⚠️ 测试文件: 不足 ({len(available_files)} 个)")
        
        if active_ports > 0:
            print(f"   ✅ 网络连接: 正常 ({active_ports} 个端口活跃)")
        else:
            print(f"   ⚠️ 网络连接: 无活跃端口")
        
        print(f"   ✅ 程序文件: 完整")
        
        return len(available_files), active_ports
    
    def show_user_operations_guide(self):
        """显示用户操作指南"""
        print(f"\n📋 用户操作指南")
        print("=" * 60)
        
        print(f"🎯 Web风格对点机现在可以进行以下操作:")
        print(f"")
        
        print(f"1️⃣ 文件管理功能测试:")
        print(f"   📍 导航路径: 📁 配置文件管理 → SCD文件解析")
        print(f"   🔄 操作步骤:")
        print(f"      • 点击 '选择文件' 按钮")
        print(f"      • 选择 'large_substation_500points.scd'")
        print(f"      • 点击 '解析文件' 按钮")
        print(f"      • 观察解析进度条和结果")
        print(f"   ✅ 预期结果: 显示SCD文件信息和数据预览")
        print(f"")
        
        print(f"2️⃣ 通信配置功能测试:")
        print(f"   📍 导航路径: 🔧 通信配置 → 网关配置")
        print(f"   🔄 操作步骤:")
        print(f"      • 设置IP地址: 127.0.0.1")
        print(f"      • 设置端口: 102")
        print(f"      • 选择协议: IEC 61850")
        print(f"      • 点击 '测试连接' 按钮")
        print(f"      • 点击 '保存配置' 按钮")
        print(f"   ✅ 预期结果: 显示连接状态和配置保存确认")
        print(f"")
        
        print(f"3️⃣ 自动对点功能测试:")
        print(f"   📍 导航路径: 🎮 遥控验收 → 自动对点")
        print(f"   🔄 操作步骤:")
        print(f"      • 选择测试模式: 自动对点")
        print(f"      • 选择测试范围: 全部信号")
        print(f"      • 点击 '开始测试' 按钮")
        print(f"      • 观察测试进度和状态")
        print(f"      • 查看测试结果表格")
        print(f"   ✅ 预期结果: 完整的对点测试流程和结果")
        print(f"")
        
        print(f"4️⃣ 界面交互测试:")
        print(f"   🔄 操作内容:")
        print(f"      • 点击左侧导航菜单切换页面")
        print(f"      • 观察顶部状态指示器变化")
        print(f"      • 测试按钮悬停和点击效果")
        print(f"      • 查看数据表格和图表显示")
        print(f"   ✅ 预期结果: 流畅的界面响应和状态更新")
    
    def show_test_recommendations(self):
        """显示测试建议"""
        print(f"\n💡 测试建议")
        print("=" * 60)
        
        print(f"🎯 推荐测试顺序:")
        print(f"   1. 先测试文件管理 → 验证文件解析功能")
        print(f"   2. 再测试通信配置 → 验证网络连接功能")
        print(f"   3. 然后测试自动对点 → 验证核心业务功能")
        print(f"   4. 最后测试界面交互 → 验证用户体验")
        print(f"")
        
        print(f"📁 推荐测试文件:")
        print(f"   • large_substation_500points.scd (大型SCD文件)")
        print(f"   • large_points_500.csv (大型CSV文件)")
        print(f"   • test_substation.scd (标准测试文件)")
        print(f"")
        
        print(f"🔧 推荐配置参数:")
        print(f"   • IP地址: 127.0.0.1 (本地回环)")
        print(f"   • 端口: 102 (IEC 61850标准端口)")
        print(f"   • 协议: IEC 61850")
        print(f"")
        
        print(f"⚠️ 注意事项:")
        print(f"   • 某些连接测试可能失败(正常现象)")
        print(f"   • 文件解析支持真实文件处理")
        print(f"   • 自动对点使用模拟数据测试")
        print(f"   • 所有操作都有实时进度反馈")
    
    def show_troubleshooting(self):
        """显示故障排除"""
        print(f"\n🔧 故障排除")
        print("=" * 60)
        
        print(f"❓ 常见问题及解决方案:")
        print(f"")
        
        print(f"1. 文件解析失败:")
        print(f"   🔍 检查文件格式是否为SCD/RCD/CSV")
        print(f"   🔍 确认文件没有损坏")
        print(f"   🔍 尝试使用其他测试文件")
        print(f"")
        
        print(f"2. 连接测试失败:")
        print(f"   🔍 确认IP地址和端口设置正确")
        print(f"   🔍 检查子站模拟器是否运行")
        print(f"   🔍 尝试使用其他端口(如80)")
        print(f"")
        
        print(f"3. 界面无响应:")
        print(f"   🔍 等待操作完成(可能有延迟)")
        print(f"   🔍 检查是否有错误弹窗")
        print(f"   🔍 尝试重新启动程序")
        print(f"")
        
        print(f"4. 功能按钮无效:")
        print(f"   🔍 确认已选择必要的文件或配置")
        print(f"   🔍 检查按钮是否处于禁用状态")
        print(f"   🔍 查看状态栏的提示信息")
    
    def run_monitoring(self):
        """运行监控"""
        self.print_header()
        
        try:
            # 显示当前状态
            file_count, port_count = self.show_current_status()
            
            # 显示操作指南
            self.show_user_operations_guide()
            
            # 显示测试建议
            self.show_test_recommendations()
            
            # 显示故障排除
            self.show_troubleshooting()
            
            # 最终总结
            print(f"\n🎉 监控完成")
            print("=" * 60)
            
            current_time = datetime.now()
            duration = current_time - self.start_time
            
            print(f"📊 监控总结:")
            print(f"   ⏱️ 监控耗时: {duration.total_seconds():.1f} 秒")
            print(f"   📁 可用文件: {file_count} 个")
            print(f"   🌐 活跃端口: {port_count} 个")
            print(f"   🕐 完成时间: {current_time.strftime('%H:%M:%S')}")
            print(f"")
            
            if file_count >= 3:
                print(f"✅ 系统状态: 优秀 - 所有功能可正常测试")
                print(f"🎯 建议: 立即开始用户操作测试")
            elif file_count >= 2:
                print(f"⚠️ 系统状态: 良好 - 主要功能可测试")
                print(f"🎯 建议: 可以进行基本功能测试")
            else:
                print(f"❌ 系统状态: 需要改进 - 缺少测试文件")
                print(f"🎯 建议: 检查测试文件是否完整")
            
            print(f"")
            print(f"💡 现在可以在Web界面中进行实际操作测试！")
            
        except Exception as e:
            print(f"\n❌ 监控异常: {str(e)}")

def main():
    """主函数"""
    print("🔍 Auto_Point Web风格对点机简化测试监控")
    print("提供系统状态检查和用户操作指导")
    print()
    
    monitor = SimpleMonitor()
    monitor.run_monitoring()

if __name__ == "__main__":
    main()
