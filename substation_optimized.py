#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版子站模拟器GUI - 简化界面，保留核心功能
"""

import socket
import threading
import json
import pandas as pd
from datetime import datetime
import sys
import os
import tkinter as tk
from tkinter import filedialog, messagebox, ttk

class SubstationOptimized:
    """优化版子站模拟器"""
    
    def __init__(self, host='0.0.0.0', port=102):
        self.host = host
        self.port = port
        self.server_socket = None
        self.clients = []
        self.running = False
        self.data_points = {}
        
        # 创建GUI
        self.root = tk.Tk()
        self.root.title("子站模拟器 - 优化版")
        self.root.geometry("800x600")
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="配置", padding="10")
        config_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 网络配置
        network_frame = ttk.Frame(config_frame)
        network_frame.pack(fill=tk.X)
        
        ttk.Label(network_frame, text="监听IP:").pack(side=tk.LEFT)
        self.ip_var = tk.StringVar(value="0.0.0.0")
        ttk.Entry(network_frame, textvariable=self.ip_var, width=15).pack(side=tk.LEFT, padx=(5, 20))
        
        ttk.Label(network_frame, text="端口:").pack(side=tk.LEFT)
        self.port_var = tk.StringVar(value="102")
        ttk.Entry(network_frame, textvariable=self.port_var, width=8).pack(side=tk.LEFT, padx=(5, 20))

        ttk.Label(network_frame, text="协议:").pack(side=tk.LEFT)
        self.protocol_var = tk.StringVar(value="IEC 61850")
        protocol_combo = ttk.Combobox(network_frame, textvariable=self.protocol_var,
                                     values=["IEC 61850", "DL/T 634.5104"],
                                     state="readonly", width=15)
        protocol_combo.pack(side=tk.LEFT, padx=(5, 0))
        
        # 点表文件
        file_frame = ttk.Frame(config_frame)
        file_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(file_frame, text="加载点表", command=self.load_point_table).pack(side=tk.LEFT)
        self.file_label = ttk.Label(file_frame, text="未加载点表")
        self.file_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 控制区域
        control_frame = ttk.LabelFrame(main_frame, text="控制", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)
        
        self.start_btn = ttk.Button(button_frame, text="启动服务器", command=self.start_server_thread)
        self.start_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(button_frame, text="停止服务器", command=self.stop_server, state="disabled")
        self.stop_btn.pack(side=tk.LEFT)
        
        # 状态显示
        self.status_label = ttk.Label(button_frame, text="状态: 未启动", foreground="red")
        self.status_label.pack(side=tk.RIGHT)
        
        # 数据点显示区域
        data_frame = ttk.LabelFrame(main_frame, text="数据点", padding="10")
        data_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 数据点表格
        columns = ("IED", "信号名称", "值", "类型")
        self.data_tree = ttk.Treeview(data_frame, columns=columns, show="headings", height=10)
        
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=150)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(data_frame, orient=tk.VERTICAL, command=self.data_tree.yview)
        self.data_tree.configure(yscrollcommand=scrollbar.set)
        
        self.data_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="日志", padding="10")
        log_frame.pack(fill=tk.X)
        
        self.log_text = tk.Text(log_frame, height=8, font=("Consolas", 9))
        log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] {message}")
        
    def load_point_table(self):
        """加载点表文件"""
        file_path = filedialog.askopenfilename(
            title="选择点表文件",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xls;*.xlsx")]
        )
        
        if not file_path:
            return
            
        try:
            # 读取文件
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            else:
                df = pd.read_excel(file_path)
            
            self.data_points = {}
            
            for _, row in df.iterrows():
                ied_name = str(row.get('IED', 'Unknown')).strip()
                signal_name = str(row.get('SignalName', '')).strip()
                signal_type = str(row.get('DataType', '遥测')).strip()
                expected_value = row.get('ExpectedValue', 0)
                
                if not signal_name:
                    continue
                    
                if ied_name not in self.data_points:
                    self.data_points[ied_name] = {}
                
                # 根据信号类型设置值
                if signal_type.strip() in ['遥信', '遥控']:
                    value = self._convert_to_bool(expected_value)
                    # 调试输出
                    if str(expected_value).strip().lower() == 'true':
                        print(f"[调试] {signal_name}: 期望值={expected_value} -> 转换值={value} ({type(value)})")
                else:  # 遥测
                    try:
                        value = float(expected_value)
                    except:
                        value = 0.0

                self.data_points[ied_name][signal_name] = {
                    'value': value,
                    'type': signal_type,
                    'quality': 'good'
                }

                # 调试输出存储的数据
                if str(expected_value).strip().lower() in ['true', 'false']:
                    print(f"[存储] {signal_name}: 值={value} ({type(value)}), 类型={signal_type}")
            
            total_points = sum(len(points) for points in self.data_points.values())
            self.file_label.config(text=f"已加载 {len(self.data_points)} 个IED, {total_points} 个数据点")
            self.log(f"成功加载点表: {os.path.basename(file_path)}")
            
            # 更新数据显示
            self.update_data_display()
            
        except Exception as e:
            self.log(f"加载点表失败: {e}")
            messagebox.showerror("错误", f"加载点表失败: {e}")
    
    def _convert_to_bool(self, value):
        """转换值为布尔类型"""
        if isinstance(value, bool):
            return value
        elif isinstance(value, str):
            value_str = value.strip().lower()
            if value_str in ['true', '1', 'on', 'yes']:
                return True
            elif value_str in ['false', '0', 'off', 'no']:
                return False
            else:
                try:
                    return bool(int(float(value)))
                except:
                    return False
        else:
            try:
                return bool(int(float(value)))
            except:
                return False
    
    def update_data_display(self):
        """更新数据显示"""
        # 清空现有数据
        for item in self.data_tree.get_children():
            self.data_tree.delete(item)
        
        # 添加数据点
        for ied_name, points in self.data_points.items():
            for point_name, point_data in points.items():
                self.data_tree.insert('', 'end', values=(
                    ied_name,
                    point_name,
                    point_data['value'],
                    point_data['type']
                ))
    
    def start_server_thread(self):
        """启动服务器线程"""
        if not self.data_points:
            messagebox.showwarning("警告", "请先加载点表文件")
            return
            
        self.host = self.ip_var.get()
        try:
            self.port = int(self.port_var.get())
        except ValueError:
            messagebox.showerror("错误", "端口号必须是数字")
            return

        self.protocol = self.protocol_var.get()
            
        # 启动服务器线程
        server_thread = threading.Thread(target=self.start_server)
        server_thread.daemon = True
        server_thread.start()
        
    def start_server(self):
        """启动TCP服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            self.running = True
            
            # 更新界面状态
            self.root.after(0, lambda: [
                self.start_btn.config(state="disabled"),
                self.stop_btn.config(state="normal"),
                self.status_label.config(text="状态: 运行中", foreground="green")
            ])
            
            self.log(f"服务器启动成功 - {self.host}:{self.port}")
            self.log("等待对点机连接...")
            
            while self.running:
                try:
                    client_socket, client_address = self.server_socket.accept()
                    self.clients.append(client_socket)
                    self.log(f"对点机连接: {client_address}")
                    
                    # 为每个客户端创建处理线程
                    client_thread = threading.Thread(
                        target=self.handle_client, 
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        self.log(f"接受连接异常: {e}")
                        
        except Exception as e:
            self.log(f"启动服务器失败: {e}")
            self.root.after(0, lambda: [
                self.start_btn.config(state="normal"),
                self.stop_btn.config(state="disabled"),
                self.status_label.config(text="状态: 启动失败", foreground="red")
            ])
            
    def handle_client(self, client_socket, client_address):
        """处理客户端请求"""
        try:
            while self.running:
                try:
                    data = client_socket.recv(1024)
                    if not data:
                        break
                        
                    request = data.decode('utf-8', errors='ignore')
                    response = self.process_request(request)
                    
                    if response:
                        client_socket.send(response.encode('utf-8'))
                        
                except socket.timeout:
                    continue
                except ConnectionResetError:
                    self.log(f"客户端 {client_address} 重置连接")
                    break
                except Exception as e:
                    self.log(f"处理客户端请求异常: {e}")
                    break
                    
        except Exception as e:
            self.log(f"客户端处理异常: {e}")
        finally:
            try:
                client_socket.close()
                if client_socket in self.clients:
                    self.clients.remove(client_socket)
                self.log(f"客户端断开: {client_address}")
            except:
                pass
    
    def process_request(self, request):
        """处理请求"""
        try:
            if self.protocol == "IEC 61850":
                return self.process_iec61850_request(request)
            else:  # DL/T 634.5104
                return self.process_dlt634_request(request)

        except Exception as e:
            self.log(f"请求处理异常: {e}")
            return json.dumps({'error': str(e)})

    def process_iec61850_request(self, request):
        """处理IEC 61850请求"""
        try:
            # 简单的READ请求处理
            if 'READ' in request.upper():
                point_name = request.split()[-1]
                return self.handle_read_request(point_name)
            else:
                return json.dumps({'error': '不支持的请求类型'})
        except Exception as e:
            return json.dumps({'error': str(e)})

    def process_dlt634_request(self, request):
        """处理DL/T 634.5104请求"""
        try:
            # 104规约的请求处理
            if 'READ' in request.upper():
                point_name = request.split()[-1]
                return self.handle_read_request(point_name)
            else:
                return json.dumps({'error': '不支持的请求类型'})
        except Exception as e:
            return json.dumps({'error': str(e)})
    
    def handle_read_request(self, point_name):
        """处理读取请求"""
        # 查找数据点
        for ied_name, points in self.data_points.items():
            if point_name in points:
                point_data = points[point_name]

                # 调试输出
                if point_data['type'] in ['遥信', '遥控']:
                    print(f"[返回] {point_name}: 存储值={point_data['value']} ({type(point_data['value'])}), 类型={point_data['type']}")

                response = {
                    'status': 'success',
                    'data': {
                        'point_name': point_name,
                        'value': point_data['value'],
                        'type': point_data['type'],
                        'quality': point_data['quality'],
                        'timestamp': datetime.now().isoformat()
                    }
                }

                json_response = json.dumps(response, ensure_ascii=False)

                # 调试JSON输出
                if point_data['type'] in ['遥信', '遥控']:
                    print(f"[JSON] {point_name}: {json_response}")

                return json_response

        return json.dumps({'status': 'error', 'message': f'数据点 {point_name} 不存在'})
    
    def stop_server(self):
        """停止服务器"""
        self.running = False
        
        # 关闭所有客户端连接
        for client in self.clients:
            try:
                client.close()
            except:
                pass
        self.clients.clear()
        
        # 关闭服务器socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
                
        # 更新界面状态
        self.start_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.status_label.config(text="状态: 已停止", foreground="red")
        
        self.log("服务器已停止")
        
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    simulator = SubstationOptimized()
    simulator.run()

if __name__ == "__main__":
    main()
