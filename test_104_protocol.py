#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DL/T 634.5104规约通信测试脚本
"""

import socket
import json
import time
import pandas as pd
from datetime import datetime

def test_104_communication():
    """测试104规约通信"""
    print("=== DL/T 634.5104规约通信测试 ===")
    
    # 连接参数
    host = "127.0.0.1"
    port = 102
    
    try:
        # 建立连接
        print(f"正在连接到 {host}:{port}...")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect((host, port))
        print("✓ 连接成功")
        
        # 读取测试点表
        print("\n正在读取测试点表...")
        df = pd.read_csv('test_points_104.csv', dtype=str)
        test_points = []
        
        for _, row in df.iterrows():
            signal_name = str(row.get('SignalName', '')).strip()
            expected_value = row.get('ExpectedValue', '')
            data_type = str(row.get('DataType', '遥测')).strip()
            
            if signal_name and not signal_name.startswith('#'):
                test_points.append({
                    'signal_name': signal_name,
                    'expected_value': expected_value,
                    'data_type': data_type
                })
        
        print(f"✓ 加载了 {len(test_points)} 个测试点")
        
        # 测试数据读取
        print("\n开始104规约数据读取测试...")
        print("-" * 80)
        
        success_count = 0
        error_count = 0
        
        for i, point in enumerate(test_points):
            try:
                # 发送READ请求
                request = f"READ {point['signal_name']}"
                sock.send(request.encode('utf-8'))
                
                # 接收响应
                response = sock.recv(1024).decode('utf-8')
                
                try:
                    # 解析JSON响应
                    data = json.loads(response)
                    actual_value = data.get('data', {}).get('value')
                    response_type = data.get('data', {}).get('type', '')
                    
                    # 比较值
                    expected = point['expected_value']
                    is_correct = False
                    
                    if point['data_type'] in ['遥信', '遥控']:
                        # 布尔值比较
                        expected_bool = str(expected).strip().lower() == 'true'
                        actual_bool = bool(actual_value) if actual_value is not None else False
                        is_correct = expected_bool == actual_bool
                    else:
                        # 数值比较
                        try:
                            expected_float = float(expected)
                            actual_float = float(actual_value) if actual_value is not None else 0.0
                            is_correct = abs(expected_float - actual_float) < 0.01
                        except:
                            is_correct = str(expected) == str(actual_value)
                    
                    # 显示结果
                    status = "✓ 正确" if is_correct else "✗ 错误"
                    progress = f"进度: {((i+1)/len(test_points)*100):5.1f}%"
                    
                    print(f"{progress} - {point['signal_name']:25} | "
                          f"期望: {expected:>8} | 实际: {actual_value:>8} | "
                          f"类型: {point['data_type']:4} | {status}")
                    
                    if is_correct:
                        success_count += 1
                    else:
                        error_count += 1
                        
                except json.JSONDecodeError:
                    print(f"进度: {((i+1)/len(test_points)*100):5.1f}% - {point['signal_name']:25} | "
                          f"✗ JSON解析失败: {response}")
                    error_count += 1
                
                # 延时
                time.sleep(0.1)
                
            except Exception as e:
                print(f"进度: {((i+1)/len(test_points)*100):5.1f}% - {point['signal_name']:25} | "
                      f"✗ 通信异常: {e}")
                error_count += 1
        
        print("-" * 80)
        print(f"\n=== 104规约测试结果 ===")
        print(f"总计: {len(test_points)} 个数据点")
        print(f"正确: {success_count} 个")
        print(f"错误: {error_count} 个")
        print(f"正确率: {(success_count/len(test_points)*100):5.1f}%")
        
        if error_count == 0:
            print("🎉 所有数据点测试通过！104规约通信正常")
        else:
            print(f"⚠️  有 {error_count} 个数据点测试失败")
        
        # 关闭连接
        sock.close()
        print("\n✓ 连接已断开")
        
        return success_count, error_count
        
    except ConnectionRefusedError:
        print("✗ 连接被拒绝，请确保子站模拟器已启动")
        return 0, 1
    except Exception as e:
        print(f"✗ 测试异常: {e}")
        return 0, 1

def compare_protocols():
    """比较不同协议的性能"""
    print("\n=== 协议性能对比 ===")
    
    protocols = [
        ("IEC 61850", "test_points_fixed.csv"),
        ("DL/T 634.5104", "test_points_104.csv")
    ]
    
    results = {}
    
    for protocol, point_file in protocols:
        print(f"\n测试 {protocol} 协议...")
        start_time = time.time()
        
        # 这里应该切换子站模拟器的协议设置
        # 实际测试中需要手动切换或通过API切换
        
        success, error = test_104_communication()
        end_time = time.time()
        
        results[protocol] = {
            'success': success,
            'error': error,
            'time': end_time - start_time,
            'accuracy': success / (success + error) * 100 if (success + error) > 0 else 0
        }
    
    print("\n=== 协议对比结果 ===")
    print(f"{'协议':<15} {'正确率':<10} {'耗时(秒)':<10} {'状态'}")
    print("-" * 50)
    
    for protocol, result in results.items():
        status = "✓ 优秀" if result['accuracy'] >= 95 else "⚠ 需优化"
        print(f"{protocol:<15} {result['accuracy']:>6.1f}% {result['time']:>8.2f}s   {status}")

if __name__ == "__main__":
    print("DL/T 634.5104规约通信测试工具")
    print("=" * 50)
    print("使用说明:")
    print("1. 启动子站模拟器")
    print("2. 选择 'DL/T 634.5104' 协议")
    print("3. 加载 test_points_104.csv 点表")
    print("4. 启动服务器")
    print("5. 运行此测试脚本")
    print("=" * 50)
    
    input("按回车键开始测试...")
    
    success, error = test_104_communication()
    
    if error == 0:
        print("\n🎉 104规约测试完全通过！")
    else:
        print(f"\n⚠️  测试完成，发现 {error} 个问题")
    
    print("\n测试完成。")
