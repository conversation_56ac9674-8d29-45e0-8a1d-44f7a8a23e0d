#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精简版子站模拟器
只保留核心功能：点表加载、TCP服务、数据读取
"""

import socket
import threading
import json
import pandas as pd
from datetime import datetime
import sys
import os

class SubstationSimulatorLite:
    """精简版子站模拟器"""
    
    def __init__(self, host='0.0.0.0', port=102):
        self.host = host
        self.port = port
        self.server_socket = None
        self.clients = []
        self.running = False
        self.data_points = {}
        
    def log(self, message):
        """简单日志输出"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {message}")
        
    def load_point_table(self, file_path):
        """加载点表文件"""
        try:
            if not os.path.exists(file_path):
                self.log(f"点表文件不存在: {file_path}")
                return False
                
            # 读取CSV文件
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            else:
                df = pd.read_excel(file_path)
            
            self.data_points = {}
            
            for _, row in df.iterrows():
                ied_name = str(row.get('IEDName', 'Unknown')).strip()
                signal_name = str(row.get('SignalName', '')).strip()
                signal_type = str(row.get('SignalType', '遥测')).strip()
                expected_value = row.get('ExpectedValue', 0)
                
                if not signal_name:
                    continue
                    
                if ied_name not in self.data_points:
                    self.data_points[ied_name] = {}
                
                # 根据信号类型设置值
                if signal_type.strip() == '遥信':
                    value = self._convert_to_bool(expected_value)
                elif signal_type.strip() == '遥控':
                    value = self._convert_to_bool(expected_value)
                else:  # 遥测
                    try:
                        value = float(expected_value)
                    except:
                        value = 0.0
                
                self.data_points[ied_name][signal_name] = {
                    'value': value,
                    'type': signal_type,
                    'quality': 'good'
                }
            
            total_points = sum(len(points) for points in self.data_points.values())
            self.log(f"成功加载点表: {file_path}, 共{len(self.data_points)}个IED, {total_points}个数据点")
            return True
            
        except Exception as e:
            self.log(f"加载点表失败: {e}")
            return False
    
    def _convert_to_bool(self, value):
        """转换值为布尔类型"""
        if isinstance(value, bool):
            return value
        elif isinstance(value, str):
            value_str = value.strip().lower()
            if value_str in ['true', '1', 'on', 'yes']:
                return True
            elif value_str in ['false', '0', 'off', 'no']:
                return False
            else:
                try:
                    return bool(int(float(value)))
                except:
                    return False
        else:
            try:
                return bool(int(float(value)))
            except:
                return False
    
    def start_server(self):
        """启动TCP服务器"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind((self.host, self.port))
            self.server_socket.listen(5)
            self.running = True
            
            self.log(f"子站服务器启动成功 - {self.host}:{self.port}")
            self.log("等待对点机连接...")
            
            while self.running:
                try:
                    client_socket, client_address = self.server_socket.accept()
                    self.clients.append(client_socket)
                    self.log(f"对点机连接: {client_address}")
                    
                    # 为每个客户端创建处理线程
                    client_thread = threading.Thread(
                        target=self.handle_client, 
                        args=(client_socket, client_address)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except Exception as e:
                    if self.running:
                        self.log(f"接受连接异常: {e}")
                        
        except Exception as e:
            self.log(f"启动服务器失败: {e}")
            
    def handle_client(self, client_socket, client_address):
        """处理客户端请求"""
        try:
            while self.running:
                try:
                    data = client_socket.recv(1024)
                    if not data:
                        break

                    request = data.decode('utf-8', errors='ignore')
                    response = self.process_request(request)

                    if response:
                        client_socket.send(response.encode('utf-8'))

                except socket.timeout:
                    continue
                except ConnectionResetError:
                    self.log(f"客户端 {client_address} 重置连接")
                    break
                except Exception as e:
                    self.log(f"处理客户端请求异常: {e}")
                    break

        except Exception as e:
            self.log(f"客户端处理异常: {e}")
        finally:
            try:
                client_socket.close()
                if client_socket in self.clients:
                    self.clients.remove(client_socket)
                self.log(f"客户端断开: {client_address}")
            except:
                pass
    
    def process_request(self, request):
        """处理请求"""
        try:
            # 简单的READ请求处理
            if 'READ' in request.upper():
                point_name = request.split()[-1]
                return self.handle_read_request(point_name)
            else:
                return json.dumps({'error': '不支持的请求类型'})
                
        except Exception as e:
            self.log(f"请求处理异常: {e}")
            return json.dumps({'error': str(e)})
    
    def handle_read_request(self, point_name):
        """处理读取请求"""
        # 查找数据点
        for ied_name, points in self.data_points.items():
            if point_name in points:
                point_data = points[point_name]
                response = {
                    'status': 'success',
                    'data': {
                        'point_name': point_name,
                        'value': point_data['value'],
                        'type': point_data['type'],
                        'quality': point_data['quality'],
                        'timestamp': datetime.now().isoformat()
                    }
                }
                return json.dumps(response, ensure_ascii=False)
        
        return json.dumps({'status': 'error', 'message': f'数据点 {point_name} 不存在'})
    
    def stop_server(self):
        """停止服务器"""
        self.running = False
        
        # 关闭所有客户端连接
        for client in self.clients:
            try:
                client.close()
            except:
                pass
        self.clients.clear()
        
        # 关闭服务器socket
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
                
        self.log("子站服务器已停止")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("使用方法: python substation_simulator_lite.py <点表文件路径> [端口号]")
        print("示例: python substation_simulator_lite.py test_points_fixed.csv 102")
        return
    
    point_table_path = sys.argv[1]
    port = int(sys.argv[2]) if len(sys.argv) > 2 else 102
    
    # 创建模拟器实例
    simulator = SubstationSimulatorLite(port=port)
    
    # 加载点表
    if not simulator.load_point_table(point_table_path):
        print("点表加载失败，退出程序")
        return
    
    try:
        # 启动服务器
        print(f"启动精简版子站模拟器...")
        print(f"端口: {port}")
        print(f"点表: {point_table_path}")
        print("按 Ctrl+C 停止服务器")
        
        simulator.start_server()
        
    except KeyboardInterrupt:
        print("\n收到停止信号...")
        simulator.stop_server()
        print("服务器已停止")

if __name__ == "__main__":
    main()
