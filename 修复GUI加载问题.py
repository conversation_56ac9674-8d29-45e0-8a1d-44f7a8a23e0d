#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复子站模拟器GUI加载问题
解决"启用服务"时提示"加载点表"的问题
"""

import os
import pandas as pd
from datetime import datetime

def check_point_table_files():
    """检查点表文件"""
    print("🔍 检查点表文件状态")
    print("-" * 50)
    
    # 查找所有点表文件
    csv_files = [f for f in os.listdir('.') if f.startswith('point_table_') and f.endswith('.csv')]
    
    if not csv_files:
        print("   ❌ 未找到点表文件")
        return None
    
    # 按修改时间排序，获取最新文件
    csv_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
    latest_file = csv_files[0]
    
    print(f"   ✅ 找到 {len(csv_files)} 个点表文件")
    print(f"   📄 最新文件: {latest_file}")
    
    # 检查文件内容
    try:
        df = pd.read_csv(latest_file, encoding='utf-8-sig')
        point_count = len(df)
        file_size = os.path.getsize(latest_file) / 1024  # KB
        
        print(f"   📊 数据点数: {point_count}个")
        print(f"   📏 文件大小: {file_size:.1f}KB")
        
        # 显示前几行数据
        print(f"   📝 数据预览:")
        for i, row in df.head(3).iterrows():
            point_id = row.get('点号', 'N/A')
            signal_name = row.get('信号名称', 'N/A')
            signal_type = row.get('信号类型', 'N/A')
            print(f"      {i+1}. 点号:{point_id}, 名称:{signal_name}, 类型:{signal_type}")
        
        return latest_file
        
    except Exception as e:
        print(f"   ❌ 文件读取失败: {e}")
        return None

def create_simple_point_table():
    """创建简单的点表文件供GUI使用"""
    print("\n🔧 创建简化点表文件")
    print("-" * 50)
    
    try:
        # 创建简化的点表数据
        simple_data = []
        
        # 生成1000个简单数据点
        for i in range(1000):
            point_id = 1001 + i
            
            if i < 600:  # 遥信
                signal_type = 'DI'
                data_type = 'BOOL'
                value = '0'
                name = f'遥信_{point_id}'
                desc = f'遥信信号点_{point_id}'
            elif i < 800:  # 遥测
                signal_type = 'AI'
                data_type = 'FLOAT'
                value = '220.0'
                name = f'遥测_{point_id}'
                desc = f'遥测信号点_{point_id}'
            else:  # 遥调
                signal_type = 'AO'
                data_type = 'FLOAT'
                value = '50.0'
                name = f'遥调_{point_id}'
                desc = f'遥调信号点_{point_id}'
            
            simple_data.append({
                '点号': point_id,
                '信号名称': name,
                '信号类型': signal_type,
                '数据类型': data_type,
                '期望值': value,
                '描述': desc,
                'SCD路径': f'IED_{(i//50)+1}.LD0.{signal_type}{i%50+1}',
                'IED名称': f'IED_{(i//50)+1}'
            })
        
        # 保存为CSV文件
        df = pd.DataFrame(simple_data)
        filename = f'simple_point_table_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        
        file_size = os.path.getsize(filename) / 1024
        print(f"   ✅ 创建成功: {filename}")
        print(f"   📊 数据点数: {len(simple_data)}个")
        print(f"   📏 文件大小: {file_size:.1f}KB")
        
        return filename
        
    except Exception as e:
        print(f"   ❌ 创建失败: {e}")
        return None

def provide_gui_instructions():
    """提供GUI操作指导"""
    print("\n📋 GUI操作指导")
    print("=" * 60)
    
    print("🎯 解决'加载点表'提示的步骤:")
    print("   1. 在子站模拟器GUI中找到'加载点表'或'浏览'按钮")
    print("   2. 选择刚创建的点表文件 (simple_point_table_*.csv)")
    print("   3. 确认加载成功，应显示数据点数量")
    print("   4. 然后点击'启用服务'或'启动服务'")
    
    print("\n🔧 如果仍然有问题:")
    print("   1. 尝试重新启动子站模拟器GUI")
    print("   2. 先加载文件，再配置端口参数")
    print("   3. 确保端口设置为102")
    print("   4. 检查是否有其他程序占用端口")
    
    print("\n💡 替代方案:")
    print("   1. 使用我们的自动服务 (推荐)")
    print("   2. 运行: python 自动启动子站服务.py")
    print("   3. 直接在Auto_Point中配置连接")

def restart_auto_service():
    """重新启动自动服务"""
    print("\n🚀 重新启动自动子站服务")
    print("-" * 50)
    
    print("   💡 如果GUI问题难以解决，建议使用自动服务:")
    print("   📝 运行命令: python 自动启动子站服务.py")
    print("   🔗 连接参数: localhost:102")
    print("   📊 数据点数: 1680个")
    print("   ✅ 无需GUI操作，自动加载数据")

def main():
    """主函数"""
    print("🛠️ 子站模拟器GUI加载问题修复")
    print("=" * 80)
    print(f"🕐 修复时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查现有点表文件
    existing_file = check_point_table_files()
    
    # 2. 创建简化点表文件
    simple_file = create_simple_point_table()
    
    # 3. 提供操作指导
    provide_gui_instructions()
    
    # 4. 建议替代方案
    restart_auto_service()
    
    print("\n" + "=" * 80)
    print("📊 修复总结")
    print("=" * 80)
    
    if existing_file:
        print(f"   ✅ 现有点表文件: {existing_file}")
    
    if simple_file:
        print(f"   ✅ 新建简化文件: {simple_file}")
        print(f"   💡 建议在GUI中加载此文件")
    
    print(f"\n🎯 推荐操作:")
    print(f"   方案1: 在GUI中加载 {simple_file if simple_file else '点表文件'}")
    print(f"   方案2: 使用自动服务 (python 自动启动子站服务.py)")
    print(f"   方案3: 直接在Auto_Point中测试连接")
    
    return True

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✅ GUI问题修复指导完成")
        print(f"💡 请按照上述指导操作，或使用自动服务")
    else:
        print(f"\n❌ 修复过程中遇到问题")
