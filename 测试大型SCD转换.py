#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试大型SCD文件转换功能
验证2000个数据点的转换性能和准确性
"""

import os
import time
from datetime import datetime

def test_large_scd_conversion():
    """测试大型SCD文件转换"""
    print("🧪 大型SCD文件转换测试")
    print("=" * 60)
    
    # 检查大型SCD文件
    large_scd_file = "large_substation_2000points.scd"
    
    if not os.path.exists(large_scd_file):
        print(f"❌ 大型SCD文件不存在: {large_scd_file}")
        return False
    
    file_size = os.path.getsize(large_scd_file)
    print(f"✅ 发现大型SCD文件: {large_scd_file}")
    print(f"📊 文件大小: {file_size:,} bytes ({file_size/1024:.1f} KB)")
    
    try:
        # 导入转换器
        from scd_to_point_converter import SCDToPointConverter
        print("✅ SCD转换器导入成功")
        
        # 创建转换器实例
        converter = SCDToPointConverter()
        print("✅ 转换器实例创建成功")
        
        # 记录开始时间
        start_time = time.time()
        print(f"\n🔄 开始解析大型SCD文件...")
        print(f"🕐 开始时间: {datetime.now().strftime('%H:%M:%S')}")
        
        # 解析SCD文件
        scd_result = converter.parse_scd_file(large_scd_file)
        
        parse_time = time.time() - start_time
        print(f"✅ SCD文件解析完成")
        print(f"⏱️ 解析耗时: {parse_time:.2f} 秒")
        print(f"📊 解析结果:")
        print(f"   - IED数量: {scd_result['total_ieds']}")
        print(f"   - 文件大小: {scd_result['file_size']:,} bytes")
        
        # 转换为点表
        print(f"\n🔄 开始转换为点表...")
        convert_start_time = time.time()
        
        csv_file = converter.convert_to_point_table('csv')
        
        convert_time = time.time() - convert_start_time
        total_time = time.time() - start_time
        
        print(f"✅ 点表转换完成")
        print(f"⏱️ 转换耗时: {convert_time:.2f} 秒")
        print(f"⏱️ 总耗时: {total_time:.2f} 秒")
        print(f"📄 输出文件: {csv_file}")
        
        # 获取转换统计
        stats = converter.get_conversion_statistics()
        
        print(f"\n📊 转换统计:")
        print(f"   📈 总信号点数: {stats.get('total_points', 0)}")
        
        signal_types = stats.get('signal_types', {})
        for signal_type, count in signal_types.items():
            type_name = {'DI': '遥信', 'AI': '遥测', 'DO': '遥控', 'AO': '遥调'}.get(signal_type, signal_type)
            percentage = (count / stats.get('total_points', 1)) * 100
            print(f"   📊 {type_name}: {count}个 ({percentage:.1f}%)")
        
        data_types = stats.get('data_types', {})
        print(f"   📊 数据类型分布:")
        for data_type, count in data_types.items():
            percentage = (count / stats.get('total_points', 1)) * 100
            print(f"      - {data_type}: {count}个 ({percentage:.1f}%)")
        
        # 验证输出文件
        print(f"\n📋 验证输出文件:")
        if os.path.exists(csv_file):
            output_size = os.path.getsize(csv_file)
            print(f"   ✅ 文件存在: {csv_file}")
            print(f"   📊 文件大小: {output_size:,} bytes ({output_size/1024:.1f} KB)")
            
            # 读取并验证CSV内容
            try:
                import pandas as pd
                df = pd.read_csv(csv_file, encoding='utf-8-sig')
                print(f"   ✅ CSV格式正确")
                print(f"   📊 数据行数: {len(df)}")
                print(f"   📊 数据列数: {len(df.columns)}")
                
                # 验证数据完整性
                if len(df) == stats.get('total_points', 0):
                    print(f"   ✅ 数据完整性验证通过")
                else:
                    print(f"   ❌ 数据完整性验证失败: 期望{stats.get('total_points', 0)}行，实际{len(df)}行")
                
                # 显示前5行和后5行数据
                print(f"\n📋 数据预览 (前5行):")
                for i, row in df.head(5).iterrows():
                    print(f"   {i+1}. {row['信号名称']} ({row['信号类型']}) - {row['描述']}")
                
                print(f"\n📋 数据预览 (后5行):")
                for i, row in df.tail(5).iterrows():
                    print(f"   {i+1}. {row['信号名称']} ({row['信号类型']}) - {row['描述']}")
                
                return True
                
            except Exception as e:
                print(f"   ❌ CSV文件验证失败: {e}")
                return False
        else:
            print(f"   ❌ 输出文件不存在")
            return False
            
    except ImportError:
        print("❌ SCD转换器模块导入失败")
        return False
    except Exception as e:
        print(f"❌ 转换测试失败: {e}")
        return False

def test_performance_metrics():
    """测试性能指标"""
    print("\n📈 性能指标测试")
    print("=" * 60)
    
    large_scd_file = "large_substation_2000points.scd"
    
    if not os.path.exists(large_scd_file):
        print(f"❌ 测试文件不存在")
        return False
    
    try:
        from scd_to_point_converter import SCDToPointConverter
        
        # 进行多次测试以获得平均性能
        test_rounds = 3
        parse_times = []
        convert_times = []
        total_times = []
        
        print(f"🔄 进行 {test_rounds} 轮性能测试...")
        
        for round_num in range(1, test_rounds + 1):
            print(f"\n📋 第 {round_num} 轮测试:")
            
            converter = SCDToPointConverter()
            
            # 解析测试
            start_time = time.time()
            scd_result = converter.parse_scd_file(large_scd_file)
            parse_time = time.time() - start_time
            parse_times.append(parse_time)
            
            # 转换测试
            convert_start = time.time()
            csv_file = converter.convert_to_point_table('csv')
            convert_time = time.time() - convert_start
            convert_times.append(convert_time)
            
            total_time = time.time() - start_time
            total_times.append(total_time)
            
            stats = converter.get_conversion_statistics()
            
            print(f"   ⏱️ 解析时间: {parse_time:.2f}秒")
            print(f"   ⏱️ 转换时间: {convert_time:.2f}秒")
            print(f"   ⏱️ 总时间: {total_time:.2f}秒")
            print(f"   📊 数据点数: {stats.get('total_points', 0)}")
            
            # 清理临时文件
            if os.path.exists(csv_file):
                os.remove(csv_file)
        
        # 计算平均性能
        avg_parse_time = sum(parse_times) / len(parse_times)
        avg_convert_time = sum(convert_times) / len(convert_times)
        avg_total_time = sum(total_times) / len(total_times)
        
        print(f"\n📊 平均性能指标:")
        print(f"   ⏱️ 平均解析时间: {avg_parse_time:.2f}秒")
        print(f"   ⏱️ 平均转换时间: {avg_convert_time:.2f}秒")
        print(f"   ⏱️ 平均总时间: {avg_total_time:.2f}秒")
        
        # 计算处理速度
        points_per_second = 2000 / avg_total_time
        print(f"   🚀 处理速度: {points_per_second:.0f} 点/秒")
        
        # 性能评估
        if avg_total_time < 5:
            print(f"   🏆 性能评级: 优秀 (< 5秒)")
        elif avg_total_time < 10:
            print(f"   🥈 性能评级: 良好 (< 10秒)")
        elif avg_total_time < 20:
            print(f"   🥉 性能评级: 一般 (< 20秒)")
        else:
            print(f"   ⚠️ 性能评级: 需要优化 (≥ 20秒)")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🧪 大型SCD文件转换功能测试")
    print("=" * 70)
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 测试目标: 验证2000个数据点的转换性能")
    print("=" * 70)
    
    test_results = []
    
    # 测试1: 大型SCD转换功能
    print("\n🧪 测试1: 大型SCD转换功能")
    result1 = test_large_scd_conversion()
    test_results.append(("大型SCD转换功能", result1))
    
    # 测试2: 性能指标测试
    print("\n🧪 测试2: 性能指标测试")
    result2 = test_performance_metrics()
    test_results.append(("性能指标测试", result2))
    
    # 测试总结
    print("\n" + "=" * 70)
    print("📊 大型SCD转换测试总结")
    print("=" * 70)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    success_rate = (passed / total * 100) if total > 0 else 0
    
    print(f"\n📈 测试统计:")
    print(f"   ✅ 通过: {passed} 项")
    print(f"   ❌ 失败: {total - passed} 项")
    print(f"   📊 成功率: {success_rate:.1f}%")
    
    if success_rate >= 100:
        print(f"\n🎉 大型SCD转换测试全部通过！")
        print(f"✅ 系统可以处理2000个数据点的大规模转换")
        print(f"✅ 转换性能和准确性均达到要求")
    elif success_rate >= 50:
        print(f"\n⚠️ 大型SCD转换测试部分通过")
        print(f"💡 建议检查失败的测试项目")
    else:
        print(f"\n❌ 大型SCD转换测试失败较多")
        print(f"💡 建议检查系统配置和文件完整性")
    
    print(f"\n💡 使用建议:")
    print(f"   1. 在Web界面中选择large_substation_2000points.scd文件")
    print(f"   2. 执行解析和转换操作")
    print(f"   3. 验证生成的点表文件")
    print(f"   4. 测试大规模数据的对点功能")

if __name__ == "__main__":
    main()
