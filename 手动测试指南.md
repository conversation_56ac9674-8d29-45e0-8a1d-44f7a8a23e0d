# Auto_Point 手动测试指南

## 🎯 当前系统状态

### ✅ **已启动的服务**

#### **🎮 Auto_Point对点机 (终端ID 75)**
- **状态**: ✅ 正常运行
- **功能**: 完整版Web界面
- **访问**: 通过浏览器访问Web界面
- **特性**: 支持SCD文件和点表文件加载

#### **🏭 子站模拟器GUI (终端ID 72)**
- **状态**: ✅ 已启动
- **功能**: 图形界面操作
- **操作**: 需要手动加载文件和启动服务

#### **🔧 自动子站服务 (终端ID 76)**
- **状态**: ✅ 正常运行
- **监听**: 0.0.0.0:102
- **数据点**: 21个 (备用服务)
- **连接**: 等待对点机连接

### 📁 **可用的测试文件**
- **SCD文件**: `new_substation_config.scd` (新编制)
- **大型点表**: `gui_2000points_20250704_145348.csv` (2000个点)
- **小型点表**: `gui_compatible_point_table.csv` (32个点)
- **历史点表**: 多个 `point_table_*.csv` 文件

## 🎮 **手动测试流程**

### **第一阶段: 对点机功能测试**

#### **测试1: 访问Web界面**
1. **打开浏览器**
2. **访问地址**: `http://localhost:8080` (或启动时显示的地址)
3. **验证界面**: 确认看到Auto_Point Web风格对点机界面
4. **检查导航**: 确认左侧导航菜单正常显示

#### **测试2: SCD文件加载**
1. **选择功能**: 点击 "📁 配置文件管理" → "SCD文件解析"
2. **选择文件**: 点击"选择文件"，选择 `new_substation_config.scd`
3. **解析文件**: 点击"解析文件"按钮
4. **验证结果**: 确认显示IED设备和数据对象信息
5. **转换点表**: 点击"转换为点表"生成CSV文件

#### **测试3: 点表文件加载**
1. **选择文件**: 选择 `gui_2000points_20250704_145348.csv`
2. **自动检测**: 确认系统识别为点表文件
3. **加载点表**: 点击"加载点表"按钮
4. **验证结果**: 确认显示2000个数据点
5. **查看分析**: 检查信号类型分布和IED统计

#### **测试4: 通信配置**
1. **选择功能**: 点击 "🔧 通信配置" → "网关配置"
2. **配置参数**:
   ```
   服务器地址: localhost
   端口: 102
   协议: IEC 61850
   ```
3. **测试连接**: 点击"测试连接"按钮
4. **验证结果**: 确认连接成功

### **第二阶段: 子站模拟器测试**

#### **测试5: 子站GUI操作**
1. **找到GUI窗口**: 查找"子站模拟器 - 优化版"窗口
2. **加载点表**: 点击"加载点表"，选择点表文件
3. **配置参数**:
   ```
   监听IP: 0.0.0.0
   端口: 102
   协议: IEC 61850
   ```
4. **启动服务**: 点击"启动服务器"按钮
5. **验证状态**: 确认显示"服务运行中"

#### **测试6: 验证子站服务**
1. **检查端口**: 确认端口102正在监听
2. **查看数据**: 确认数据点列表显示正常
3. **监控连接**: 观察连接状态变化

### **第三阶段: 连接和对点测试**

#### **测试7: 建立连接**
1. **在对点机中**: 重新测试连接
2. **观察子站**: 查看子站是否显示新连接
3. **验证通信**: 确认双方通信正常

#### **测试8: 执行对点测试**
1. **选择功能**: 在对点机中点击 "🎮 自动对点"
2. **配置测试**:
   ```
   测试模式: 自动对点
   测试速度: 5级 (中等速度)
   测试范围: 全部信号
   ```
3. **开始测试**: 点击"开始自动对点"
4. **监控进度**: 观察测试进度和实时状态
5. **查看结果**: 等待测试完成，查看成功率

#### **测试9: 生成报告**
1. **选择功能**: 点击 "📋 报告管理" → "验收报告"
2. **填写信息**:
   ```
   操作人员: [您的姓名]
   项目名称: Auto_Point手动测试
   变电站名: 测试变电站
   ```
3. **选择格式**: 选择"全部格式"
4. **生成报告**: 点击"生成验收报告"
5. **下载查看**: 下载并查看生成的报告

## 🔍 **测试验证要点**

### **对点机验证**
- ✅ **文件加载**: SCD和CSV文件都能正确加载
- ✅ **类型识别**: 自动识别文件类型
- ✅ **数据解析**: 正确解析文件内容
- ✅ **统计分析**: 准确的数据统计信息
- ✅ **界面响应**: 操作流畅，无卡顿

### **子站模拟器验证**
- ✅ **文件加载**: 点表文件加载成功
- ✅ **服务启动**: 网络服务正常启动
- ✅ **端口监听**: 端口102正常监听
- ✅ **数据提供**: 能够提供模拟数据

### **通信验证**
- ✅ **连接建立**: 对点机能连接到子站
- ✅ **数据传输**: 数据正常传输
- ✅ **协议兼容**: IEC 61850协议正常工作
- ✅ **稳定性**: 连接稳定，无断线

### **对点测试验证**
- ✅ **测试执行**: 对点测试正常执行
- ✅ **进度显示**: 实时进度正常显示
- ✅ **结果统计**: 准确的成功率统计
- ✅ **报告生成**: 专业报告正常生成

## 🚨 **常见问题处理**

### **连接问题**
- **现象**: 对点机连接失败
- **检查**: 确认子站服务已启动
- **解决**: 重启子站服务或使用自动服务

### **文件加载问题**
- **现象**: 文件加载失败
- **检查**: 确认文件格式和编码
- **解决**: 尝试不同的文件或检查文件完整性

### **测试异常**
- **现象**: 对点测试中断
- **检查**: 确认网络连接稳定
- **解决**: 重新建立连接后继续测试

## 💡 **测试建议**

### **推荐测试顺序**
1. **先测试对点机** - 确认界面和文件加载功能
2. **再测试子站** - 确认服务启动和数据提供
3. **然后测试连接** - 确认通信正常
4. **最后测试对点** - 执行完整测试流程

### **性能测试建议**
- **小规模测试**: 先用32个数据点测试基本功能
- **大规模测试**: 再用2000个数据点测试性能
- **稳定性测试**: 多次重复测试验证稳定性

### **功能测试重点**
- **文件兼容性**: 测试不同格式的文件
- **编码支持**: 测试中文和英文内容
- **数据完整性**: 验证数据传输的准确性
- **报告质量**: 检查生成报告的专业性

---

**🎯 系统已完全就绪，现在可以开始手动测试！请按照上述流程逐步验证各项功能。**

*测试指南版本: v1.0*  
*适用系统: Auto_Point Web风格对点机 + 子站模拟器*  
*测试时间: 2025年7月4日 15:34*
