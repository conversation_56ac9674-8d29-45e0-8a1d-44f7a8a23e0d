#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
目录文件浏览器
为Auto_Point对点机提供高级目录浏览功能
"""

import os
import sys
from datetime import datetime
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                               QHBoxLayout, QTreeWidget, QTreeWidgetItem, QLabel, 
                               QPushButton, QTextEdit, QSplitter, QGroupBox,
                               QMessageBox, QFileDialog, QComboBox, QLineEdit)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QIcon

class DirectoryFileBrowser(QMainWindow):
    """目录文件浏览器"""
    file_selected = Signal(str)  # 文件选择信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Auto_Point 目录文件浏览器")
        self.setGeometry(200, 200, 1000, 700)
        self.current_path = os.getcwd()
        self.setup_ui()
        self.load_directory(self.current_path)
    
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题
        title_label = QLabel("目录文件浏览器")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setStyleSheet("color: #262626; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 路径导航
        nav_layout = QHBoxLayout()
        
        self.path_edit = QLineEdit(self.current_path)
        self.path_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                font-size: 13px;
            }
        """)
        self.path_edit.returnPressed.connect(self.navigate_to_path)
        
        self.up_btn = QPushButton("上级目录")
        self.up_btn.clicked.connect(self.go_up)
        self.up_btn.setStyleSheet("""
            QPushButton {
                padding: 8px 16px;
                background-color: #f0f0f0;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e6f7ff;
            }
        """)
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_directory)
        self.refresh_btn.setStyleSheet(self.up_btn.styleSheet())
        
        nav_layout.addWidget(QLabel("路径:"))
        nav_layout.addWidget(self.path_edit)
        nav_layout.addWidget(self.up_btn)
        nav_layout.addWidget(self.refresh_btn)
        
        layout.addLayout(nav_layout)
        
        # 文件过滤
        filter_layout = QHBoxLayout()
        
        filter_label = QLabel("文件类型:")
        self.filter_combo = QComboBox()
        self.filter_combo.addItems([
            "所有文件 (*.*)",
            "SCD文件 (*.scd)",
            "CSV文件 (*.csv)",
            "Excel文件 (*.xlsx)",
            "文本文件 (*.txt)",
            "配置文件 (*.scd, *.csv)"
        ])
        self.filter_combo.currentTextChanged.connect(self.apply_filter)
        
        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(self.filter_combo)
        filter_layout.addStretch()
        
        layout.addLayout(filter_layout)
        
        # 主要内容区域
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：目录树
        tree_group = QGroupBox("目录结构")
        tree_layout = QVBoxLayout(tree_group)
        
        self.tree_widget = QTreeWidget()
        self.tree_widget.setHeaderLabels(["名称", "类型", "大小", "修改时间"])
        self.tree_widget.itemDoubleClicked.connect(self.on_item_double_clicked)
        self.tree_widget.itemClicked.connect(self.on_item_clicked)
        
        tree_layout.addWidget(self.tree_widget)
        
        # 右侧：文件信息
        info_group = QGroupBox("文件信息")
        info_layout = QVBoxLayout(info_group)
        
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(200)
        self.info_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                padding: 10px;
                font-family: 'Consolas', monospace;
                font-size: 12px;
            }
        """)
        
        # 操作按钮
        button_layout = QHBoxLayout()
        
        self.select_btn = QPushButton("选择此文件")
        self.select_btn.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                background-color: #1890ff;
                color: white;
                border: none;
                border-radius: 6px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
            QPushButton:disabled {
                background-color: #d9d9d9;
                color: #8c8c8c;
            }
        """)
        self.select_btn.clicked.connect(self.select_current_file)
        self.select_btn.setEnabled(False)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                padding: 10px 20px;
                background-color: #f0f0f0;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
            }
            QPushButton:hover {
                background-color: #e6f7ff;
            }
        """)
        self.cancel_btn.clicked.connect(self.close)
        
        button_layout.addWidget(self.select_btn)
        button_layout.addWidget(self.cancel_btn)
        button_layout.addStretch()
        
        info_layout.addWidget(self.info_text)
        info_layout.addLayout(button_layout)
        
        # 添加到分割器
        splitter.addWidget(tree_group)
        splitter.addWidget(info_group)
        splitter.setSizes([600, 400])
        
        layout.addWidget(splitter)
        
        # 状态信息
        self.status_label = QLabel("请选择文件")
        self.status_label.setStyleSheet("color: #8c8c8c; padding: 5px;")
        layout.addWidget(self.status_label)
        
        self.selected_file = None
    
    def load_directory(self, path):
        """加载目录内容"""
        try:
            self.tree_widget.clear()
            self.current_path = path
            self.path_edit.setText(path)
            
            # 获取目录内容
            items = []
            
            # 添加目录
            for item in os.listdir(path):
                item_path = os.path.join(path, item)
                
                if os.path.isdir(item_path):
                    items.append({
                        'name': item,
                        'type': '文件夹',
                        'size': '',
                        'modified': datetime.fromtimestamp(os.path.getmtime(item_path)).strftime('%Y-%m-%d %H:%M'),
                        'path': item_path,
                        'is_dir': True
                    })
            
            # 添加文件
            for item in os.listdir(path):
                item_path = os.path.join(path, item)
                
                if os.path.isfile(item_path):
                    size = os.path.getsize(item_path)
                    size_str = self.format_size(size)
                    
                    items.append({
                        'name': item,
                        'type': self.get_file_type(item),
                        'size': size_str,
                        'modified': datetime.fromtimestamp(os.path.getmtime(item_path)).strftime('%Y-%m-%d %H:%M'),
                        'path': item_path,
                        'is_dir': False
                    })
            
            # 排序：文件夹在前，然后按名称排序
            items.sort(key=lambda x: (not x['is_dir'], x['name'].lower()))
            
            # 添加到树控件
            for item in items:
                tree_item = QTreeWidgetItem([
                    item['name'],
                    item['type'],
                    item['size'],
                    item['modified']
                ])
                tree_item.setData(0, Qt.UserRole, item['path'])
                tree_item.setData(1, Qt.UserRole, item['is_dir'])
                
                # 设置图标样式
                if item['is_dir']:
                    tree_item.setText(0, f"📁 {item['name']}")
                else:
                    if item['name'].endswith('.scd'):
                        tree_item.setText(0, f"⚙️ {item['name']}")
                    elif item['name'].endswith('.csv'):
                        tree_item.setText(0, f"📊 {item['name']}")
                    elif item['name'].endswith('.xlsx'):
                        tree_item.setText(0, f"📈 {item['name']}")
                    else:
                        tree_item.setText(0, f"📄 {item['name']}")
                
                self.tree_widget.addTopLevelItem(tree_item)
            
            # 调整列宽
            self.tree_widget.resizeColumnToContents(0)
            self.tree_widget.resizeColumnToContents(1)
            self.tree_widget.resizeColumnToContents(2)
            
            self.status_label.setText(f"当前目录: {path} ({len(items)} 项)")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载目录失败: {str(e)}")
    
    def format_size(self, size):
        """格式化文件大小"""
        if size < 1024:
            return f"{size} B"
        elif size < 1024 * 1024:
            return f"{size/1024:.1f} KB"
        else:
            return f"{size/1024/1024:.1f} MB"
    
    def get_file_type(self, filename):
        """获取文件类型"""
        ext = os.path.splitext(filename)[1].lower()
        types = {
            '.scd': 'SCD配置文件',
            '.csv': 'CSV数据文件',
            '.xlsx': 'Excel文件',
            '.txt': '文本文件',
            '.json': 'JSON文件',
            '.xml': 'XML文件'
        }
        return types.get(ext, '其他文件')
    
    def on_item_double_clicked(self, item):
        """双击项目"""
        path = item.data(0, Qt.UserRole)
        is_dir = item.data(1, Qt.UserRole)
        
        if is_dir:
            self.load_directory(path)
        else:
            self.select_file(path)
    
    def on_item_clicked(self, item):
        """单击项目"""
        path = item.data(0, Qt.UserRole)
        is_dir = item.data(1, Qt.UserRole)
        
        if not is_dir:
            self.show_file_info(path)
            self.selected_file = path
            self.select_btn.setEnabled(True)
        else:
            self.selected_file = None
            self.select_btn.setEnabled(False)
    
    def show_file_info(self, file_path):
        """显示文件信息"""
        try:
            stat = os.stat(file_path)
            size = stat.st_size
            modified = datetime.fromtimestamp(stat.st_mtime)
            
            info = f"文件信息\n"
            info += f"{'='*40}\n"
            info += f"文件名: {os.path.basename(file_path)}\n"
            info += f"完整路径: {file_path}\n"
            info += f"文件大小: {self.format_size(size)}\n"
            info += f"修改时间: {modified.strftime('%Y-%m-%d %H:%M:%S')}\n"
            info += f"文件类型: {self.get_file_type(file_path)}\n"
            
            # 特殊信息
            if file_path.endswith('.scd'):
                info += f"\n💡 这是一个SCD配置文件\n"
                info += f"   用途: IEC 61850标准配置\n"
                info += f"   可用于: 对点机标准配置解析\n"
            elif file_path.endswith('.csv'):
                info += f"\n💡 这是一个CSV数据文件\n"
                info += f"   用途: 点表数据或测试基准\n"
                info += f"   可用于: 对点机测试数据加载\n"
            
            self.info_text.setPlainText(info)
            
        except Exception as e:
            self.info_text.setPlainText(f"无法获取文件信息: {str(e)}")
    
    def select_file(self, file_path):
        """选择文件"""
        self.file_selected.emit(file_path)
        self.close()
    
    def select_current_file(self):
        """选择当前文件"""
        if self.selected_file:
            self.select_file(self.selected_file)
    
    def go_up(self):
        """上级目录"""
        parent_dir = os.path.dirname(self.current_path)
        if parent_dir != self.current_path:
            self.load_directory(parent_dir)
    
    def navigate_to_path(self):
        """导航到指定路径"""
        path = self.path_edit.text()
        if os.path.exists(path) and os.path.isdir(path):
            self.load_directory(path)
        else:
            QMessageBox.warning(self, "警告", "路径不存在或不是目录")
            self.path_edit.setText(self.current_path)
    
    def refresh_directory(self):
        """刷新目录"""
        self.load_directory(self.current_path)
    
    def apply_filter(self):
        """应用文件过滤"""
        # 这里可以添加文件过滤逻辑
        self.refresh_directory()

def main():
    """测试主函数"""
    app = QApplication(sys.argv)
    
    browser = DirectoryFileBrowser()
    browser.file_selected.connect(lambda path: print(f"选择的文件: {path}"))
    browser.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
