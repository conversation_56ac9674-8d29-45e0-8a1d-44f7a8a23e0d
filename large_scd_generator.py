#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大型SCD文件生成器
生成包含500个数据点的变电站SCD配置文件
"""

import xml.etree.ElementTree as ET
from xml.dom import minidom
from datetime import datetime
import random

class LargeSCDGenerator:
    """大型SCD文件生成器"""
    
    def __init__(self):
        self.root = None
        self.scl_ns = "http://www.iec.ch/61850/2003/SCL"
        self.data_type_templates = None
        
    def create_large_scd(self, substation_name="LargeSubstation_500kV", target_points=500):
        """创建包含指定数量数据点的大型SCD文件"""
        print(f"开始生成包含{target_points}个数据点的SCD文件...")
        
        # 创建根元素
        self.root = ET.Element("SCL", {
            "version": "2007",
            "revision": "B", 
            "release": "4",
            "xmlns": self.scl_ns,
            "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance"
        })
        
        # 添加Header
        self._add_header(substation_name)
        
        # 添加Substation结构
        self._add_substation_structure(substation_name)
        
        # 计算需要的IED数量和每个IED的数据点数
        points_per_ied = 50  # 每个IED约50个数据点
        ied_count = (target_points + points_per_ied - 1) // points_per_ied
        
        print(f"将创建{ied_count}个IED设备，每个约{points_per_ied}个数据点")
        
        # 添加IED设备
        for i in range(ied_count):
            remaining_points = target_points - i * points_per_ied
            current_ied_points = min(points_per_ied, remaining_points)
            self._add_large_ied(f"IED_{i+1:03d}", i+1, current_ied_points)
        
        # 添加数据类型模板
        self._add_comprehensive_data_types()
        
        print(f"✅ SCD文件生成完成，包含{ied_count}个IED设备")
        return self.root
    
    def _add_header(self, substation_name):
        """添加Header信息"""
        header = ET.SubElement(self.root, "Header", {
            "id": f"{substation_name}_Large_SCD",
            "version": "2.0",
            "revision": "1",
            "toolID": "AutoPoint_Large_SCD_Generator",
            "nameStructure": "IEDName"
        })
        
        history = ET.SubElement(header, "History")
        hitem = ET.SubElement(history, "Hitem", {
            "version": "2.0",
            "revision": "1", 
            "when": datetime.now().isoformat(),
            "who": "AutoPoint System",
            "what": f"Large SCD creation with 500+ data points"
        })
    
    def _add_substation_structure(self, substation_name):
        """添加变电站结构"""
        substation = ET.SubElement(self.root, "Substation", {
            "name": substation_name,
            "desc": f"{substation_name} Large Scale Configuration"
        })
        
        # 添加多个电压等级
        voltage_levels = [
            ("500kV", "500"),
            ("220kV", "220"), 
            ("110kV", "110"),
            ("35kV", "35"),
            ("10kV", "10")
        ]
        
        for vl_name, voltage in voltage_levels:
            voltage_level = ET.SubElement(substation, "VoltageLevel", {
                "name": vl_name,
                "desc": f"{vl_name} Voltage Level",
                "nomFreq": "50",
                "numPhases": "3"
            })
            
            voltage_elem = ET.SubElement(voltage_level, "Voltage", {
                "unit": "V",
                "multiplier": "k"
            })
            voltage_elem.text = voltage
            
            # 每个电压等级添加多个间隔
            for bay_num in range(1, 4):
                bay = ET.SubElement(voltage_level, "Bay", {
                    "name": f"{vl_name}_Bay{bay_num}",
                    "desc": f"{vl_name} Bay {bay_num}"
                })
    
    def _add_large_ied(self, ied_name, ied_index, target_points):
        """添加包含大量数据点的IED设备"""
        # 确定IED类型
        ied_types = ["XCBR", "MMXU", "PTRC", "GGIO", "CSWI", "TCTR", "TVTR"]
        ied_type = ied_types[ied_index % len(ied_types)]
        
        ied_section = ET.SubElement(self.root, "IED", {
            "name": ied_name,
            "type": ied_type,
            "manufacturer": "TestManufacturer",
            "configVersion": "2.0",
            "originalSclVersion": "2007",
            "originalSclRevision": "B"
        })
        
        # 添加Services
        services = ET.SubElement(ied_section, "Services")
        ET.SubElement(services, "DynAssociation")
        ET.SubElement(services, "SettingGroups")
        ET.SubElement(services, "GetDirectory")
        ET.SubElement(services, "GetDataObjectDefinition")
        ET.SubElement(services, "DataObjectDirectory")
        ET.SubElement(services, "GetDataSetValue")
        ET.SubElement(services, "ReadWrite")
        
        # 添加AccessPoint
        access_point = ET.SubElement(ied_section, "AccessPoint", {
            "name": "AP1"
        })
        
        # 添加Server
        server = ET.SubElement(access_point, "Server")
        authentication = ET.SubElement(server, "Authentication")
        
        # 添加LDevice
        ldevice = ET.SubElement(server, "LDevice", {
            "inst": "CTRL"
        })
        
        # 添加多个逻辑节点以达到目标数据点数
        self._add_logical_nodes_for_points(ldevice, ied_name, target_points)
    
    def _add_logical_nodes_for_points(self, ldevice, ied_name, target_points):
        """为指定的数据点数量添加逻辑节点"""
        # 不同类型的逻辑节点及其典型数据点数
        ln_types = [
            ("XCBR", "XCBR_TYPE", 8),    # 断路器：8个数据点
            ("MMXU", "MMXU_TYPE", 15),   # 测量单元：15个数据点
            ("PTRC", "PTRC_TYPE", 6),    # 保护：6个数据点
            ("GGIO", "GGIO_TYPE", 12),   # 通用IO：12个数据点
            ("CSWI", "CSWI_TYPE", 5),    # 开关：5个数据点
            ("TCTR", "TCTR_TYPE", 4),    # 电流互感器：4个数据点
            ("TVTR", "TVTR_TYPE", 4),    # 电压互感器：4个数据点
        ]
        
        points_added = 0
        ln_instance = 1
        
        while points_added < target_points:
            for ln_class, ln_type, points_per_ln in ln_types:
                if points_added >= target_points:
                    break
                    
                # 添加逻辑节点
                ln = ET.SubElement(ldevice, "LN", {
                    "lnClass": ln_class,
                    "inst": str(ln_instance),
                    "lnType": ln_type
                })
                
                points_added += points_per_ln
                ln_instance += 1
                
                # 如果接近目标，调整最后一个LN的数据点数
                if points_added > target_points:
                    points_added = target_points
                    break
    
    def _add_comprehensive_data_types(self):
        """添加全面的数据类型模板"""
        self.data_type_templates = ET.SubElement(self.root, "DataTypeTemplates")
        
        # 添加各种LNodeType
        self._add_lnodetype_xcbr()
        self._add_lnodetype_mmxu()
        self._add_lnodetype_ptrc()
        self._add_lnodetype_ggio()
        self._add_lnodetype_cswi()
        self._add_lnodetype_tctr()
        self._add_lnodetype_tvtr()
        
        # 添加DOType定义
        self._add_comprehensive_dotypes()
    
    def _add_lnodetype_xcbr(self):
        """添加断路器逻辑节点类型"""
        lntype = ET.SubElement(self.data_type_templates, "LNodeType", {
            "id": "XCBR_TYPE",
            "lnClass": "XCBR"
        })
        
        # 断路器的数据对象
        dos = [
            ("Pos", "DPC_TYPE"),      # 位置
            ("BlkOpn", "SPC_TYPE"),   # 分闸闭锁
            ("BlkCls", "SPC_TYPE"),   # 合闸闭锁
            ("Beh", "ENS_TYPE"),      # 行为
            ("Health", "ENS_TYPE"),   # 健康状态
            ("Mod", "ENC_TYPE"),      # 模式
            ("OpCnt", "INS_TYPE"),    # 操作计数
            ("Loc", "SPS_TYPE"),      # 本地/远方
        ]
        
        for do_name, do_type in dos:
            ET.SubElement(lntype, "DO", {
                "name": do_name,
                "type": do_type
            })
    
    def _add_lnodetype_mmxu(self):
        """添加测量单元逻辑节点类型"""
        lntype = ET.SubElement(self.data_type_templates, "LNodeType", {
            "id": "MMXU_TYPE", 
            "lnClass": "MMXU"
        })
        
        # 测量单元的数据对象
        dos = [
            ("TotW", "MV_TYPE"),      # 有功功率
            ("TotVAr", "MV_TYPE"),    # 无功功率
            ("TotVA", "MV_TYPE"),     # 视在功率
            ("TotPF", "MV_TYPE"),     # 功率因数
            ("Hz", "MV_TYPE"),        # 频率
            ("PhV", "WYE_TYPE"),      # 相电压
            ("A", "WYE_TYPE"),        # 电流
            ("PPV", "DEL_TYPE"),      # 线电压
            ("Beh", "ENS_TYPE"),      # 行为
            ("Health", "ENS_TYPE"),   # 健康状态
            ("Mod", "ENC_TYPE"),      # 模式
            ("MaxW", "MV_TYPE"),      # 最大有功功率
            ("MaxVAr", "MV_TYPE"),    # 最大无功功率
            ("MinW", "MV_TYPE"),      # 最小有功功率
            ("MinVAr", "MV_TYPE"),    # 最小无功功率
        ]
        
        for do_name, do_type in dos:
            ET.SubElement(lntype, "DO", {
                "name": do_name,
                "type": do_type
            })
    
    def _add_lnodetype_ptrc(self):
        """添加保护逻辑节点类型"""
        lntype = ET.SubElement(self.data_type_templates, "LNodeType", {
            "id": "PTRC_TYPE",
            "lnClass": "PTRC"
        })
        
        dos = [
            ("Str", "ACD_TYPE"),      # 启动
            ("Op", "ACT_TYPE"),       # 动作
            ("Tr", "ACD_TYPE"),       # 跳闸
            ("Beh", "ENS_TYPE"),      # 行为
            ("Health", "ENS_TYPE"),   # 健康状态
            ("Mod", "ENC_TYPE"),      # 模式
        ]
        
        for do_name, do_type in dos:
            ET.SubElement(lntype, "DO", {
                "name": do_name,
                "type": do_type
            })
    
    def _add_lnodetype_ggio(self):
        """添加通用IO逻辑节点类型"""
        lntype = ET.SubElement(self.data_type_templates, "LNodeType", {
            "id": "GGIO_TYPE",
            "lnClass": "GGIO"
        })
        
        # 通用IO的数据对象
        for i in range(1, 9):  # 8个SPCSO
            ET.SubElement(lntype, "DO", {
                "name": f"SPCSO{i}",
                "type": "SPC_TYPE"
            })
        
        for i in range(1, 5):  # 4个Ind
            ET.SubElement(lntype, "DO", {
                "name": f"Ind{i}",
                "type": "SPS_TYPE"
            })
    
    def _add_lnodetype_cswi(self):
        """添加开关逻辑节点类型"""
        lntype = ET.SubElement(self.data_type_templates, "LNodeType", {
            "id": "CSWI_TYPE",
            "lnClass": "CSWI"
        })
        
        dos = [
            ("Pos", "DPC_TYPE"),      # 位置
            ("Beh", "ENS_TYPE"),      # 行为
            ("Health", "ENS_TYPE"),   # 健康状态
            ("Mod", "ENC_TYPE"),      # 模式
            ("Loc", "SPS_TYPE"),      # 本地/远方
        ]
        
        for do_name, do_type in dos:
            ET.SubElement(lntype, "DO", {
                "name": do_name,
                "type": do_type
            })
    
    def _add_lnodetype_tctr(self):
        """添加电流互感器逻辑节点类型"""
        lntype = ET.SubElement(self.data_type_templates, "LNodeType", {
            "id": "TCTR_TYPE",
            "lnClass": "TCTR"
        })
        
        dos = [
            ("Amp", "SAV_TYPE"),      # 电流值
            ("Beh", "ENS_TYPE"),      # 行为
            ("Health", "ENS_TYPE"),   # 健康状态
            ("Mod", "ENC_TYPE"),      # 模式
        ]
        
        for do_name, do_type in dos:
            ET.SubElement(lntype, "DO", {
                "name": do_name,
                "type": do_type
            })
    
    def _add_lnodetype_tvtr(self):
        """添加电压互感器逻辑节点类型"""
        lntype = ET.SubElement(self.data_type_templates, "LNodeType", {
            "id": "TVTR_TYPE",
            "lnClass": "TVTR"
        })
        
        dos = [
            ("Vol", "SAV_TYPE"),      # 电压值
            ("Beh", "ENS_TYPE"),      # 行为
            ("Health", "ENS_TYPE"),   # 健康状态
            ("Mod", "ENC_TYPE"),      # 模式
        ]
        
        for do_name, do_type in dos:
            ET.SubElement(lntype, "DO", {
                "name": do_name,
                "type": do_type
            })
    
    def _add_comprehensive_dotypes(self):
        """添加全面的DOType定义"""
        # 定义各种DOType
        dotypes = [
            ("DPC_TYPE", "DPC", [("stVal", "ST", "BOOLEAN"), ("q", "ST", "Quality"), ("t", "ST", "Timestamp"), ("ctlVal", "CO", "BOOLEAN")]),
            ("SPC_TYPE", "SPC", [("stVal", "ST", "BOOLEAN"), ("q", "ST", "Quality"), ("t", "ST", "Timestamp"), ("ctlVal", "CO", "BOOLEAN")]),
            ("SPS_TYPE", "SPS", [("stVal", "ST", "BOOLEAN"), ("q", "ST", "Quality"), ("t", "ST", "Timestamp")]),
            ("ENS_TYPE", "ENS", [("stVal", "ST", "INT32"), ("q", "ST", "Quality"), ("t", "ST", "Timestamp")]),
            ("ENC_TYPE", "ENC", [("stVal", "ST", "INT32"), ("q", "ST", "Quality"), ("t", "ST", "Timestamp"), ("ctlVal", "CO", "INT32")]),
            ("INS_TYPE", "INS", [("stVal", "ST", "INT32"), ("q", "ST", "Quality"), ("t", "ST", "Timestamp")]),
            ("MV_TYPE", "MV", [("mag", "MX", "AnalogueValue"), ("q", "MX", "Quality"), ("t", "MX", "Timestamp")]),
            ("WYE_TYPE", "WYE", [("phsA", "MX", "CMV"), ("phsB", "MX", "CMV"), ("phsC", "MX", "CMV"), ("neut", "MX", "CMV")]),
            ("DEL_TYPE", "DEL", [("phsAB", "MX", "CMV"), ("phsBC", "MX", "CMV"), ("phsCA", "MX", "CMV")]),
            ("ACD_TYPE", "ACD", [("general", "ST", "BOOLEAN"), ("dirGeneral", "ST", "INT32"), ("q", "ST", "Quality"), ("t", "ST", "Timestamp")]),
            ("ACT_TYPE", "ACT", [("general", "ST", "BOOLEAN"), ("q", "ST", "Quality"), ("t", "ST", "Timestamp")]),
            ("SAV_TYPE", "SAV", [("instMag", "MX", "AnalogueValue"), ("q", "MX", "Quality"), ("t", "MX", "Timestamp")]),
        ]
        
        for dotype_id, cdc, das in dotypes:
            dotype = ET.SubElement(self.data_type_templates, "DOType", {
                "id": dotype_id,
                "cdc": cdc
            })
            
            for da_name, fc, da_type in das:
                ET.SubElement(dotype, "DA", {
                    "name": da_name,
                    "fc": fc,
                    "type": da_type
                })
    
    def save_large_scd(self, file_path):
        """保存大型SCD文件"""
        if self.root is None:
            raise ValueError("没有SCD内容可保存")
        
        # 格式化XML
        rough_string = ET.tostring(self.root, encoding='unicode')
        reparsed = minidom.parseString(rough_string)
        pretty_xml = reparsed.toprettyxml(indent="  ")
        
        # 移除空行
        lines = [line for line in pretty_xml.split('\n') if line.strip()]
        formatted_xml = '\n'.join(lines)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(formatted_xml)
        
        print(f"✅ 大型SCD文件已保存到: {file_path}")

def generate_500_point_scd():
    """生成包含500个数据点的SCD文件"""
    print("=" * 60)
    print("🏗️  生成500个数据点的大型SCD文件")
    print("=" * 60)
    
    generator = LargeSCDGenerator()
    
    # 生成SCD文件
    generator.create_large_scd("TestSubstation_500Points", 500)
    
    # 保存文件
    scd_path = "large_substation_500points.scd"
    generator.save_large_scd(scd_path)
    
    return scd_path

if __name__ == "__main__":
    scd_path = generate_500_point_scd()
    print(f"\n🎉 500个数据点的SCD文件生成完成: {scd_path}")
