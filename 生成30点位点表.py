#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成30个数据点的点表文件
适合快速测试和入门使用
"""

import csv
from datetime import datetime

def generate_30_points():
    """生成30个精选数据点"""
    print("🔄 生成30个数据点的点表文件...")
    
    # CSV文件头
    fieldnames = ['点号', '信号名称', '信号类型', '数据类型', '期望值', '描述', 'SCD路径', 'IED名称']
    
    # 生成文件名
    filename = f"point_table_30points_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    
    with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        # 定义30个精选数据点
        points_data = [
            # IED_001 - 主变保护装置 (10个点)
            (1001, "IED_001_XCBR1_Pos_stVal", "DI", "BOOL", "0", "主变高压侧断路器位置", "IED_001.LD0.XCBR1.Pos.stVal", "IED_001"),
            (1002, "IED_001_XCBR1_BlkOpn_stVal", "DI", "BOOL", "0", "主变高压侧分闸闭锁", "IED_001.LD0.XCBR1.BlkOpn.stVal", "IED_001"),
            (1003, "IED_001_XCBR1_BlkCls_stVal", "DI", "BOOL", "0", "主变高压侧合闸闭锁", "IED_001.LD0.XCBR1.BlkCls.stVal", "IED_001"),
            (1004, "IED_001_PTRC_Str_general", "DI", "BOOL", "0", "主变保护启动", "IED_001.LD0.PTRC.Str.general", "IED_001"),
            (1005, "IED_001_PTRC_Op_general", "DI", "BOOL", "0", "主变保护动作", "IED_001.LD0.PTRC.Op.general", "IED_001"),
            (1006, "IED_001_MMXU1_TotW_mag_f", "AI", "FLOAT", "220.5", "主变有功功率", "IED_001.LD0.MMXU1.TotW.mag.f", "IED_001"),
            (1007, "IED_001_MMXU1_TotVAr_mag_f", "AI", "FLOAT", "45.2", "主变无功功率", "IED_001.LD0.MMXU1.TotVAr.mag.f", "IED_001"),
            (1008, "IED_001_MMXU1_Hz_mag_f", "AI", "FLOAT", "50.0", "系统频率", "IED_001.LD0.MMXU1.Hz.mag.f", "IED_001"),
            (1009, "IED_001_MMXU1_PPV_phsA_cVal_mag_f", "AI", "FLOAT", "220000.0", "A相电压", "IED_001.LD0.MMXU1.PPV.phsA.cVal.mag.f", "IED_001"),
            (1010, "IED_001_CSWI1_Pos_ctlVal", "AO", "BOOL", "0", "主变断路器控制", "IED_001.LD0.CSWI1.Pos.ctlVal", "IED_001"),
            
            # IED_002 - 线路保护装置 (10个点)
            (2001, "IED_002_XCBR1_Pos_stVal", "DI", "BOOL", "1", "线路1断路器位置", "IED_002.LD0.XCBR1.Pos.stVal", "IED_002"),
            (2002, "IED_002_XCBR1_BlkOpn_stVal", "DI", "BOOL", "0", "线路1分闸闭锁", "IED_002.LD0.XCBR1.BlkOpn.stVal", "IED_002"),
            (2003, "IED_002_XCBR1_BlkCls_stVal", "DI", "BOOL", "0", "线路1合闸闭锁", "IED_002.LD0.XCBR1.BlkCls.stVal", "IED_002"),
            (2004, "IED_002_PTRC_Str_general", "DI", "BOOL", "0", "线路保护启动", "IED_002.LD0.PTRC.Str.general", "IED_002"),
            (2005, "IED_002_PTRC_Op_general", "DI", "BOOL", "0", "线路保护动作", "IED_002.LD0.PTRC.Op.general", "IED_002"),
            (2006, "IED_002_MMXU1_TotW_mag_f", "AI", "FLOAT", "180.3", "线路有功功率", "IED_002.LD0.MMXU1.TotW.mag.f", "IED_002"),
            (2007, "IED_002_MMXU1_A_phsA_cVal_mag_f", "AI", "FLOAT", "1000.0", "线路A相电流", "IED_002.LD0.MMXU1.A.phsA.cVal.mag.f", "IED_002"),
            (2008, "IED_002_MMXU1_A_phsB_cVal_mag_f", "AI", "FLOAT", "1000.0", "线路B相电流", "IED_002.LD0.MMXU1.A.phsB.cVal.mag.f", "IED_002"),
            (2009, "IED_002_MMXU1_A_phsC_cVal_mag_f", "AI", "FLOAT", "1000.0", "线路C相电流", "IED_002.LD0.MMXU1.A.phsC.cVal.mag.f", "IED_002"),
            (2010, "IED_002_CSWI1_Pos_ctlVal", "AO", "BOOL", "0", "线路断路器控制", "IED_002.LD0.CSWI1.Pos.ctlVal", "IED_002"),
            
            # IED_003 - 母线保护装置 (10个点)
            (3001, "IED_003_XCBR1_Pos_stVal", "DI", "BOOL", "1", "母线分段断路器位置", "IED_003.LD0.XCBR1.Pos.stVal", "IED_003"),
            (3002, "IED_003_XCBR1_BlkOpn_stVal", "DI", "BOOL", "0", "母线分段分闸闭锁", "IED_003.LD0.XCBR1.BlkOpn.stVal", "IED_003"),
            (3003, "IED_003_XCBR1_BlkCls_stVal", "DI", "BOOL", "0", "母线分段合闸闭锁", "IED_003.LD0.XCBR1.BlkCls.stVal", "IED_003"),
            (3004, "IED_003_PTRC_Str_general", "DI", "BOOL", "0", "母线保护启动", "IED_003.LD0.PTRC.Str.general", "IED_003"),
            (3005, "IED_003_PTRC_Op_general", "DI", "BOOL", "0", "母线保护动作", "IED_003.LD0.PTRC.Op.general", "IED_003"),
            (3006, "IED_003_GGIO_Ind1_stVal", "DI", "BOOL", "0", "母线PT断线", "IED_003.LD0.GGIO.Ind1.stVal", "IED_003"),
            (3007, "IED_003_GGIO_Ind2_stVal", "DI", "BOOL", "0", "母线接地", "IED_003.LD0.GGIO.Ind2.stVal", "IED_003"),
            (3008, "IED_003_MMXU1_PPV_phsA_cVal_mag_f", "AI", "FLOAT", "110000.0", "母线A相电压", "IED_003.LD0.MMXU1.PPV.phsA.cVal.mag.f", "IED_003"),
            (3009, "IED_003_MMXU1_PPV_phsB_cVal_mag_f", "AI", "FLOAT", "110000.0", "母线B相电压", "IED_003.LD0.MMXU1.PPV.phsB.cVal.mag.f", "IED_003"),
            (3010, "IED_003_CSWI1_Pos_ctlVal", "AO", "BOOL", "0", "母线分段控制", "IED_003.LD0.CSWI1.Pos.ctlVal", "IED_003"),
        ]
        
        # 写入所有数据点
        for point_data in points_data:
            writer.writerow({
                '点号': point_data[0],
                '信号名称': point_data[1],
                '信号类型': point_data[2],
                '数据类型': point_data[3],
                '期望值': point_data[4],
                '描述': point_data[5],
                'SCD路径': point_data[6],
                'IED名称': point_data[7]
            })
    
    return filename

def verify_30_points_file(filename):
    """验证生成的30点位文件"""
    print(f"🔍 验证30点位文件: {filename}")
    
    try:
        with open(filename, 'r', encoding='utf-8-sig') as f:
            lines = f.readlines()
            total_lines = len(lines)
            data_lines = total_lines - 1  # 减去标题行
        
        print(f"   ✅ 文件生成成功")
        print(f"   📊 总行数: {total_lines} (含标题)")
        print(f"   📋 数据点数: {data_lines}")
        print(f"   📏 文件大小: {round(len(open(filename, 'rb').read()) / 1024, 1)}KB")
        
        # 读取并分析数据
        import pandas as pd
        df = pd.read_csv(filename, encoding='utf-8-sig')
        
        print(f"   📝 数据预览:")
        for i, row in df.head(5).iterrows():
            print(f"      {i+1}. 点号:{row['点号']}, 名称:{row['信号名称']}, 类型:{row['信号类型']}")
        
        # 统计信号类型
        signal_counts = df['信号类型'].value_counts()
        print(f"   📈 信号类型分布:")
        for signal_type, count in signal_counts.items():
            type_name = {'DI': '遥信', 'AI': '遥测', 'AO': '遥调'}.get(signal_type, signal_type)
            print(f"      {type_name}({signal_type}): {count}个")
        
        # 统计IED分布
        ied_counts = df['IED名称'].value_counts()
        print(f"   🏭 IED设备分布:")
        for ied_name, count in ied_counts.items():
            print(f"      {ied_name}: {count}个数据点")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 文件验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 生成30个数据点的点表文件")
    print("=" * 60)
    print(f"🕐 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 生成文件
    filename = generate_30_points()
    
    # 验证文件
    if verify_30_points_file(filename):
        print(f"\n✅ 30点位点表生成完成!")
        print(f"📄 文件名: {filename}")
        print(f"💡 使用方法:")
        print(f"   1. 在对点机Web界面中选择'配置文件管理'")
        print(f"   2. 点击'选择文件'，选择: {filename}")
        print(f"   3. 点击'加载点表'")
        print(f"   4. 配置通信连接并开始对点测试")
        
        print(f"\n🎯 文件特点:")
        print(f"   📊 数据点数: 30个 (适合快速测试)")
        print(f"   🏭 IED设备: 3个 (主变、线路、母线)")
        print(f"   📋 信号类型: 遥信、遥测、遥调")
        print(f"   🔧 编码格式: UTF-8 with BOM")
        print(f"   ✅ 完全兼容: Auto_Point对点机")
        
        return filename
    else:
        print(f"\n❌ 文件生成失败")
        return None

if __name__ == "__main__":
    result = main()
    
    if result:
        print(f"\n🎉 成功生成30点位点表: {result}")
        print(f"📝 这个文件非常适合:")
        print(f"   - 快速入门测试")
        print(f"   - 功能验证")
        print(f"   - 演示展示")
        print(f"   - 学习使用")
    else:
        print(f"\n💥 文件生成过程中出现问题")
