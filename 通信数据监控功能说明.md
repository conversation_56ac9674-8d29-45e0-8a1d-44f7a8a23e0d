# 通信数据监控功能说明

## 🎯 **功能概述**

Auto_Point对点机现已支持**通信数据监控功能**，当双方建立通信连接成功后，可以弹出专门的监控窗口实时观察数据传输信息。

### 📋 **核心功能**
- **连接状态检测**: 自动检测通信连接状态
- **实时数据监控**: 观察发送和接收的数据流
- **多格式显示**: 支持十六进制、二进制、ASCII码等格式
- **独立监控窗口**: 专门的弹窗界面，不影响主程序操作

## 🎮 **使用方法**

### 第1步: 建立通信连接
1. **访问对点机**: `http://localhost:8080`
2. **进入通信配置**: 点击左侧导航的"通信配置"
3. **配置连接参数**: 
   - 主机地址: localhost 或目标IP
   - 端口号: 102 (IEC 61850标准端口)
   - 协议类型: IEC 61850
4. **测试连接**: 点击"测试连接"按钮

### 第2步: 打开数据监控
1. **连接成功后**: "数据监控"按钮变为可用状态
2. **点击数据监控**: 弹出专门的监控窗口
3. **开始监控**: 在监控窗口中点击"开始监控"
4. **观察数据**: 实时查看数据传输过程

### 第3步: 监控数据传输
1. **发送数据区域**: 左侧显示发送的数据 (TX)
2. **接收数据区域**: 右侧显示接收的数据 (RX)
3. **格式选择**: 选择数据显示格式
4. **统计信息**: 查看传输统计和连接时间

## 📊 **监控窗口功能**

### ✅ **显示格式选择**
- **十六进制 (HEX)**: `68 04 07 00 64 01 A5 16`
- **二进制 (BIN)**: `01101000 00000100 00000111 ...`
- **ASCII码**: 可读的文本格式显示
- **混合显示**: 同时显示HEX和ASCII格式

### 🔧 **监控控制**
- **开始监控**: 启动数据监控功能
- **停止监控**: 暂停数据监控
- **清空数据**: 清除所有监控记录
- **自动滚动**: 自动滚动到最新数据
- **显示时间戳**: 显示每条数据的时间戳

### 📋 **数据显示区域**

#### **发送数据 (TX) - 绿色背景**
```
[14:30:25.123] TX: 68 04 07 00 64 01 A5 16
[14:30:25.456] TX: 68 06 08 00 65 02 01 80 FF 16
```

#### **接收数据 (RX) - 蓝色背景**
```
[14:30:25.789] RX: 68 06 08 00 64 01 01 80 12 16
[14:30:26.012] RX: 68 04 09 00 66 03 B2 16
```

### 📈 **传输统计**
- **发送字节数**: 实时统计发送的数据量
- **接收字节数**: 实时统计接收的数据量
- **错误次数**: 记录通信错误次数
- **连接时间**: 显示连接持续时间

## 🔍 **数据格式解析**

### IEC 61850 通信数据示例

#### **查询命令 (发送)**
```
十六进制: 68 04 07 00 64 01 A5 16
解析:
  68    - 起始字符
  04    - 数据长度
  07    - 控制字段
  00    - 地址字段
  64    - 功能码 (读取)
  01    - 信息对象地址
  A5    - 校验码
  16    - 结束字符
```

#### **响应数据 (接收)**
```
十六进制: 68 06 08 00 64 01 01 80 12 16
解析:
  68    - 起始字符
  06    - 数据长度
  08    - 控制字段
  00    - 地址字段
  64    - 功能码 (读取响应)
  01    - 信息对象地址
  01    - 状态值 (开关状态)
  80    - 品质描述
  12    - 时间戳
  16    - 结束字符
```

## 💡 **使用场景**

### 场景1: 对点测试验证
```
目的: 验证对点数据的正确性
操作: 
1. 建立连接
2. 开启数据监控
3. 执行对点测试
4. 观察数据传输过程
5. 验证发送和接收数据的一致性
```

### 场景2: 通信故障诊断
```
目的: 诊断通信问题
操作:
1. 监控数据传输
2. 查看是否有数据丢失
3. 检查数据格式是否正确
4. 分析错误模式
5. 定位故障原因
```

### 场景3: 协议分析
```
目的: 分析通信协议
操作:
1. 记录完整的通信过程
2. 分析协议格式
3. 验证协议实现
4. 优化通信参数
```

### 场景4: 性能监控
```
目的: 监控通信性能
操作:
1. 统计数据传输量
2. 监控传输速度
3. 检查连接稳定性
4. 评估系统性能
```

## 🔧 **技术特点**

### ✅ **实时监控**
- **低延迟**: 100ms刷新间隔
- **高精度**: 微秒级时间戳
- **大容量**: 支持1000条记录缓存
- **智能滚动**: 自动显示最新数据

### 📊 **数据处理**
- **多格式支持**: HEX/BIN/ASCII/混合
- **智能解析**: 自动识别数据类型
- **错误处理**: 处理编码异常
- **数据过滤**: 可选择显示内容

### 🎮 **用户体验**
- **独立窗口**: 不影响主程序操作
- **可调整大小**: 灵活的窗口布局
- **状态指示**: 清晰的连接状态显示
- **操作简便**: 直观的控制界面

## ⚠️ **注意事项**

### 🔒 **安全考虑**
- **数据敏感性**: 监控数据可能包含敏感信息
- **访问控制**: 确保只有授权人员使用
- **数据保护**: 不要在不安全环境中使用

### 📈 **性能影响**
- **内存使用**: 大量数据监控会占用内存
- **CPU负载**: 实时处理会增加CPU使用
- **网络影响**: 监控本身不影响网络性能

### 🔧 **故障排除**
- **窗口无法打开**: 检查是否已建立连接
- **数据不显示**: 确认监控已启动
- **格式显示异常**: 尝试切换显示格式
- **性能问题**: 清空数据或降低刷新频率

## 🎉 **功能优势**

### ✅ **调试便利**
- **可视化**: 直观显示通信数据
- **实时性**: 即时观察数据传输
- **详细性**: 完整的数据记录
- **分析性**: 支持多种数据格式

### 📊 **专业性**
- **标准兼容**: 支持IEC 61850等标准协议
- **格式完整**: 支持二进制和ASCII显示
- **时间精确**: 微秒级时间戳记录
- **统计完善**: 完整的传输统计信息

### 🎯 **实用性**
- **易于使用**: 简单直观的操作界面
- **功能完整**: 涵盖监控的各个方面
- **集成良好**: 与对点机完美集成
- **扩展性强**: 易于添加新功能

## 🎯 **总结**

**通信数据监控功能**为Auto_Point对点机提供了强大的通信诊断和分析能力：

### 核心价值
- ✅ **实时监控**: 观察通信过程中的数据传输
- ✅ **多格式显示**: 支持二进制码和ASCII码等格式
- ✅ **专业分析**: 提供详细的数据解析和统计
- ✅ **故障诊断**: 帮助快速定位通信问题

### 立即体验
1. **访问对点机**: `http://localhost:8080`
2. **进入通信配置**: 配置连接参数
3. **测试连接**: 建立通信连接
4. **打开数据监控**: 点击"数据监控"按钮
5. **开始监控**: 观察实时数据传输

**🎉 现在您可以实时观察Auto_Point对点机的通信数据传输过程，支持二进制码和ASCII码等多种格式显示！**

---

**功能版本**: v1.0  
**适用系统**: Auto_Point Web风格对点机  
**更新时间**: 2025年7月5日  
**支持协议**: IEC 61850, Modbus, 自定义协议
