# 自动对点机

## 项目简介
本项目为变电站监控信息一体化自动对点机，基于IEC 61850和DL/T 634.5104标准，支持主站（对点机）与模拟子站间遥测遥信自动对点，减少人工比对，提高效率。

## 主要功能
- 支持IEC 61850、DL/T 634.5104协议自动对点
- PySide6图形界面，Windows风格菜单与标签页
- 支持SCD、点表文件导入
- 网络参数、协议、对点速度可配置
- 实时对点进度与结果高亮显示（绿色=一致，红色=不一致）
- 结果筛选与一键导出Excel/CSV报告
- 日志与详细报告查看
- 附带模拟子站（支持点表加载、数据点管理、TCP通信）
- 附带通信测试脚本

## 目录结构
- main_fixed.py：主程序（PySide6 GUI，对点机）
- substation_simulator.py：模拟子站（tkinter GUI+TCP服务）
- core_iec61850.py：通信适配层（libiec61850/TCP JSON）
- config_parser.py：配置/点表解析
- logic.py、mapping.py：业务逻辑
- test_communication.py：通信自动化测试脚本
- test_gui.py：GUI测试脚本
- requirements.txt：依赖文件
- README.md：简要说明
- 使用说明.md：详细使用手册

## 安装与运行

### 1. 环境准备
建议使用conda环境，Python 3.10+
```bash
conda create -n autopoint python=3.10
conda activate autopoint
pip install -r requirements.txt
```

### 2. 启动模拟子站
```bash
python substation_simulator.py
```

### 3. 启动自动对点机
```bash
python main_fixed.py
```

## 快速测试
可用自动化脚本验证通信：
```bash
python test_communication.py
```

## 常见问题
- **PySide6未安装/找不到**：请先激活conda环境并`pip install -r requirements.txt`
- **连接失败**：确认子站已启动，IP/端口正确，防火墙未阻挡
- **对点结果异常**：检查点表、协议选择、日志
- **界面无响应**：检查依赖、Python版本

## 参考与支持
- 详细功能、操作步骤、扩展方法请见`使用说明.md`
- 如有问题请查看日志、代码注释或联系开发者

---

> 本项目依赖libiec61850（如需真实协议），请确保`core_iec61850.py`中DLL路径正确。 