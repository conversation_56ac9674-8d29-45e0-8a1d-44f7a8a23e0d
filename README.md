# Auto_Point 智能对点机

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![PySide6](https://img.shields.io/badge/PySide6-6.0+-green.svg)](https://pyside.org)
[![IEC 61850](https://img.shields.io/badge/IEC%2061850-Compatible-orange.svg)](https://en.wikipedia.org/wiki/IEC_61850)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## 🎯 项目简介

Auto_Point是一个专业的变电站监控信息一体化智能对点机，基于IEC 61850和DL/T 634.5104标准，提供完整的SCD文件解析、点表管理、通信数据监控和自动对点测试功能。

### ✨ 核心特性
- **🔧 智能文件管理**: 同时支持SCD和点表文件，满足对策设备要求
- **📡 实时数据监控**: 通信数据传输监控（二进制码/ASCII码显示）
- **🎯 自动对点测试**: 支持批量和单点测试，可调节测试速度
- **🔍 智能兼容性检查**: 自动检测文件匹配度和兼容性问题
- **🛠️ SCD文件修复**: 自动修复XML命名空间等格式问题
- **📊 专业报告生成**: Excel/HTML/CSV/JSON多格式报告

## 🚀 主要功能

### 📁 配置文件管理
- **SCD文件解析**: 完整支持IEC 61850标准SCD文件
- **点表文件加载**: 支持CSV、Excel格式点表
- **同时加载功能**: 对策设备要求的SCD+点表双重文件支持
- **目录文件选择**: 可视化目录浏览器，支持任意路径选择
- **智能文件分类**: 按大小自动分类（🚀快速/📊功能/🏭性能测试）
- **兼容性检查**: 自动检测文件匹配度，防止配置错误

### 📡 通信配置与监控
- **多协议支持**: IEC 61850、Modbus、DL/T 634.5104
- **连接状态检测**: 智能检测双方通信连接状态
- **实时数据监控**: 独立监控窗口，实时显示数据传输
- **多格式显示**: 十六进制(HEX)、二进制(BIN)、ASCII码、混合显示
- **传输统计**: 发送/接收字节数、错误次数、连接时间
- **数据分析**: 支持IEC 61850、Modbus等协议数据解析

### 🎯 自动对点测试
- **批量自动测试**: 支持大规模数据点自动对点
- **手动单点测试**: 精确测试特定数据点
- **测试速度调节**: 可配置测试间隔和并发数
- **实时进度显示**: 动态显示测试进度和状态
- **结果高亮显示**: 绿色=一致，红色=不一致，直观明了
- **错误分析**: 详细的失败原因分析和建议

### 📊 报告与分析
- **多格式报告**: Excel、HTML、CSV、JSON专业报告
- **统计分析**: 成功率、失败率、性能指标
- **历史记录**: 完整的测试历史和趋势分析
- **结果筛选**: 支持按状态、类型、时间筛选
- **一键导出**: 快速生成和下载报告

## 📁 项目结构

### 核心程序
```
Auto_Point/
├── main_web_functional.py          # 🎮 主程序 (Web风格GUI)
├── substation_simulator.py         # 🏭 模拟子站 (TCP服务)
├── 通信数据监控窗口.py              # 📡 数据监控窗口
└── 目录文件浏览器.py                # 📁 文件浏览器
```

### 功能模块
```
├── core_iec61850.py                # 🔧 IEC 61850通信核心
├── config_parser.py                # ⚙️ 配置解析器
├── logic.py                        # 🧠 业务逻辑
├── mapping.py                      # 🗺️ 数据映射
└── scd_converter.py                # 🔄 SCD转换器
```

### 工具脚本
```
├── 修复SCD文件.py                   # 🛠️ SCD文件修复工具
├── 智能文件匹配检查.py              # 🔍 兼容性检查工具
├── 终极SCD分析工具.py               # 📊 大型SCD分析工具
├── 测试数据监控功能.py              # 🧪 监控功能测试
└── test_communication.py           # 📡 通信测试脚本
```

### 配置文件
```
├── requirements.txt                # 📦 Python依赖
├── 使用指南.md                     # 📖 详细使用手册
└── README.md                       # 📋 项目说明
```

### 测试数据
```
├── scd_30points_*.scd              # 🚀 小型测试文件 (30点位)
├── large_substation_2000points.scd # 📊 中型测试文件 (2000点位)
├── 222/220kVQFB.scd                # 🏭 大型工程文件 (64.4MB)
└── *.csv                           # 📋 点表文件
```

## 🚀 安装与运行

### 系统要求
- **Python**: 3.8+ (推荐 3.10+)
- **操作系统**: Windows 10/11, Linux, macOS
- **内存**: 4GB+ (处理大型SCD文件需要8GB+)
- **存储**: 1GB+ 可用空间

### 1. 环境准备
```bash
# 使用conda创建环境 (推荐)
conda create -n autopoint python=3.10
conda activate autopoint

# 或使用venv
python -m venv autopoint
# Windows
autopoint\Scripts\activate
# Linux/macOS
source autopoint/bin/activate
```

### 2. 安装依赖
```bash
# 安装核心依赖
pip install -r requirements.txt

# 核心依赖包括:
# - PySide6 (GUI框架)
# - pandas (数据处理)
# - openpyxl (Excel支持)
# - chardet (编码检测)
```

### 3. 启动系统

#### 方式一：完整系统启动
```bash
# 1. 启动对点机主程序
python main_web_functional.py

# 2. 启动子站模拟器 (可选，用于测试)
python substation_simulator.py
```

#### 方式二：独立功能测试
```bash
# 测试数据监控功能
python 通信数据监控窗口.py

# 测试文件浏览器
python 目录文件浏览器.py

# 修复SCD文件
python 修复SCD文件.py
```

### 4. 访问Web界面
启动成功后，打开浏览器访问：
```
http://localhost:8080
```

## 🎮 快速开始

### 基础功能测试
```bash
# 1. 启动对点机
python main_web_functional.py

# 2. 访问Web界面
# 打开浏览器: http://localhost:8080

# 3. 加载测试文件
# 选择: scd_30points_20250704_162945.scd (小文件快速测试)

# 4. 配置通信
# IP: localhost, 端口: 102

# 5. 执行对点测试
```

### 高级功能测试
```bash
# 同时加载SCD和点表文件
# 1. 选择SCD文件: large_substation_2000points.scd
# 2. 选择点表文件: 对应的CSV文件
# 3. 执行兼容性检查
# 4. 同时加载双重文件

# 通信数据监控
# 1. 建立通信连接
# 2. 点击"数据监控"按钮
# 3. 选择显示格式 (HEX/BIN/ASCII)
# 4. 观察实时数据传输
```

### 工具脚本测试
```bash
# SCD文件修复
python 修复SCD文件.py

# 兼容性检查
python 智能文件匹配检查.py

# 大型文件分析
python 终极SCD分析工具.py

# 数据监控测试
python 测试数据监控功能.py

# 通信功能测试
python test_communication.py
```

## 📖 使用指南

### 快速入门
1. **文件准备**: 准备SCD文件和点表文件
2. **启动系统**: 运行对点机主程序
3. **加载文件**: 使用同时加载功能或单独加载
4. **配置通信**: 设置IP地址和端口
5. **执行测试**: 运行自动对点测试
6. **查看结果**: 生成和下载测试报告

### 详细文档
- 📋 [使用指南.md](使用指南.md) - 完整使用手册
- 🔧 [同时加载SCD和点表功能说明.md](同时加载SCD和点表功能说明.md)
- 📁 [目录文件选择功能说明.md](目录文件选择功能说明.md)
- 📡 [通信数据监控功能说明.md](通信数据监控功能说明.md)
- 🔍 [对侧子站SCD问题分析与解决方案.md](对侧子站SCD问题分析与解决方案.md)

## ✨ 技术特点

### 🎯 核心优势
- **现代化界面**: 基于PySide6的Web风格GUI
- **标准兼容**: 完整支持IEC 61850标准
- **智能处理**: 自动文件修复和兼容性检查
- **实时监控**: 通信数据传输实时监控
- **专业报告**: 多格式专业测试报告

### 🔧 技术架构
- **模块化设计**: 松耦合的模块化架构
- **多线程处理**: 高效的并发处理能力
- **错误恢复**: 完善的错误处理和恢复机制
- **扩展性强**: 易于添加新协议和功能
- **性能优化**: 支持大型文件和大规模测试

### 📊 支持规模
- **小型测试**: 30-100个数据点
- **中型项目**: 500-2000个数据点
- **大型工程**: 2000+个数据点，支持64MB+文件
- **并发处理**: 支持多线程并发测试
- **内存优化**: 智能内存管理，支持大文件处理

## ⚠️ 注意事项

### 系统要求
- **网络配置**: 确保防火墙允许TCP 102端口通信
- **内存需求**: 大型SCD文件(64MB+)需要8GB+内存
- **文件编码**: 推荐使用UTF-8编码的文件
- **权限要求**: 确保有文件读写权限

### 使用建议
- **渐进测试**: 建议先用小规模数据测试功能
- **文件备份**: 处理重要文件前请备份
- **兼容性检查**: 使用智能检查功能避免文件不匹配
- **监控资源**: 处理大文件时注意系统资源使用

### 故障排除
- **连接问题**: 检查IP地址、端口和防火墙设置
- **文件问题**: 使用SCD修复工具处理格式问题
- **性能问题**: 增加系统内存或使用较小文件
- **兼容性问题**: 使用兼容性检查工具验证文件匹配

## 🤝 贡献与支持

### 贡献指南
欢迎提交Issue和Pull Request来改进项目：
- 🐛 Bug报告
- ✨ 新功能建议
- 📖 文档改进
- 🧪 测试用例

### 技术支持
- 📧 技术问题请提交Issue
- 📋 详细使用方法请参考[使用指南.md](使用指南.md)
- 🔧 开发相关问题请查看代码注释

## 📄 许可证

本项目采用MIT许可证 - 详见 [LICENSE](LICENSE) 文件

## 🙏 致谢

感谢所有为Auto_Point项目做出贡献的开发者和用户！

---

**Auto_Point智能对点机** - 让变电站对点测试更智能、更高效！

> 💡 **提示**: 本项目支持libiec61850真实协议，如需使用请确保`core_iec61850.py`中DLL路径正确。