#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SCD文件生成器和测试工具
用于生成标准的IEC 61850 SCD配置文件
"""

import xml.etree.ElementTree as ET
from xml.dom import minidom
import pandas as pd
from datetime import datetime
import os

class SCDGenerator:
    """SCD文件生成器"""
    
    def __init__(self):
        self.root = None
        self.scl_ns = "http://www.iec.ch/61850/2003/SCL"
        
    def create_basic_scd(self, substation_name="TestSubstation"):
        """创建基础SCD文件结构"""
        # 创建根元素
        self.root = ET.Element("SCL", {
            "version": "2007",
            "revision": "B",
            "release": "4",
            "xmlns": self.scl_ns,
            "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance"
        })
        
        # 添加Header
        header = ET.SubElement(self.root, "Header", {
            "id": f"{substation_name}_SCD",
            "version": "1.0",
            "revision": "1",
            "toolID": "AutoPoint_SCD_Generator",
            "nameStructure": "IEDName"
        })
        
        # 添加History
        history = ET.SubElement(header, "History")
        hitem = ET.SubElement(history, "Hitem", {
            "version": "1.0",
            "revision": "1",
            "when": datetime.now().isoformat(),
            "who": "AutoPoint System",
            "what": "Initial SCD creation"
        })
        
        # 添加Substation
        substation = ET.SubElement(self.root, "Substation", {
            "name": substation_name,
            "desc": f"{substation_name} Configuration"
        })
        
        # 添加VoltageLevel
        voltage_level = ET.SubElement(substation, "VoltageLevel", {
            "name": "220kV",
            "desc": "220kV Voltage Level",
            "nomFreq": "50",
            "numPhases": "3"
        })
        
        # 添加Voltage
        voltage = ET.SubElement(voltage_level, "Voltage", {
            "unit": "V",
            "multiplier": "k"
        })
        voltage.text = "220"
        
        # 添加Bay (间隔)
        bay = ET.SubElement(voltage_level, "Bay", {
            "name": "Bay1",
            "desc": "Main Transformer Bay"
        })
        
        return self.root
    
    def add_ied_device(self, ied_name, ied_type="XCBR", manufacturer="TestManufacturer"):
        """添加IED设备"""
        if self.root is None:
            raise ValueError("请先创建基础SCD结构")
            
        # 查找IED部分，如果不存在则创建
        ied_section = self.root.find("IED[@name='{}']".format(ied_name))
        if ied_section is None:
            ied_section = ET.SubElement(self.root, "IED", {
                "name": ied_name,
                "type": ied_type,
                "manufacturer": manufacturer,
                "configVersion": "1.0",
                "originalSclVersion": "2007",
                "originalSclRevision": "B"
            })
            
        # 添加Services
        services = ET.SubElement(ied_section, "Services")
        dyn_association = ET.SubElement(services, "DynAssociation")
        setting_groups = ET.SubElement(services, "SettingGroups")
        get_directory = ET.SubElement(services, "GetDirectory")
        get_data_object_definition = ET.SubElement(services, "GetDataObjectDefinition")
        data_object_directory = ET.SubElement(services, "DataObjectDirectory")
        get_data_set_value = ET.SubElement(services, "GetDataSetValue")
        read_write = ET.SubElement(services, "ReadWrite")
        
        # 添加AccessPoint
        access_point = ET.SubElement(ied_section, "AccessPoint", {
            "name": "AP1"
        })
        
        # 添加Server
        server = ET.SubElement(access_point, "Server")
        authentication = ET.SubElement(server, "Authentication")
        
        # 添加LDevice
        ldevice = ET.SubElement(server, "LDevice", {
            "inst": "CTRL"
        })
        
        return ied_section
    
    def add_logical_nodes(self, ied_name, ln_configs):
        """添加逻辑节点"""
        ied = self.root.find(f"IED[@name='{ied_name}']")
        if ied is None:
            raise ValueError(f"IED {ied_name} 不存在")
            
        ldevice = ied.find(".//LDevice")
        if ldevice is None:
            raise ValueError(f"IED {ied_name} 中未找到LDevice")
            
        for ln_config in ln_configs:
            ln = ET.SubElement(ldevice, "LN", {
                "lnClass": ln_config["lnClass"],
                "inst": ln_config.get("inst", "1"),
                "lnType": ln_config["lnType"]
            })
            
    def add_data_type_templates(self):
        """添加数据类型模板"""
        if self.root is None:
            raise ValueError("请先创建基础SCD结构")
            
        data_type_templates = ET.SubElement(self.root, "DataTypeTemplates")
        
        # 添加LNodeType - 断路器
        lntype_xcbr = ET.SubElement(data_type_templates, "LNodeType", {
            "id": "XCBR_1",
            "lnClass": "XCBR"
        })
        
        # 位置信息
        do_pos = ET.SubElement(lntype_xcbr, "DO", {
            "name": "Pos",
            "type": "DPC_1"
        })
        
        # 状态信息
        do_beh = ET.SubElement(lntype_xcbr, "DO", {
            "name": "Beh",
            "type": "ENS_1"
        })
        
        # 健康状态
        do_health = ET.SubElement(lntype_xcbr, "DO", {
            "name": "Health",
            "type": "ENS_1"
        })
        
        # 添加DOType - 双点控制
        dotype_dpc = ET.SubElement(data_type_templates, "DOType", {
            "id": "DPC_1",
            "cdc": "DPC"
        })
        
        da_stval = ET.SubElement(dotype_dpc, "DA", {
            "name": "stVal",
            "fc": "ST",
            "type": "BOOLEAN"
        })
        
        da_q = ET.SubElement(dotype_dpc, "DA", {
            "name": "q",
            "fc": "ST",
            "type": "Quality"
        })
        
        da_t = ET.SubElement(dotype_dpc, "DA", {
            "name": "t",
            "fc": "ST",
            "type": "Timestamp"
        })
        
        da_ctlval = ET.SubElement(dotype_dpc, "DA", {
            "name": "ctlVal",
            "fc": "CO",
            "type": "BOOLEAN"
        })
        
        # 添加DOType - 枚举状态
        dotype_ens = ET.SubElement(data_type_templates, "DOType", {
            "id": "ENS_1",
            "cdc": "ENS"
        })
        
        da_stval_ens = ET.SubElement(dotype_ens, "DA", {
            "name": "stVal",
            "fc": "ST",
            "type": "INT32"
        })
        
        da_q_ens = ET.SubElement(dotype_ens, "DA", {
            "name": "q",
            "fc": "ST",
            "type": "Quality"
        })
        
        da_t_ens = ET.SubElement(dotype_ens, "DA", {
            "name": "t",
            "fc": "ST",
            "type": "Timestamp"
        })
        
        return data_type_templates
    
    def save_scd_file(self, file_path):
        """保存SCD文件"""
        if self.root is None:
            raise ValueError("没有SCD内容可保存")
            
        # 格式化XML
        rough_string = ET.tostring(self.root, encoding='unicode')
        reparsed = minidom.parseString(rough_string)
        pretty_xml = reparsed.toprettyxml(indent="  ")
        
        # 移除空行
        lines = [line for line in pretty_xml.split('\n') if line.strip()]
        formatted_xml = '\n'.join(lines)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(formatted_xml)
            
        print(f"SCD文件已保存到: {file_path}")

def generate_test_scd():
    """生成测试用的SCD文件"""
    generator = SCDGenerator()
    
    # 创建基础结构
    generator.create_basic_scd("TestSubstation_220kV")
    
    # 添加IED设备
    generator.add_ied_device("TR1_CTRL", "XCBR", "TestManufacturer")
    generator.add_ied_device("LINE1_CTRL", "XCBR", "TestManufacturer")
    generator.add_ied_device("BUS1_CTRL", "CSWI", "TestManufacturer")
    
    # 添加逻辑节点配置
    ln_configs = [
        {"lnClass": "XCBR", "inst": "1", "lnType": "XCBR_1"},
        {"lnClass": "CSWI", "inst": "1", "lnType": "XCBR_1"},
        {"lnClass": "MMXU", "inst": "1", "lnType": "XCBR_1"}
    ]
    
    generator.add_logical_nodes("TR1_CTRL", ln_configs)
    generator.add_logical_nodes("LINE1_CTRL", ln_configs)
    generator.add_logical_nodes("BUS1_CTRL", ln_configs)
    
    # 添加数据类型模板
    generator.add_data_type_templates()
    
    # 保存文件
    scd_path = "test_substation.scd"
    generator.save_scd_file(scd_path)
    
    return scd_path

def test_scd_parsing():
    """测试SCD文件解析"""
    from config_parser import SCDParser
    
    # 生成测试SCD文件
    scd_path = generate_test_scd()
    
    try:
        # 解析SCD文件
        parser = SCDParser(scd_path)
        
        # 获取IED列表
        ieds = parser.get_ieds()
        print(f"发现IED设备: {ieds}")
        
        # 获取信号列表
        signals = parser.get_signals()
        print(f"发现信号数量: {len(signals)}")
        
        # 转换为点表
        point_table_path = "scd_generated_points.csv"
        df = parser.convert_to_point_table(point_table_path)
        print(f"点表已生成: {point_table_path}")
        print(f"点表包含 {len(df)} 个数据点")
        
        return True
        
    except Exception as e:
        print(f"SCD解析测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=== SCD文件生成器和测试工具 ===")
    print("1. 生成测试SCD文件")
    print("2. 测试SCD文件解析")
    print("3. 生成点表")
    
    # 执行测试
    success = test_scd_parsing()
    
    if success:
        print("\n✅ SCD文件生成和解析测试成功！")
        print("生成的文件:")
        print("- test_substation.scd (SCD配置文件)")
        print("- scd_generated_points.csv (自动生成的点表)")
    else:
        print("\n❌ SCD文件测试失败")
