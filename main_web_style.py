#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Auto_Point Web风格对点机界面
基于详细设计文档的现代化Web界面实现
"""

import sys
import os
import json
import pandas as pd
from datetime import datetime
import time
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
    QSplitter, QTreeWidget, QTreeWidgetItem, QStackedWidget,
    QLabel, QPushButton, QLineEdit, QTextEdit, QTableWidget, QTableWidgetItem,
    QProgressBar, QGroupBox, QFormLayout, QComboBox, QFileDialog,
    QMessageBox, QTabWidget, QFrame, QScrollArea, QGridLayout,
    QHeaderView, QCheckBox, QSpinBox, QDateTimeEdit
)
from PySide6.QtCore import Qt, Q<PERSON>imer, Q<PERSON>hread, Signal, QPropertyAnimation, QEasingCurve
from PySide6.QtGui import QFont, QColor, QPalette, QIcon, QPixmap, QPainter, QBrush
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np

# 设置matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class StatusIndicator(QLabel):
    """状态指示器组件"""
    def __init__(self, text="", status="offline"):
        super().__init__(text)
        self.status = status
        self.setFixedSize(12, 12)
        self.update_status(status)
    
    def update_status(self, status):
        self.status = status
        colors = {
            "online": "#4CAF50",
            "offline": "#F44336", 
            "warning": "#FF9800",
            "processing": "#2196F3"
        }
        color = colors.get(status, "#9E9E9E")
        self.setStyleSheet(f"""
            QLabel {{
                background-color: {color};
                border-radius: 6px;
                border: 1px solid #ddd;
            }}
        """)

class ModernButton(QPushButton):
    """现代化按钮组件"""
    def __init__(self, text="", icon_path="", button_type="primary"):
        super().__init__(text)
        self.button_type = button_type
        self.setMinimumHeight(36)
        self.setFont(QFont("Microsoft YaHei", 9))
        self.apply_style()
    
    def apply_style(self):
        styles = {
            "primary": """
                QPushButton {
                    background-color: #1890ff;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #40a9ff;
                }
                QPushButton:pressed {
                    background-color: #096dd9;
                }
                QPushButton:disabled {
                    background-color: #d9d9d9;
                    color: #999;
                }
            """,
            "secondary": """
                QPushButton {
                    background-color: white;
                    color: #1890ff;
                    border: 1px solid #1890ff;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #f0f8ff;
                }
                QPushButton:pressed {
                    background-color: #e6f7ff;
                }
            """,
            "danger": """
                QPushButton {
                    background-color: #ff4d4f;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: 500;
                }
                QPushButton:hover {
                    background-color: #ff7875;
                }
                QPushButton:pressed {
                    background-color: #d9363e;
                }
            """
        }
        self.setStyleSheet(styles.get(self.button_type, styles["primary"]))

class DataChart(FigureCanvas):
    """数据图表组件"""
    def __init__(self, parent=None, width=5, height=4, dpi=100):
        self.fig = Figure(figsize=(width, height), dpi=dpi)
        super().__init__(self.fig)
        self.setParent(parent)
        self.axes = self.fig.add_subplot(111)
        self.fig.patch.set_facecolor('white')
        
    def plot_data(self, data, chart_type="line"):
        self.axes.clear()
        if chart_type == "line":
            self.axes.plot(data['x'], data['y'], 'b-', linewidth=2)
        elif chart_type == "bar":
            self.axes.bar(data['x'], data['y'], color='#1890ff', alpha=0.7)
        
        self.axes.set_title(data.get('title', ''), fontsize=12, fontweight='bold')
        self.axes.set_xlabel(data.get('xlabel', ''))
        self.axes.set_ylabel(data.get('ylabel', ''))
        self.axes.grid(True, alpha=0.3)
        self.fig.tight_layout()
        self.draw()

class TopBar(QWidget):
    """顶部功能栏"""
    def __init__(self):
        super().__init__()
        self.setFixedHeight(60)
        self.setStyleSheet("""
            QWidget {
                background-color: #001529;
                color: white;
            }
        """)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QHBoxLayout()
        layout.setContentsMargins(20, 0, 20, 0)
        
        # Logo和标题
        logo_layout = QHBoxLayout()
        logo_label = QLabel("⚡")
        logo_label.setFont(QFont("Arial", 20))
        logo_label.setStyleSheet("color: #1890ff;")
        
        title_label = QLabel("变电站监控信息一体化自动对点机")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        
        logo_layout.addWidget(logo_label)
        logo_layout.addWidget(title_label)
        logo_layout.addStretch()
        
        layout.addLayout(logo_layout)
        layout.addStretch()
        
        # 状态指示器
        status_layout = QHBoxLayout()
        
        # 网络状态
        network_label = QLabel("网络状态:")
        network_label.setStyleSheet("color: #8c8c8c;")
        self.network_indicator = StatusIndicator("", "offline")
        
        # 系统时间
        self.time_label = QLabel()
        self.time_label.setStyleSheet("color: #8c8c8c; margin-left: 20px;")
        self.update_time()
        
        # 定时器更新时间
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)
        
        status_layout.addWidget(network_label)
        status_layout.addWidget(self.network_indicator)
        status_layout.addWidget(self.time_label)
        
        layout.addLayout(status_layout)
        self.setLayout(layout)
    
    def update_time(self):
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
    
    def update_network_status(self, status):
        self.network_indicator.update_status(status)

class LeftNavigation(QWidget):
    """左侧导航栏"""
    def __init__(self):
        super().__init__()
        self.setFixedWidth(240)
        self.setStyleSheet("""
            QWidget {
                background-color: #f0f2f5;
                border-right: 1px solid #d9d9d9;
            }
        """)
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(0, 20, 0, 0)
        
        # 导航树
        self.nav_tree = QTreeWidget()
        self.nav_tree.setHeaderHidden(True)
        self.nav_tree.setStyleSheet("""
            QTreeWidget {
                background-color: transparent;
                border: none;
                font-size: 13px;
                outline: none;
            }
            QTreeWidget::item {
                height: 40px;
                padding-left: 20px;
                border: none;
            }
            QTreeWidget::item:selected {
                background-color: #e6f7ff;
                color: #1890ff;
                border-right: 3px solid #1890ff;
            }
            QTreeWidget::item:hover {
                background-color: #f5f5f5;
            }
        """)
        
        # 添加导航项目
        nav_items = [
            ("📁 配置文件管理", ["SCD文件解析", "RCD文件解析", "点表转换"]),
            ("🔧 通信配置", ["网关配置", "通信状态", "网络诊断"]),
            ("🏭 仿真模型管理", ["间隔层设备", "模型参数", "运行状态"]),
            ("📊 遥信遥测管理", ["实时数据", "数据趋势", "告警信息"]),
            ("🎮 遥控验收", ["遥控测试", "验收记录"]),
            ("📋 报告管理", ["验收报告", "历史记录", "模板管理"])
        ]
        
        for parent_text, children in nav_items:
            parent_item = QTreeWidgetItem([parent_text])
            parent_item.setFont(0, QFont("Microsoft YaHei", 10, QFont.Bold))
            self.nav_tree.addTopLevelItem(parent_item)
            
            for child_text in children:
                child_item = QTreeWidgetItem([child_text])
                child_item.setFont(0, QFont("Microsoft YaHei", 9))
                parent_item.addChild(child_item)
        
        # 展开所有项目
        self.nav_tree.expandAll()
        
        layout.addWidget(self.nav_tree)
        layout.addStretch()
        self.setLayout(layout)

class FileManagementWidget(QWidget):
    """配置文件管理界面"""
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(24, 24, 24, 24)
        
        # 页面标题
        title_label = QLabel("配置文件管理")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setStyleSheet("color: #262626; margin-bottom: 16px;")
        layout.addWidget(title_label)
        
        # 文件上传区域
        upload_group = QGroupBox("文件上传")
        upload_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #d9d9d9;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        upload_layout = QVBoxLayout()
        
        # 拖拽上传区域
        upload_area = QFrame()
        upload_area.setMinimumHeight(120)
        upload_area.setStyleSheet("""
            QFrame {
                border: 2px dashed #d9d9d9;
                border-radius: 8px;
                background-color: #fafafa;
            }
            QFrame:hover {
                border-color: #1890ff;
                background-color: #f0f8ff;
            }
        """)
        
        upload_area_layout = QVBoxLayout()
        upload_icon = QLabel("📁")
        upload_icon.setAlignment(Qt.AlignCenter)
        upload_icon.setFont(QFont("Arial", 24))
        
        upload_text = QLabel("拖拽文件到此处或点击上传\n支持 .scd, .rcd, .csv 格式")
        upload_text.setAlignment(Qt.AlignCenter)
        upload_text.setStyleSheet("color: #8c8c8c;")
        
        upload_btn = ModernButton("选择文件", button_type="primary")
        upload_btn.clicked.connect(self.select_file)
        
        upload_area_layout.addWidget(upload_icon)
        upload_area_layout.addWidget(upload_text)
        upload_area_layout.addWidget(upload_btn)
        upload_area.setLayout(upload_area_layout)
        
        upload_layout.addWidget(upload_area)
        upload_group.setLayout(upload_layout)
        layout.addWidget(upload_group)
        
        # 文件列表
        file_list_group = QGroupBox("已上传文件")
        file_list_group.setStyleSheet(upload_group.styleSheet())
        file_list_layout = QVBoxLayout()
        
        self.file_table = QTableWidget()
        self.file_table.setColumnCount(5)
        self.file_table.setHorizontalHeaderLabels(["文件名", "文件类型", "上传时间", "文件大小", "状态"])
        self.file_table.horizontalHeader().setStretchLastSection(True)
        self.file_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #f0f0f0;
                background-color: white;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
            }
            QHeaderView::section {
                background-color: #fafafa;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #f0f0f0;
                font-weight: bold;
            }
        """)
        
        file_list_layout.addWidget(self.file_table)
        file_list_group.setLayout(file_list_layout)
        layout.addWidget(file_list_group)
        
        self.setLayout(layout)
    
    def select_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择配置文件", "", 
            "配置文件 (*.scd *.rcd *.csv);;SCD文件 (*.scd);;RCD文件 (*.rcd);;CSV文件 (*.csv)"
        )
        if file_path:
            self.add_file_to_table(file_path)
    
    def add_file_to_table(self, file_path):
        row = self.file_table.rowCount()
        self.file_table.insertRow(row)
        
        file_name = os.path.basename(file_path)
        file_ext = os.path.splitext(file_name)[1].upper()
        file_size = f"{os.path.getsize(file_path) / 1024:.1f} KB"
        upload_time = datetime.now().strftime("%Y-%m-%d %H:%M")
        
        self.file_table.setItem(row, 0, QTableWidgetItem(file_name))
        self.file_table.setItem(row, 1, QTableWidgetItem(file_ext))
        self.file_table.setItem(row, 2, QTableWidgetItem(upload_time))
        self.file_table.setItem(row, 3, QTableWidgetItem(file_size))
        
        # 状态指示
        status_widget = QWidget()
        status_layout = QHBoxLayout()
        status_layout.setContentsMargins(5, 0, 5, 0)
        
        status_indicator = StatusIndicator("", "online")
        status_label = QLabel("已解析")
        status_label.setStyleSheet("color: #52c41a; font-weight: bold;")
        
        status_layout.addWidget(status_indicator)
        status_layout.addWidget(status_label)
        status_layout.addStretch()
        status_widget.setLayout(status_layout)
        
        self.file_table.setCellWidget(row, 4, status_widget)

class CommunicationWidget(QWidget):
    """通信配置界面"""
    def __init__(self):
        super().__init__()
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(24, 24, 24, 24)

        # 页面标题
        title_label = QLabel("通信配置")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setStyleSheet("color: #262626; margin-bottom: 16px;")
        layout.addWidget(title_label)

        # 配置表单
        config_group = QGroupBox("网关配置参数")
        config_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #d9d9d9;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
        """)

        form_layout = QFormLayout()
        form_layout.setSpacing(16)

        # IP地址
        self.ip_edit = QLineEdit("127.0.0.1")
        self.ip_edit.setStyleSheet("""
            QLineEdit {
                padding: 8px 12px;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                font-size: 13px;
            }
            QLineEdit:focus {
                border-color: #1890ff;
                outline: none;
            }
        """)

        # 端口
        self.port_edit = QLineEdit("102")
        self.port_edit.setStyleSheet(self.ip_edit.styleSheet())

        # 协议选择
        self.protocol_combo = QComboBox()
        self.protocol_combo.addItems(["IEC 61850", "DL/T 634.5104"])
        self.protocol_combo.setStyleSheet("""
            QComboBox {
                padding: 8px 12px;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                font-size: 13px;
                min-width: 150px;
            }
        """)

        form_layout.addRow("IP地址:", self.ip_edit)
        form_layout.addRow("端口:", self.port_edit)
        form_layout.addRow("通信协议:", self.protocol_combo)

        # 连接按钮
        connect_btn = ModernButton("测试连接", button_type="primary")
        connect_btn.clicked.connect(self.test_connection)
        form_layout.addRow("", connect_btn)

        config_group.setLayout(form_layout)
        layout.addWidget(config_group)

        # 通信状态监控
        status_group = QGroupBox("通信状态监控")
        status_group.setStyleSheet(config_group.styleSheet())
        status_layout = QGridLayout()

        # 状态指示器
        status_items = [
            ("网络连接", "online"),
            ("数据通信", "processing"),
            ("设备响应", "online"),
            ("协议状态", "warning")
        ]

        for i, (name, status) in enumerate(status_items):
            row = i // 2
            col = (i % 2) * 2

            indicator = StatusIndicator("", status)
            label = QLabel(name)
            label.setStyleSheet("font-weight: bold; margin-left: 8px;")

            status_layout.addWidget(indicator, row, col)
            status_layout.addWidget(label, row, col + 1)

        status_group.setLayout(status_layout)
        layout.addWidget(status_group)

        layout.addStretch()
        self.setLayout(layout)

    def test_connection(self):
        QMessageBox.information(self, "连接测试", "连接测试成功！")

class DataMonitorWidget(QWidget):
    """数据监控界面"""
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_timer()

    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(24, 24, 24, 24)

        # 页面标题
        title_label = QLabel("遥信遥测管理")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setStyleSheet("color: #262626; margin-bottom: 16px;")
        layout.addWidget(title_label)

        # 统计卡片
        stats_layout = QHBoxLayout()

        stats_data = [
            ("总数据点", "1,256", "#1890ff"),
            ("在线设备", "864", "#52c41a"),
            ("告警数量", "128", "#faad14"),
            ("异常数量", "42", "#ff4d4f")
        ]

        for title, value, color in stats_data:
            card = self.create_stat_card(title, value, color)
            stats_layout.addWidget(card)

        layout.addLayout(stats_layout)

        # 图表区域
        chart_group = QGroupBox("数据趋势图")
        chart_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #d9d9d9;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
        """)

        chart_layout = QVBoxLayout()

        # 创建图表
        self.chart = DataChart(self, width=8, height=4)
        chart_layout.addWidget(self.chart)

        chart_group.setLayout(chart_layout)
        layout.addWidget(chart_group)

        # 实时数据表格
        table_group = QGroupBox("实时数据监控")
        table_group.setStyleSheet(chart_group.styleSheet())
        table_layout = QVBoxLayout()

        self.data_table = QTableWidget()
        self.data_table.setColumnCount(6)
        self.data_table.setHorizontalHeaderLabels([
            "信号名称", "当前值", "单位", "质量", "时间戳", "状态"
        ])
        self.data_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #f0f0f0;
                background-color: white;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
            }
            QHeaderView::section {
                background-color: #fafafa;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #f0f0f0;
                font-weight: bold;
            }
        """)

        # 添加示例数据
        self.populate_data_table()

        table_layout.addWidget(self.data_table)
        table_group.setLayout(table_layout)
        layout.addWidget(table_group)

        self.setLayout(layout)

    def create_stat_card(self, title, value, color):
        """创建统计卡片"""
        card = QFrame()
        card.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #f0f0f0;
                border-radius: 8px;
                padding: 16px;
            }}
        """)
        card.setMinimumHeight(100)

        layout = QVBoxLayout()

        title_label = QLabel(title)
        title_label.setStyleSheet("color: #8c8c8c; font-size: 12px;")

        value_label = QLabel(value)
        value_label.setStyleSheet(f"color: {color}; font-size: 24px; font-weight: bold;")

        layout.addWidget(title_label)
        layout.addWidget(value_label)
        layout.addStretch()

        card.setLayout(layout)
        return card

    def populate_data_table(self):
        """填充数据表格"""
        sample_data = [
            ("TR1_HV_Voltage", "220.5", "kV", "良好", "2024-01-15 14:30:25", "正常"),
            ("TR1_MV_Current", "1000.2", "A", "良好", "2024-01-15 14:30:25", "正常"),
            ("CB1_Position", "分闸", "", "良好", "2024-01-15 14:30:24", "正常"),
            ("PT1_Voltage", "110.1", "kV", "良好", "2024-01-15 14:30:25", "正常"),
            ("CT1_Current", "500.8", "A", "告警", "2024-01-15 14:30:23", "告警"),
        ]

        self.data_table.setRowCount(len(sample_data))

        for row, data in enumerate(sample_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value))
                if col == 5:  # 状态列
                    if value == "告警":
                        item.setBackground(QColor("#fff2e8"))
                        item.setForeground(QColor("#fa8c16"))
                    else:
                        item.setBackground(QColor("#f6ffed"))
                        item.setForeground(QColor("#52c41a"))
                self.data_table.setItem(row, col, item)

    def setup_timer(self):
        """设置定时器更新图表"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_chart)
        self.timer.start(2000)  # 2秒更新一次
        self.update_chart()

    def update_chart(self):
        """更新图表数据"""
        # 生成模拟数据
        x = np.arange(0, 24, 1)
        y = 220 + 10 * np.sin(x * 0.5) + np.random.normal(0, 2, len(x))

        data = {
            'x': x,
            'y': y,
            'title': '电压趋势图 (24小时)',
            'xlabel': '时间 (小时)',
            'ylabel': '电压 (kV)'
        }

        self.chart.plot_data(data, "line")

class ReportWidget(QWidget):
    """报告管理界面"""
    def __init__(self):
        super().__init__()
        self.setup_ui()

    def setup_ui(self):
        layout = QVBoxLayout()
        layout.setContentsMargins(24, 24, 24, 24)

        # 页面标题
        title_label = QLabel("报告管理")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setStyleSheet("color: #262626; margin-bottom: 16px;")
        layout.addWidget(title_label)

        # 操作按钮区域
        action_layout = QHBoxLayout()

        generate_btn = ModernButton("生成验收报告", button_type="primary")
        generate_btn.clicked.connect(self.generate_report)

        export_btn = ModernButton("导出报告", button_type="secondary")
        export_btn.clicked.connect(self.export_report)

        template_btn = ModernButton("模板管理", button_type="secondary")

        action_layout.addWidget(generate_btn)
        action_layout.addWidget(export_btn)
        action_layout.addWidget(template_btn)
        action_layout.addStretch()

        layout.addLayout(action_layout)

        # 报告列表
        report_group = QGroupBox("验收报告列表")
        report_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                border: 2px solid #d9d9d9;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
        """)

        report_layout = QVBoxLayout()

        self.report_table = QTableWidget()
        self.report_table.setColumnCount(6)
        self.report_table.setHorizontalHeaderLabels([
            "报告名称", "生成时间", "测试点数", "正确率", "状态", "操作"
        ])
        self.report_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #f0f0f0;
                background-color: white;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
            }
            QHeaderView::section {
                background-color: #fafafa;
                padding: 8px;
                border: none;
                border-bottom: 1px solid #f0f0f0;
                font-weight: bold;
            }
        """)

        # 添加示例报告数据
        self.populate_report_table()

        report_layout.addWidget(self.report_table)
        report_group.setLayout(report_layout)
        layout.addWidget(report_group)

        self.setLayout(layout)

    def populate_report_table(self):
        """填充报告表格"""
        sample_reports = [
            ("110kV变电站验收报告", "2024-01-15 14:30", "1,256", "99.2%", "已完成"),
            ("220kV线路对点报告", "2024-01-14 09:15", "864", "100.0%", "已完成"),
            ("系统综合测试报告", "2024-01-13 16:45", "2,048", "98.8%", "已完成"),
        ]

        self.report_table.setRowCount(len(sample_reports))

        for row, data in enumerate(sample_reports):
            for col, value in enumerate(data):
                if col == 4:  # 状态列
                    status_widget = QWidget()
                    status_layout = QHBoxLayout()
                    status_layout.setContentsMargins(5, 0, 5, 0)

                    status_indicator = StatusIndicator("", "online")
                    status_label = QLabel(value)
                    status_label.setStyleSheet("color: #52c41a; font-weight: bold;")

                    status_layout.addWidget(status_indicator)
                    status_layout.addWidget(status_label)
                    status_layout.addStretch()
                    status_widget.setLayout(status_layout)

                    self.report_table.setCellWidget(row, col, status_widget)
                elif col == 5:  # 操作列
                    action_widget = QWidget()
                    action_layout = QHBoxLayout()
                    action_layout.setContentsMargins(5, 0, 5, 0)

                    view_btn = ModernButton("查看", button_type="secondary")
                    view_btn.setMaximumWidth(60)
                    download_btn = ModernButton("下载", button_type="secondary")
                    download_btn.setMaximumWidth(60)

                    action_layout.addWidget(view_btn)
                    action_layout.addWidget(download_btn)
                    action_layout.addStretch()
                    action_widget.setLayout(action_layout)

                    self.report_table.setCellWidget(row, col, action_widget)
                else:
                    item = QTableWidgetItem(str(value))
                    if col == 3 and "100.0%" in value:  # 正确率列
                        item.setForeground(QColor("#52c41a"))
                        item.setBackground(QColor("#f6ffed"))
                    self.report_table.setItem(row, col, item)

    def generate_report(self):
        QMessageBox.information(self, "生成报告", "验收报告生成成功！")

    def export_report(self):
        QMessageBox.information(self, "导出报告", "报告导出成功！")

class MainWindow(QMainWindow):
    """主窗口"""
    def __init__(self):
        super().__init__()
        self.setWindowTitle("变电站监控信息一体化自动对点机 - Web风格界面")
        self.setGeometry(100, 100, 1400, 900)
        self.setup_ui()
        self.apply_global_style()
    
    def setup_ui(self):
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 顶部栏
        self.top_bar = TopBar()
        main_layout.addWidget(self.top_bar)
        
        # 内容区域
        content_splitter = QSplitter(Qt.Horizontal)
        
        # 左侧导航
        self.left_nav = LeftNavigation()
        content_splitter.addWidget(self.left_nav)
        
        # 右侧主内容区
        self.content_stack = QStackedWidget()
        self.content_stack.setStyleSheet("background-color: white;")
        
        # 添加各个功能页面
        self.file_management = FileManagementWidget()
        self.communication = CommunicationWidget()
        self.data_monitor = DataMonitorWidget()
        self.report_management = ReportWidget()

        self.content_stack.addWidget(self.file_management)
        self.content_stack.addWidget(self.communication)
        self.content_stack.addWidget(self.data_monitor)
        self.content_stack.addWidget(self.report_management)

        # 页面映射
        self.page_mapping = {
            "SCD文件解析": self.file_management,
            "RCD文件解析": self.file_management,
            "点表转换": self.file_management,
            "网关配置": self.communication,
            "通信状态": self.communication,
            "网络诊断": self.communication,
            "实时数据": self.data_monitor,
            "数据趋势": self.data_monitor,
            "告警信息": self.data_monitor,
            "验收报告": self.report_management,
            "历史记录": self.report_management,
            "模板管理": self.report_management,
        }

        content_splitter.addWidget(self.content_stack)
        content_splitter.setSizes([240, 1160])

        main_layout.addWidget(content_splitter)
        central_widget.setLayout(main_layout)

        # 连接导航信号
        self.left_nav.nav_tree.itemClicked.connect(self.on_nav_clicked)

        # 默认显示文件管理页面
        self.content_stack.setCurrentWidget(self.file_management)
    
    def on_nav_clicked(self, item, column):
        """导航点击处理"""
        if item.parent() is None:  # 父级项目
            return

        item_text = item.text(0)
        print(f"导航到: {item_text}")

        # 根据导航项目切换到对应页面
        if item_text in self.page_mapping:
            target_page = self.page_mapping[item_text]
            self.content_stack.setCurrentWidget(target_page)

            # 更新网络状态（模拟）
            if item_text in ["网关配置", "通信状态"]:
                self.top_bar.update_network_status("online")
            elif item_text in ["实时数据", "数据趋势"]:
                self.top_bar.update_network_status("processing")
        else:
            # 默认显示文件管理页面
            self.content_stack.setCurrentWidget(self.file_management)
    
    def apply_global_style(self):
        """应用全局样式"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f0f2f5;
            }
            QSplitter::handle {
                background-color: #d9d9d9;
                width: 1px;
            }
        """)

def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    # 设置字体
    font = QFont("Microsoft YaHei", 9)
    app.setFont(font)
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
