#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件校核模块
支持SCD、RCD和监控信息点表的有效性和一致性校核
"""

import os
import json
import pandas as pd
from datetime import datetime
from config_parser import SCDParser, parse_point_table
import xml.etree.ElementTree as ET

class ConfigValidator:
    """配置文件校核器"""
    
    def __init__(self):
        self.validation_results = {}
        self.errors = []
        self.warnings = []
        
    def validate_scd_file(self, scd_path):
        """校核SCD文件有效性"""
        print(f"正在校核SCD文件: {scd_path}")
        
        validation_result = {
            'file_path': scd_path,
            'file_exists': False,
            'xml_valid': False,
            'iec61850_compliant': False,
            'ied_count': 0,
            'signal_count': 0,
            'errors': [],
            'warnings': []
        }
        
        try:
            # 检查文件存在性
            if not os.path.exists(scd_path):
                validation_result['errors'].append(f"SCD文件不存在: {scd_path}")
                return validation_result
            
            validation_result['file_exists'] = True
            
            # 检查XML格式有效性
            try:
                tree = ET.parse(scd_path)
                root = tree.getroot()
                validation_result['xml_valid'] = True
            except ET.ParseError as e:
                validation_result['errors'].append(f"XML格式错误: {e}")
                return validation_result
            
            # 检查IEC 61850标准符合性
            if root.tag != "SCL":
                validation_result['errors'].append("根元素不是SCL，不符合IEC 61850标准")
            else:
                validation_result['iec61850_compliant'] = True
            
            # 使用SCDParser进行详细分析
            parser = SCDParser(scd_path)
            
            # 统计IED数量
            ieds = parser.get_ieds()
            validation_result['ied_count'] = len(ieds)
            
            if len(ieds) == 0:
                validation_result['warnings'].append("未找到任何IED设备")
            
            # 统计信号数量
            signals = parser.get_signals()
            validation_result['signal_count'] = len(signals)
            
            if len(signals) == 0:
                validation_result['warnings'].append("未找到任何信号定义")
            
            # 检查必要的元素
            required_elements = ['Header', 'Substation', 'DataTypeTemplates']
            for element in required_elements:
                if root.find(f".//{element}") is None:
                    validation_result['warnings'].append(f"缺少推荐元素: {element}")
            
            print(f"✅ SCD文件校核完成: {len(ieds)} 个IED, {len(signals)} 个信号")
            
        except Exception as e:
            validation_result['errors'].append(f"SCD文件校核异常: {e}")
        
        return validation_result
    
    def validate_point_table(self, point_table_path):
        """校核点表文件有效性"""
        print(f"正在校核点表文件: {point_table_path}")
        
        validation_result = {
            'file_path': point_table_path,
            'file_exists': False,
            'format_valid': False,
            'point_count': 0,
            'signal_types': {},
            'errors': [],
            'warnings': []
        }
        
        try:
            # 检查文件存在性
            if not os.path.exists(point_table_path):
                validation_result['errors'].append(f"点表文件不存在: {point_table_path}")
                return validation_result
            
            validation_result['file_exists'] = True
            
            # 解析点表
            try:
                points = parse_point_table(point_table_path)
                validation_result['format_valid'] = True
                validation_result['point_count'] = len(points)
            except Exception as e:
                validation_result['errors'].append(f"点表格式错误: {e}")
                return validation_result
            
            # 统计信号类型
            signal_types = {}
            required_fields = ['SignalName', 'ExpectedValue', 'IED', 'DataType']
            missing_fields = set()
            
            for point in points:
                # 检查必要字段
                for field in required_fields:
                    if field not in point or not point[field]:
                        missing_fields.add(field)
                
                # 统计信号类型
                signal_type = point.get('DataType', '未知')
                signal_types[signal_type] = signal_types.get(signal_type, 0) + 1
            
            validation_result['signal_types'] = signal_types
            
            # 报告缺失字段
            if missing_fields:
                validation_result['warnings'].append(f"部分数据点缺少字段: {list(missing_fields)}")
            
            # 检查信号类型合理性
            valid_types = ['遥测', '遥信', '遥控']
            invalid_types = set(signal_types.keys()) - set(valid_types)
            if invalid_types:
                validation_result['warnings'].append(f"发现非标准信号类型: {list(invalid_types)}")
            
            print(f"✅ 点表文件校核完成: {len(points)} 个数据点")
            
        except Exception as e:
            validation_result['errors'].append(f"点表文件校核异常: {e}")
        
        return validation_result
    
    def validate_scd_point_table_consistency(self, scd_path, point_table_path):
        """校核SCD文件与点表的一致性"""
        print(f"正在校核SCD与点表一致性...")
        
        consistency_result = {
            'scd_file': scd_path,
            'point_table_file': point_table_path,
            'consistency_rate': 0.0,
            'common_signals': 0,
            'scd_only_signals': 0,
            'point_table_only_signals': 0,
            'total_scd_signals': 0,
            'total_point_table_signals': 0,
            'missing_in_point_table': [],
            'missing_in_scd': [],
            'type_mismatches': [],
            'errors': [],
            'warnings': []
        }
        
        try:
            # 解析SCD文件
            parser = SCDParser(scd_path)
            scd_signals = parser.get_signals()
            
            # 解析点表
            point_table = parse_point_table(point_table_path)
            
            # 构建信号映射
            scd_signal_map = {sig['SignalName']: sig for sig in scd_signals}
            point_table_map = {pt.get('SignalName', ''): pt for pt in point_table}
            
            scd_signal_names = set(scd_signal_map.keys())
            point_table_names = set(point_table_map.keys())
            
            # 统计信息
            consistency_result['total_scd_signals'] = len(scd_signal_names)
            consistency_result['total_point_table_signals'] = len(point_table_names)
            
            # 找出共同信号
            common_signals = scd_signal_names & point_table_names
            consistency_result['common_signals'] = len(common_signals)
            
            # 找出差异
            scd_only = scd_signal_names - point_table_names
            point_table_only = point_table_names - scd_signal_names
            
            consistency_result['scd_only_signals'] = len(scd_only)
            consistency_result['point_table_only_signals'] = len(point_table_only)
            consistency_result['missing_in_point_table'] = list(scd_only)[:10]  # 只显示前10个
            consistency_result['missing_in_scd'] = list(point_table_only)[:10]
            
            # 计算一致性率
            total_unique_signals = len(scd_signal_names | point_table_names)
            if total_unique_signals > 0:
                consistency_result['consistency_rate'] = len(common_signals) / total_unique_signals * 100
            
            # 检查类型匹配
            type_mismatches = []
            for signal_name in common_signals:
                scd_type = scd_signal_map[signal_name].get('DataType', '')
                pt_type = point_table_map[signal_name].get('DataType', '')
                
                if scd_type != pt_type:
                    type_mismatches.append({
                        'signal_name': signal_name,
                        'scd_type': scd_type,
                        'point_table_type': pt_type
                    })
            
            consistency_result['type_mismatches'] = type_mismatches[:10]  # 只显示前10个
            
            # 生成警告
            if consistency_result['consistency_rate'] < 80:
                consistency_result['warnings'].append(f"一致性率较低: {consistency_result['consistency_rate']:.1f}%")
            
            if len(scd_only) > 0:
                consistency_result['warnings'].append(f"SCD中有 {len(scd_only)} 个信号在点表中缺失")
            
            if len(point_table_only) > 0:
                consistency_result['warnings'].append(f"点表中有 {len(point_table_only)} 个信号在SCD中缺失")
            
            if len(type_mismatches) > 0:
                consistency_result['warnings'].append(f"发现 {len(type_mismatches)} 个信号类型不匹配")
            
            print(f"✅ 一致性校核完成: 一致性率 {consistency_result['consistency_rate']:.1f}%")
            
        except Exception as e:
            consistency_result['errors'].append(f"一致性校核异常: {e}")
        
        return consistency_result
    
    def generate_validation_report(self, output_path="validation_report.json"):
        """生成校核报告"""
        report = {
            'validation_time': datetime.now().isoformat(),
            'summary': {
                'total_files_validated': len(self.validation_results),
                'total_errors': sum(len(result.get('errors', [])) for result in self.validation_results.values()),
                'total_warnings': sum(len(result.get('warnings', [])) for result in self.validation_results.values())
            },
            'results': self.validation_results
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"校核报告已生成: {output_path}")
        return report
    
    def comprehensive_validation(self, scd_path=None, point_table_path=None, output_dir="validation_results"):
        """综合校核"""
        print("=== 开始综合配置文件校核 ===")
        
        os.makedirs(output_dir, exist_ok=True)
        
        # 校核SCD文件
        if scd_path:
            scd_result = self.validate_scd_file(scd_path)
            self.validation_results['scd'] = scd_result
        
        # 校核点表文件
        if point_table_path:
            pt_result = self.validate_point_table(point_table_path)
            self.validation_results['point_table'] = pt_result
        
        # 校核一致性
        if scd_path and point_table_path:
            consistency_result = self.validate_scd_point_table_consistency(scd_path, point_table_path)
            self.validation_results['consistency'] = consistency_result
        
        # 生成报告
        report_path = os.path.join(output_dir, "comprehensive_validation_report.json")
        report = self.generate_validation_report(report_path)
        
        # 生成简化的文本报告
        text_report_path = os.path.join(output_dir, "validation_summary.txt")
        self._generate_text_summary(text_report_path)
        
        print(f"\n✅ 综合校核完成！")
        print(f"详细报告: {report_path}")
        print(f"摘要报告: {text_report_path}")
        
        return report
    
    def _generate_text_summary(self, output_path):
        """生成文本摘要报告"""
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("配置文件校核摘要报告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"校核时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            for key, result in self.validation_results.items():
                f.write(f"{key.upper()} 校核结果:\n")
                f.write("-" * 30 + "\n")
                
                if 'errors' in result:
                    f.write(f"错误数量: {len(result['errors'])}\n")
                    for error in result['errors']:
                        f.write(f"  ❌ {error}\n")
                
                if 'warnings' in result:
                    f.write(f"警告数量: {len(result['warnings'])}\n")
                    for warning in result['warnings']:
                        f.write(f"  ⚠️  {warning}\n")
                
                f.write("\n")

def test_config_validator():
    """测试配置校核器"""
    validator = ConfigValidator()
    
    # 测试点表校核
    if os.path.exists("test_points_fixed.csv"):
        print("测试点表校核...")
        result = validator.validate_point_table("test_points_fixed.csv")
        print(f"点表校核结果: {result}")
    
    # 如果有SCD文件，测试SCD校核
    if os.path.exists("test_substation.scd"):
        print("测试SCD校核...")
        result = validator.validate_scd_file("test_substation.scd")
        print(f"SCD校核结果: {result}")

if __name__ == "__main__":
    test_config_validator()
