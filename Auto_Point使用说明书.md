# Auto_Point Web风格对点机使用说明书

## 📋 目录

1. [产品概述](#产品概述)
2. [系统要求](#系统要求)
3. [安装与启动](#安装与启动)
4. [界面介绍](#界面介绍)
5. [功能操作指南](#功能操作指南)
6. [故障排除](#故障排除)
7. [技术支持](#技术支持)

---

## 📖 产品概述

### 产品名称
**变电站监控信息一体化自动对点机 - Web风格版**

### 产品简介
Auto_Point Web风格对点机是一款专为变电站监控信息系统设计的现代化自动对点工具。采用Web风格界面设计，支持SCD/RCD配置文件解析、网络通信配置、自动对点测试等核心功能，为变电站运维人员提供高效、直观的对点验收解决方案。

### 主要特性
- 🎨 **现代化Web界面**: Material Design风格，操作直观
- 📁 **多格式文件支持**: SCD、RCD、CSV文件解析
- 🔧 **智能通信配置**: 支持IEC 61850、DL/T 634.5104协议
- 🎮 **自动对点测试**: 完整的信号验证流程
- 📊 **专业数据可视化**: 实时图表、统计分析
- 📋 **验收报告生成**: 自动生成专业报告

### 适用场景
- 变电站新建工程验收
- 设备改造后的信号核对
- 定期维护检查
- 系统升级验证

---

## 💻 系统要求

### 硬件要求
- **处理器**: Intel i3或同等性能以上
- **内存**: 4GB RAM (推荐8GB)
- **存储**: 500MB可用磁盘空间
- **网络**: 支持TCP/IP网络连接

### 软件要求
- **操作系统**: Windows 10/11, Linux, macOS
- **Python版本**: Python 3.8或更高版本
- **依赖库**: PySide6, pandas, matplotlib, numpy

### 网络要求
- **端口**: 102 (IEC 61850标准端口)
- **协议**: TCP/IP
- **带宽**: 10Mbps或更高

---

## 🚀 安装与启动

### 安装步骤

#### 1. 环境准备
```bash
# 检查Python版本
python --version

# 安装依赖库
pip install PySide6 pandas matplotlib numpy
```

#### 2. 程序文件
确保以下文件存在于工作目录：
```
Auto_Point/
├── main_web_functional.py    # 主程序文件
├── substation_optimized.py   # 子站模拟器
├── large_substation_500points.scd  # 测试文件
├── large_points_500.csv      # 测试文件
└── Auto_Point使用说明书.md   # 本说明书
```

### 启动程序

#### 1. 启动子站模拟器 (可选)
```bash
python substation_optimized.py
```

#### 2. 启动Web风格对点机
```bash
python main_web_functional.py
```

#### 3. 验证启动
程序启动后会显示：
```
🌐 Auto_Point Web风格对点机 - 功能完整版
==================================================
✅ 界面已启动，支持以下功能:
   📁 配置文件管理 - 真实文件解析
   🔧 通信配置 - 实际连接测试
   🎮 自动对点 - 完整测试流程
   📊 实时状态 - 动态状态更新
==================================================
```

---

## 🖥️ 界面介绍

### 整体布局

```
┌─────────────────────────────────────────────────────────────────┐
│                        顶部功能栏                                │
│ ⚡ 变电站监控信息一体化自动对点机    🌐 网络状态 🕐 系统时间    │
├─────────────────┬───────────────────────────────────────────────┤
│                 │                                               │
│   左侧导航栏     │              主内容区域                        │
│                 │                                               │
│ 📁 配置文件管理  │  ┌─────────────────────────────────────────┐  │
│ 🔧 通信配置     │  │            功能页面内容                  │  │
│ 🏭 仿真模型管理  │  │                                         │  │
│ 📊 遥信遥测管理  │  │  • 文件上传区域                          │  │
│ 🎮 遥控验收     │  │  • 配置表单                              │  │
│ 📋 报告管理     │  │  • 数据表格                              │  │
│                 │  │  • 图表展示                              │  │
│                 │  └─────────────────────────────────────────┘  │
└─────────────────┴───────────────────────────────────────────────┘
```

### 界面组件说明

#### 顶部功能栏
- **系统标题**: 显示产品名称和版本信息
- **网络状态指示器**: 实时显示连接状态
  - 🟢 绿色: 连接正常
  - 🔴 红色: 连接断开
  - 🟠 橙色: 连接异常
  - 🔵 蓝色: 正在连接
- **系统时间**: 实时显示当前时间

#### 左侧导航栏
- **📁 配置文件管理**: SCD/RCD文件解析、点表转换
- **🔧 通信配置**: 网关配置、通信状态、网络诊断
- **🏭 仿真模型管理**: 间隔层设备、模型参数、运行状态
- **📊 遥信遥测管理**: 实时数据、数据趋势、告警信息
- **🎮 遥控验收**: 自动对点、手动测试、验收记录
- **📋 报告管理**: 验收报告、历史记录、模板管理

#### 主内容区
根据左侧导航选择动态显示不同功能页面内容。

---

## 📚 功能操作指南

### 1. 配置文件管理

#### 1.1 SCD文件解析

**操作步骤**:
1. 点击左侧导航 "📁 配置文件管理" → "SCD文件解析"
2. 点击 "选择文件" 按钮
3. 选择SCD配置文件 (推荐: `large_substation_500points.scd`)
4. 点击 "解析文件" 按钮
5. 等待解析完成，查看结果

**解析结果**:
- **文件信息**: 显示文件路径、大小、类型等基本信息
- **数据预览**: 表格形式展示解析出的数据点信息
- **统计信息**: IED数量、数据集数量等统计数据

**支持格式**:
- `.scd` - IEC 61850配置文件
- `.rcd` - 配置描述文件  
- `.csv` - 逗号分隔值文件

#### 1.2 SCD到点表转换 ⭐ 新功能

**功能说明**: 将SCD文件自动转换为子站可用的标准点表格式，解决"对点机使用SCD文件，但子站只能调用点表"的核心问题

**操作步骤**:
1. 完成SCD文件解析
2. 点击 "转换为点表" 按钮（解析SCD文件后自动启用）
3. 系统自动执行转换过程：
   - 提取信号点信息
   - 智能分配地址
   - 生成标准点表
4. 查看转换结果和统计信息
5. 系统自动保存点表文件（CSV格式）

**转换特性**:
- **智能地址分配**:
  - 遥信(DI): 1001起始
  - 遥测(AI): 2001起始
  - 遥控(DO): 3001起始
  - 遥调(AO): 4001起始
- **多格式支持**: CSV、JSON、XML格式
- **完整映射**: 保持SCD路径与点表地址的对应关系
- **质量保证**: 自动检查地址冲突和数据完整性

**转换结果示例**:
```csv
点号,信号名称,信号类型,数据类型,期望值,描述,SCD路径
1001,IED_001_XCBR1_Pos_stVal,DI,BOOL,0,断路器位置,IED_001.LD0.XCBR1.Pos.stVal
2001,IED_001_MMXU1_TotW_mag,AI,FLOAT,0.0,有功功率,IED_001.LD0.MMXU1.TotW.mag
```

### 2. 通信配置

#### 2.1 网关配置

**操作步骤**:
1. 点击左侧导航 "🔧 通信配置" → "网关配置"
2. 设置通信参数:
   - **IP地址**: 输入目标设备IP (如: 127.0.0.1)
   - **端口**: 输入通信端口 (默认: 102)
   - **协议**: 选择通信协议 (IEC 61850 / DL/T 634.5104)
3. 点击 "测试连接" 按钮验证连接
4. 点击 "保存配置" 按钮保存设置

**连接测试**:
- 测试过程中会显示实时状态
- 成功连接会显示绿色提示
- 失败连接会显示错误信息和解决建议

#### 2.2 点表部署 ⭐ 新功能

**功能说明**: 将转换生成的点表部署到子站设备，确保子站具有可用的信号点配置

**操作步骤**:
1. 确保已完成SCD到点表转换
2. 在通信配置页面点击 "部署点表" 按钮
3. 系统自动选择最新生成的点表文件
4. 配置目标子站连接参数
5. 执行部署过程：
   - 连接目标子站
   - 验证兼容性
   - 上传点表文件
   - 配置信号点
   - 验证完整性

**部署结果**: 子站获得完整的信号点配置，可进行后续对点验证

#### 2.3 通信状态监控

**功能说明**: 实时监控网络连接状态和通信质量

**状态指示**:
- **网络连接**: 显示TCP连接状态
- **数据通信**: 显示数据传输状态
- **设备响应**: 显示设备响应时间
- **协议状态**: 显示协议解析状态

### 3. 自动对点测试

#### 3.1 测试配置

**操作步骤**:
1. 点击左侧导航 "🎮 遥控验收" → "自动对点"
2. 配置测试参数:
   - **测试模式**: 选择自动对点/手动测试/批量验证
   - **测试范围**: 选择全部信号/遥信信号/遥测信号/自定义
3. 确认通信配置正确
4. 点击 "开始测试" 按钮

#### 3.2 测试执行

**测试流程**:
1. **初始化**: 检查配置文件和通信连接
2. **连接子站**: 建立与目标设备的通信
3. **读取配置**: 获取信号点配置信息
4. **执行对点**: 逐个验证信号点数据
5. **验证一致性**: 比较期望值与实际值
6. **生成报告**: 汇总测试结果

**进度监控**:
- 实时进度条显示测试进度
- 状态信息显示当前执行步骤
- 可随时停止测试

#### 3.3 结果分析

**测试结果表格**:
| 信号名称 | 期望值 | 实际值 | 测试结果 | 备注 |
|----------|--------|--------|----------|------|
| IED1.Test1 | 100 | 100 | ✅ 通过 | 数值匹配 |
| IED2.Test5 | 300 | 298 | ❌ 失败 | 数值偏差 |

**结果统计**:
- 总测试点数
- 通过点数
- 失败点数  
- 成功率百分比

### 4. 数据监控

#### 4.1 实时数据监控

**功能说明**: 实时显示遥信遥测数据

**操作步骤**:
1. 点击左侧导航 "📊 遥信遥测管理" → "实时数据"
2. 查看统计卡片信息
3. 观察数据表格实时更新
4. 分析数据趋势图表

**统计卡片**:
- **总数据点**: 显示配置的总信号点数
- **在线设备**: 显示当前在线的设备数量
- **告警数量**: 显示当前告警信号数量
- **异常数量**: 显示异常状态信号数量

#### 4.2 数据可视化

**趋势图表**:
- 24小时电压趋势图
- 自动数据更新 (2秒间隔)
- 支持缩放和平移操作

**数据表格**:
- 实时遥信遥测数据显示
- 状态颜色编码 (正常/告警)
- 支持排序和筛选

### 5. 报告管理

#### 5.1 验收报告生成

**操作步骤**:
1. 点击左侧导航 "📋 报告管理" → "验收报告"
2. 点击 "生成验收报告" 按钮
3. 系统自动汇总测试数据
4. 生成标准格式验收报告

**报告内容**:
- 测试基本信息 (时间、人员、设备)
- 测试结果统计
- 详细测试数据
- 问题分析和建议

#### 5.2 报告导出

**支持格式**:
- PDF格式 (推荐用于正式文档)
- Excel格式 (便于数据分析)
- Word格式 (便于编辑修改)

**操作步骤**:
1. 选择要导出的报告
2. 点击 "导出报告" 按钮
3. 选择导出格式和保存位置
4. 确认导出完成

---

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 程序启动失败

**问题现象**: 双击程序无响应或报错

**可能原因**:
- Python环境未正确安装
- 缺少必要的依赖库
- 文件权限不足

**解决方案**:
```bash
# 检查Python版本
python --version

# 重新安装依赖
pip install --upgrade PySide6 pandas matplotlib numpy

# 使用管理员权限运行
```

#### 2. 文件解析失败

**问题现象**: 选择文件后解析失败

**可能原因**:
- 文件格式不支持
- 文件损坏或编码问题
- 文件过大导致内存不足

**解决方案**:
1. 确认文件格式为SCD/RCD/CSV
2. 检查文件是否完整
3. 尝试使用较小的测试文件
4. 检查文件编码 (推荐UTF-8)

#### 3. 网络连接失败

**问题现象**: 测试连接时显示失败

**可能原因**:
- IP地址或端口设置错误
- 目标设备未启动
- 防火墙阻止连接
- 网络不通

**解决方案**:
1. 确认IP地址和端口正确
2. 检查目标设备是否运行
3. 临时关闭防火墙测试
4. 使用ping命令测试网络连通性
```bash
ping 127.0.0.1
telnet 127.0.0.1 102
```

#### 4. 自动对点测试异常

**问题现象**: 测试过程中出现错误或中断

**可能原因**:
- 配置文件未正确解析
- 通信连接不稳定
- 测试参数设置错误

**解决方案**:
1. 重新解析配置文件
2. 确认通信连接稳定
3. 检查测试参数设置
4. 尝试手动测试单个信号点

#### 5. 界面显示异常

**问题现象**: 界面布局错乱或组件无响应

**可能原因**:
- 系统分辨率不兼容
- 显示缩放设置问题
- 图形驱动问题

**解决方案**:
1. 调整系统显示缩放为100%
2. 更新图形驱动程序
3. 重启程序
4. 尝试在不同分辨率下运行

### 错误代码说明

| 错误代码 | 说明 | 解决方案 |
|----------|------|----------|
| E001 | 文件读取失败 | 检查文件路径和权限 |
| E002 | 网络连接超时 | 检查网络设置和目标设备 |
| E003 | 协议解析错误 | 确认协议类型和数据格式 |
| E004 | 内存不足 | 关闭其他程序或增加内存 |
| E005 | 配置文件错误 | 检查配置文件格式和内容 |

---

## 📞 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **用户手册**: 查看本说明书最新版本
- **在线帮助**: 程序内置帮助系统

### 版本信息
- **当前版本**: v2.0.0 Web风格版
- **发布日期**: 2025年7月
- **兼容性**: Python 3.8+, Windows/Linux/macOS

### 更新日志
- **v2.0.0**: 全新Web风格界面，增强用户体验
- **v1.5.0**: 增加自动对点功能
- **v1.0.0**: 基础版本发布

### 技术文档
- **开发文档**: 详细的API和架构说明
- **配置指南**: 高级配置和定制说明
- **最佳实践**: 使用经验和技巧分享

---

## 📄 附录

### A. 快捷键列表
| 快捷键 | 功能 |
|--------|------|
| Ctrl+O | 打开文件 |
| Ctrl+S | 保存配置 |
| F5 | 刷新数据 |
| Ctrl+T | 开始测试 |
| Esc | 停止当前操作 |

### B. 配置文件格式

#### SCD文件格式
```xml
<?xml version="1.0" encoding="UTF-8"?>
<SCL xmlns="http://www.iec.ch/61850/2003/SCL">
  <Header id="SubstationConfig" version="1.0"/>
  <Substation name="TestSubstation">
    <VoltageLevel name="110kV">
      <Bay name="Line1">
        <ConductingEquipment name="CB1" type="CBR"/>
      </Bay>
    </VoltageLevel>
  </Substation>
  <IED name="IED_001">
    <Services>
      <DynAssociation/>
      <GetDirectory/>
      <GetDataObjectDefinition/>
    </Services>
  </IED>
</SCL>
```

#### CSV文件格式
```csv
信号名称,信号类型,数据类型,期望值,单位,描述
IED_001_XCBR_Pos_stVal,遥信,BOOLEAN,1,,断路器位置
IED_001_MMXU_TotW_mag_f,遥测,FLOAT32,1000.5,MW,有功功率
IED_002_PTRC_Str_general,遥信,BOOLEAN,0,,保护启动
```

### C. 协议支持

#### IEC 61850协议
- **标准端口**: 102
- **传输协议**: TCP/IP
- **数据模型**: 符合IEC 61850-7-4标准
- **服务支持**: GetDataValues, SetDataValues, Report

#### DL/T 634.5104协议
- **标准端口**: 2404
- **传输协议**: TCP/IP
- **数据格式**: 符合国标DL/T 634.5104
- **功能支持**: 遥信、遥测、遥控、遥调

### D. 测试用例

#### 标准测试流程
1. **环境准备**
   - 启动子站模拟器
   - 加载配置文件
   - 建立通信连接

2. **功能测试**
   - 文件解析测试
   - 通信连接测试
   - 数据读取测试
   - 信号对点测试

3. **验收标准**
   - 文件解析成功率 ≥ 95%
   - 通信连接稳定性 ≥ 99%
   - 信号对点准确率 ≥ 98%
   - 响应时间 ≤ 3秒

#### 测试数据示例
```
测试项目: 遥信信号对点
测试信号: IED_001_XCBR_Pos_stVal
期望值: 1 (合闸)
实际值: 1 (合闸)
测试结果: 通过
测试时间: 2025-07-04 11:30:15
```

### E. 性能指标

#### 系统性能
- **文件解析速度**: 500点/秒
- **并发连接数**: 最大10个设备
- **数据更新频率**: 2秒/次
- **内存占用**: 典型200MB
- **CPU占用**: 典型5-10%

#### 可靠性指标
- **连续运行时间**: ≥ 24小时
- **故障恢复时间**: ≤ 30秒
- **数据准确率**: ≥ 99.9%
- **系统可用性**: ≥ 99.5%

### F. 安全说明

#### 网络安全
- 建议在内网环境使用
- 配置防火墙规则限制访问
- 定期更新系统补丁
- 使用强密码保护配置文件

#### 数据安全
- 重要配置文件建议备份
- 测试数据定期清理
- 敏感信息加密存储
- 操作日志完整记录

---

**© 2025 Auto_Point Team. 保留所有权利。**

*本说明书最后更新时间: 2025年7月4日*

**声明**: 本软件仅供变电站技术人员使用，使用前请确保具备相关专业知识和操作资质。
