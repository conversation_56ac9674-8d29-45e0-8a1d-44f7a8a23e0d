#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Auto_Point Web风格对点机完整集成测试
验证SCD转换功能和整体系统集成
"""

import os
import time
import socket
from datetime import datetime

def test_system_status():
    """测试系统状态"""
    print("🔍 系统状态检查")
    print("=" * 50)
    
    # 检查关键文件
    key_files = [
        ("main_web_functional.py", "Web界面主程序"),
        ("scd_to_point_converter.py", "SCD转换器"),
        ("substation_optimized.py", "子站模拟器"),
        ("large_substation_500points.scd", "测试SCD文件"),
        ("Auto_Point使用说明书.md", "使用说明书")
    ]
    
    missing_files = []
    for file, desc in key_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            print(f"✅ {desc}: {file} ({size:,} bytes)")
        else:
            print(f"❌ {desc}: {file} (缺失)")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️ 缺失文件: {len(missing_files)} 个")
        return False
    else:
        print(f"\n✅ 所有关键文件完整")
        return True

def test_scd_converter():
    """测试SCD转换器"""
    print("\n🔄 SCD转换器测试")
    print("=" * 50)
    
    try:
        from scd_to_point_converter import SCDToPointConverter
        print("✅ 转换器模块导入成功")
        
        converter = SCDToPointConverter()
        print("✅ 转换器实例创建成功")
        
        # 测试SCD文件解析
        scd_file = "large_substation_500points.scd"
        if os.path.exists(scd_file):
            print(f"🔄 解析SCD文件: {scd_file}")
            
            scd_result = converter.parse_scd_file(scd_file)
            print(f"✅ SCD解析成功: {scd_result['total_ieds']} 个IED")
            
            # 测试转换为点表
            print(f"🔄 转换为点表...")
            csv_file = converter.convert_to_point_table('csv')
            print(f"✅ 点表生成成功: {csv_file}")
            
            # 获取统计信息
            stats = converter.get_conversion_statistics()
            print(f"📊 转换统计:")
            print(f"   总信号点: {stats.get('total_points', 0)}")
            print(f"   遥信: {stats.get('signal_types', {}).get('DI', 0)}")
            print(f"   遥测: {stats.get('signal_types', {}).get('AI', 0)}")
            
            return True
        else:
            print(f"❌ SCD测试文件不存在: {scd_file}")
            return False
            
    except ImportError:
        print("❌ 转换器模块导入失败")
        return False
    except Exception as e:
        print(f"❌ 转换器测试失败: {e}")
        return False

def test_network_connectivity():
    """测试网络连接"""
    print("\n🌐 网络连接测试")
    print("=" * 50)
    
    # 测试端口
    test_ports = [
        (102, "IEC 61850端口"),
        (80, "HTTP端口"),
        (8080, "备用HTTP端口")
    ]
    
    active_ports = 0
    for port, desc in test_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            
            if result == 0:
                print(f"✅ 端口 {port} ({desc}): 活跃")
                active_ports += 1
            else:
                print(f"❌ 端口 {port} ({desc}): 未响应")
        except Exception as e:
            print(f"⚠️ 端口 {port} ({desc}): 检查异常")
    
    print(f"\n📊 网络状态: {active_ports}/{len(test_ports)} 个端口活跃")
    return active_ports > 0

def test_point_table_files():
    """测试点表文件"""
    print("\n📋 点表文件检查")
    print("=" * 50)
    
    # 查找点表文件
    point_table_files = [f for f in os.listdir('.') if f.startswith('point_table_') and f.endswith('.csv')]
    
    if point_table_files:
        print(f"✅ 发现 {len(point_table_files)} 个点表文件:")
        for file in point_table_files:
            size = os.path.getsize(file)
            mtime = datetime.fromtimestamp(os.path.getmtime(file))
            print(f"   📄 {file} ({size:,} bytes, {mtime.strftime('%H:%M:%S')})")
        
        # 验证最新的点表文件
        latest_file = max(point_table_files, key=lambda f: os.path.getmtime(f))
        print(f"\n🔍 验证最新点表: {latest_file}")
        
        try:
            import pandas as pd
            df = pd.read_csv(latest_file, encoding='utf-8-sig')
            print(f"✅ 点表格式正确: {len(df)} 行数据")
            
            # 统计信号类型
            if '信号类型' in df.columns:
                signal_counts = df['信号类型'].value_counts()
                print(f"📊 信号类型分布:")
                for signal_type, count in signal_counts.items():
                    type_name = {'DI': '遥信', 'AI': '遥测', 'DO': '遥控', 'AO': '遥调'}.get(signal_type, signal_type)
                    print(f"   {type_name}: {count}个")
            
            return True
        except Exception as e:
            print(f"❌ 点表验证失败: {e}")
            return False
    else:
        print("❌ 未发现点表文件")
        return False

def test_web_interface_integration():
    """测试Web界面集成"""
    print("\n🌐 Web界面集成测试")
    print("=" * 50)
    
    try:
        # 检查主程序文件
        main_file = 'main_web_functional.py'
        if not os.path.exists(main_file):
            print(f"❌ 主程序文件不存在: {main_file}")
            return False
        
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键功能集成
        integration_checks = [
            ('SCDToPointConverter', 'SCD转换器导入'),
            ('convert_to_point_table', '转换功能'),
            ('deploy_point_table', '部署功能'),
            ('转换为点表', '转换按钮'),
            ('部署点表', '部署按钮')
        ]
        
        all_integrated = True
        for check, desc in integration_checks:
            if check in content:
                print(f"✅ {desc}: 已集成")
            else:
                print(f"❌ {desc}: 未集成")
                all_integrated = False
        
        if all_integrated:
            print(f"\n✅ Web界面集成完整")
            return True
        else:
            print(f"\n❌ Web界面集成不完整")
            return False
            
    except Exception as e:
        print(f"❌ Web界面集成检查失败: {e}")
        return False

def test_documentation():
    """测试文档完整性"""
    print("\n📚 文档完整性测试")
    print("=" * 50)
    
    doc_files = [
        ("Auto_Point使用说明书.md", "完整使用说明书"),
        ("快速入门指南.md", "快速入门指南"),
        ("SCD转换功能演示.md", "转换功能演示"),
        ("操作演示脚本.md", "操作演示脚本"),
        ("文档索引.md", "文档索引")
    ]
    
    available_docs = 0
    total_size = 0
    
    for file, desc in doc_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            total_size += size
            available_docs += 1
            print(f"✅ {desc}: {file} ({size:,} bytes)")
        else:
            print(f"❌ {desc}: {file} (缺失)")
    
    print(f"\n📊 文档统计: {available_docs}/{len(doc_files)} 个文档可用")
    print(f"📊 总文档大小: {total_size:,} bytes")
    
    return available_docs >= len(doc_files) * 0.8  # 80%以上文档可用

def run_comprehensive_test():
    """运行综合测试"""
    print("🧪 Auto_Point Web风格对点机完整集成测试")
    print("=" * 70)
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 测试目标: 验证SCD转换功能和系统集成")
    print("=" * 70)
    
    test_results = []
    
    # 执行各项测试
    tests = [
        ("系统状态检查", test_system_status),
        ("SCD转换器功能", test_scd_converter),
        ("网络连接测试", test_network_connectivity),
        ("点表文件验证", test_point_table_files),
        ("Web界面集成", test_web_interface_integration),
        ("文档完整性", test_documentation)
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            test_results.append((test_name, False))
    
    # 生成测试报告
    print("\n" + "=" * 70)
    print("📊 完整集成测试报告")
    print("=" * 70)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    success_rate = (passed / total * 100) if total > 0 else 0
    
    print(f"\n📈 测试统计:")
    print(f"   ✅ 通过: {passed} 项")
    print(f"   ❌ 失败: {total - passed} 项")
    print(f"   📊 成功率: {success_rate:.1f}%")
    
    # 评估结果
    if success_rate >= 90:
        print(f"\n🏆 测试结论: 优秀")
        print(f"   系统完全就绪，所有功能正常")
    elif success_rate >= 70:
        print(f"\n🥈 测试结论: 良好")
        print(f"   主要功能正常，部分功能需要优化")
    else:
        print(f"\n🥉 测试结论: 需要改进")
        print(f"   存在重要问题，需要修复")
    
    # 功能验证总结
    print(f"\n🎯 SCD转换功能验证:")
    scd_tests = [name for name, result in test_results if 'SCD' in name or '转换' in name]
    if any('SCD' in name for name, result in test_results if result):
        print(f"   ✅ SCD到点表转换功能完全可用")
        print(f"   ✅ 解决了'对点机用SCD，子站用点表'的兼容性问题")
        print(f"   ✅ 实现了完整的一站式对点解决方案")
    else:
        print(f"   ⚠️ SCD转换功能需要进一步验证")
    
    print(f"\n💡 使用建议:")
    print(f"   1. 启动Web界面: python main_web_functional.py")
    print(f"   2. 选择SCD文件并解析")
    print(f"   3. 点击'转换为点表'生成点表")
    print(f"   4. 在通信配置中'部署点表'到子站")
    print(f"   5. 执行自动对点验证")
    
    return success_rate >= 70

if __name__ == "__main__":
    success = run_comprehensive_test()
    
    if success:
        print(f"\n🎉 集成测试成功！系统可以投入使用！")
    else:
        print(f"\n⚠️ 集成测试发现问题，建议检查后再使用")
    
    print(f"\n📞 如需技术支持，请查看使用说明书或联系开发团队")
