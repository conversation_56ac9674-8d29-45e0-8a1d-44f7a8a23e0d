#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试通信数据监控功能
验证数据监控窗口的各项功能
"""

import sys
import time
import random
from datetime import datetime
from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QVBoxLayout, QWidget
from PySide6.QtCore import QTimer

def test_data_monitor():
    """测试数据监控功能"""
    print("🎯 测试通信数据监控功能")
    print("=" * 60)
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    app = QApplication(sys.argv)
    
    # 导入数据监控窗口
    try:
        from 通信数据监控窗口 import DataMonitorWindow
        
        # 创建监控窗口
        monitor = DataMonitorWindow()
        monitor.show()
        
        print("✅ 数据监控窗口创建成功")
        
        # 创建测试控制窗口
        test_window = create_test_control_window(monitor)
        test_window.show()
        
        print("✅ 测试控制窗口创建成功")
        
        # 运行应用
        sys.exit(app.exec())
        
    except ImportError as e:
        print(f"❌ 导入数据监控窗口失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def create_test_control_window(monitor):
    """创建测试控制窗口"""
    
    class TestControlWindow(QMainWindow):
        def __init__(self, monitor_window):
            super().__init__()
            self.monitor = monitor_window
            self.setWindowTitle("数据监控测试控制")
            self.setGeometry(100, 100, 400, 300)
            
            # 创建界面
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            layout = QVBoxLayout(central_widget)
            
            # 测试按钮
            self.test_iec61850_btn = QPushButton("测试IEC 61850数据")
            self.test_iec61850_btn.clicked.connect(self.test_iec61850_data)
            
            self.test_modbus_btn = QPushButton("测试Modbus数据")
            self.test_modbus_btn.clicked.connect(self.test_modbus_data)
            
            self.test_ascii_btn = QPushButton("测试ASCII数据")
            self.test_ascii_btn.clicked.connect(self.test_ascii_data)
            
            self.test_binary_btn = QPushButton("测试二进制数据")
            self.test_binary_btn.clicked.connect(self.test_binary_data)
            
            self.start_auto_btn = QPushButton("开始自动测试")
            self.start_auto_btn.clicked.connect(self.start_auto_test)
            
            self.stop_auto_btn = QPushButton("停止自动测试")
            self.stop_auto_btn.clicked.connect(self.stop_auto_test)
            
            # 添加按钮到布局
            layout.addWidget(self.test_iec61850_btn)
            layout.addWidget(self.test_modbus_btn)
            layout.addWidget(self.test_ascii_btn)
            layout.addWidget(self.test_binary_btn)
            layout.addWidget(self.start_auto_btn)
            layout.addWidget(self.stop_auto_btn)
            
            # 自动测试定时器
            self.auto_timer = QTimer()
            self.auto_timer.timeout.connect(self.auto_test_cycle)
            
        def test_iec61850_data(self):
            """测试IEC 61850数据"""
            print("🔍 测试IEC 61850数据传输")
            
            # 模拟IEC 61850查询命令
            sent_data = bytes([
                0x68,  # 起始字符
                0x04,  # 长度
                0x07,  # 控制字段
                0x00,  # 地址
                0x64,  # 功能码：读取
                0x01,  # 信息对象地址
                random.randint(0x10, 0xFF),  # 随机数据
                0x16   # 结束字符
            ])
            
            # 模拟响应数据
            received_data = bytes([
                0x68,  # 起始字符
                0x06,  # 长度
                0x08,  # 控制字段
                0x00,  # 地址
                0x64,  # 功能码：读取响应
                0x01,  # 信息对象地址
                random.randint(0x00, 0x01),  # 状态值
                random.randint(0x80, 0xFF),  # 品质描述
                random.randint(0x00, 0xFF),  # 时间戳
                0x16   # 结束字符
            ])
            
            self.monitor.add_sent_data(sent_data)
            time.sleep(0.1)
            self.monitor.add_received_data(received_data)
            
        def test_modbus_data(self):
            """测试Modbus数据"""
            print("🔍 测试Modbus数据传输")
            
            # 模拟Modbus读取保持寄存器命令
            sent_data = bytes([
                0x01,  # 设备地址
                0x03,  # 功能码：读取保持寄存器
                0x00, 0x00,  # 起始地址
                0x00, 0x02,  # 寄存器数量
                random.randint(0x00, 0xFF), random.randint(0x00, 0xFF)  # CRC
            ])
            
            # 模拟响应数据
            received_data = bytes([
                0x01,  # 设备地址
                0x03,  # 功能码
                0x04,  # 字节数
                random.randint(0x00, 0xFF), random.randint(0x00, 0xFF),  # 数据1
                random.randint(0x00, 0xFF), random.randint(0x00, 0xFF),  # 数据2
                random.randint(0x00, 0xFF), random.randint(0x00, 0xFF)   # CRC
            ])
            
            self.monitor.add_sent_data(sent_data)
            time.sleep(0.1)
            self.monitor.add_received_data(received_data)
            
        def test_ascii_data(self):
            """测试ASCII数据"""
            print("🔍 测试ASCII数据传输")
            
            # 模拟ASCII命令
            sent_data = "GET /status HTTP/1.1\r\nHost: localhost\r\n\r\n".encode('utf-8')
            
            # 模拟ASCII响应
            received_data = "HTTP/1.1 200 OK\r\nContent-Type: text/plain\r\n\r\nOK".encode('utf-8')
            
            self.monitor.add_sent_data(sent_data)
            time.sleep(0.1)
            self.monitor.add_received_data(received_data)
            
        def test_binary_data(self):
            """测试二进制数据"""
            print("🔍 测试二进制数据传输")
            
            # 模拟随机二进制数据
            sent_data = bytes([random.randint(0, 255) for _ in range(16)])
            received_data = bytes([random.randint(0, 255) for _ in range(12)])
            
            self.monitor.add_sent_data(sent_data)
            time.sleep(0.1)
            self.monitor.add_received_data(received_data)
            
        def start_auto_test(self):
            """开始自动测试"""
            print("🚀 开始自动测试")
            self.auto_timer.start(2000)  # 每2秒测试一次
            self.start_auto_btn.setEnabled(False)
            self.stop_auto_btn.setEnabled(True)
            
        def stop_auto_test(self):
            """停止自动测试"""
            print("⏹️ 停止自动测试")
            self.auto_timer.stop()
            self.start_auto_btn.setEnabled(True)
            self.stop_auto_btn.setEnabled(False)
            
        def auto_test_cycle(self):
            """自动测试循环"""
            test_functions = [
                self.test_iec61850_data,
                self.test_modbus_data,
                self.test_ascii_data,
                self.test_binary_data
            ]
            
            # 随机选择一个测试函数
            test_func = random.choice(test_functions)
            test_func()
    
    return TestControlWindow(monitor)

def main():
    """主函数"""
    print("🎯 通信数据监控功能测试")
    print("=" * 60)
    
    # 功能说明
    print("📋 测试功能:")
    print("   1. 数据监控窗口显示")
    print("   2. 多种数据格式测试")
    print("   3. 实时数据传输模拟")
    print("   4. 格式切换验证")
    print("   5. 统计信息更新")
    
    print("\n💡 操作说明:")
    print("   1. 数据监控窗口会自动打开")
    print("   2. 测试控制窗口提供各种测试按钮")
    print("   3. 点击'开始监控'开始数据监控")
    print("   4. 使用测试按钮模拟不同类型的数据")
    print("   5. 切换显示格式观察效果")
    
    print("\n🎮 开始测试...")
    
    # 运行测试
    test_data_monitor()

if __name__ == "__main__":
    main()
