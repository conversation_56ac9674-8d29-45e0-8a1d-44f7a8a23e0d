#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试宽松解析功能
验证对点机的新增宽松解析模式
"""

import os
import sys
import re
from datetime import datetime

def test_compatibility_check():
    """测试兼容性检查功能"""
    print("🎯 测试宽松解析兼容性检查")
    print("=" * 60)
    
    # 模拟对点机的兼容性检查逻辑
    scd_file = "220kVQFB_recovered.scd"
    table_file = "point_table_regex_20250705_142137.csv"
    
    if not os.path.exists(scd_file):
        print(f"❌ SCD文件不存在: {scd_file}")
        return False
    
    if not os.path.exists(table_file):
        print(f"❌ 点表文件不存在: {table_file}")
        return False
    
    print(f"📁 SCD文件: {scd_file}")
    print(f"📁 点表文件: {table_file}")
    
    # 检查SCD文件
    result = check_scd_compatibility(scd_file)
    
    # 检查点表文件
    table_result = check_table_compatibility(table_file)
    
    # 综合评估
    if result['parseable'] and table_result['readable']:
        print("\n✅ 兼容性检查通过！")
        print(f"📊 SCD解析方式: {result['parse_method']}")
        print(f"📊 SCD数据点: {result['data_object_count']}")
        print(f"📊 点表数据点: {table_result['total_points']}")
        
        if result['parse_method'] == 'regex':
            print("⚠️ 使用宽松解析模式，XML格式有问题但数据可提取")
        
        return True
    else:
        print("\n❌ 兼容性检查失败")
        return False

def check_scd_compatibility(scd_file):
    """检查SCD文件兼容性"""
    print(f"\n🔍 检查SCD文件: {scd_file}")
    
    result = {
        'parseable': False,
        'parse_method': 'none',
        'ied_count': 0,
        'data_object_count': 0,
        'warnings': []
    }
    
    try:
        # 尝试标准XML解析
        import xml.etree.ElementTree as ET
        
        try:
            tree = ET.parse(scd_file)
            root = tree.getroot()
            
            # 统计基本信息
            ieds = root.findall('.//IED')
            dois = root.findall('.//DOI')
            
            result.update({
                'parseable': True,
                'parse_method': 'standard',
                'ied_count': len(ieds),
                'data_object_count': len(dois)
            })
            
            print(f"✅ 标准XML解析成功: {len(ieds)}个IED, {len(dois)}个数据对象")
            return result
            
        except ET.ParseError as e:
            print(f"⚠️ 标准XML解析失败: {e}")
            
            # 尝试宽松解析
            print("🔧 尝试宽松解析模式...")
            regex_result = parse_scd_with_regex(scd_file)
            
            if regex_result['success']:
                result.update({
                    'parseable': True,
                    'parse_method': 'regex',
                    'ied_count': regex_result['ied_count'],
                    'data_object_count': regex_result['data_object_count'],
                    'warnings': ['XML格式有问题', '使用宽松解析模式', '数据内容可以提取']
                })
                
                print(f"✅ 宽松解析成功: {regex_result['ied_count']}个IED, {regex_result['data_object_count']}个数据对象")
                return result
            else:
                print("❌ 宽松解析也失败")
                return result
    
    except Exception as e:
        print(f"❌ SCD文件检查异常: {e}")
        return result

def parse_scd_with_regex(scd_file):
    """使用正则表达式解析SCD文件"""
    try:
        print(f"🔧 正则表达式解析: {os.path.basename(scd_file)}")
        
        # 读取文件内容
        with open(scd_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        # 提取IED信息
        ied_pattern = r'<IED[^>]*name="([^"]*)"[^>]*>'
        ieds = re.findall(ied_pattern, content)
        
        # 提取数据对象实例
        doi_pattern = r'<DOI[^>]*name="([^"]*)"[^>]*>'
        dois = re.findall(doi_pattern, content)
        
        result = {
            'success': True,
            'ied_count': len(ieds),
            'data_object_count': len(dois),
            'content_length': len(content)
        }
        
        print(f"📊 正则解析结果: {len(ieds)}个IED, {len(dois)}个数据对象")
        
        return result
        
    except Exception as e:
        print(f"❌ 正则表达式解析失败: {e}")
        return {'success': False, 'error': str(e)}

def check_table_compatibility(table_file):
    """检查点表文件兼容性"""
    print(f"\n🔍 检查点表文件: {table_file}")
    
    result = {
        'readable': False,
        'total_points': 0,
        'signal_types': {}
    }
    
    try:
        import pandas as pd
        
        # 读取点表文件
        df = pd.read_csv(table_file, encoding='utf-8-sig')
        
        result.update({
            'readable': True,
            'total_points': len(df),
            'columns': list(df.columns)
        })
        
        # 统计信号类型
        if '信号类型' in df.columns:
            signal_types = df['信号类型'].value_counts().to_dict()
            result['signal_types'] = signal_types
        
        print(f"✅ 点表文件读取成功: {len(df)}个数据点")
        
        return result
        
    except Exception as e:
        print(f"❌ 点表文件读取失败: {e}")
        return result

def simulate_dual_load():
    """模拟同时加载过程"""
    print("\n🎮 模拟同时加载过程")
    print("-" * 40)
    
    # 执行兼容性检查
    compatible = test_compatibility_check()
    
    if compatible:
        print("\n🎉 模拟加载成功！")
        print("💡 在实际对点机中应该可以成功加载")
        print("📋 建议操作:")
        print("   1. 访问 http://localhost:8080")
        print("   2. 进入配置文件管理")
        print("   3. 使用同时加载功能")
        print("   4. 选择这两个文件")
        print("   5. 忽略XML格式警告，选择继续")
        return True
    else:
        print("\n❌ 模拟加载失败")
        print("💡 需要进一步调试")
        return False

def main():
    """主函数"""
    print("🎯 宽松解析功能测试")
    print("=" * 60)
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查必要文件
    required_files = [
        "220kVQFB_recovered.scd",
        "point_table_regex_20250705_142137.csv"
    ]
    
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        print("💡 请确保以下文件存在:")
        for f in required_files:
            print(f"   - {f}")
        return
    
    # 执行测试
    success = simulate_dual_load()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成：宽松解析功能应该可以工作！")
        print("💡 现在可以在对点机中测试实际的同时加载功能")
    else:
        print("❌ 测试失败：需要进一步调试")
    
    print(f"🕐 测试结束: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
