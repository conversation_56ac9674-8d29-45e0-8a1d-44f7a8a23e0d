#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Web风格界面与子站模拟程序联合演示
展示完整的对点机功能和界面交互
"""

import sys
import os
import time
import subprocess
import threading
from datetime import datetime

class AutoPointDemo:
    """Auto_Point完整功能演示"""
    
    def __init__(self):
        self.substation_process = None
        self.web_interface_process = None
        
    def print_banner(self):
        """打印演示横幅"""
        print("=" * 80)
        print("🌐 Auto_Point Web风格界面 + 子站模拟程序 联合演示")
        print("=" * 80)
        print("📋 演示内容:")
        print("   1. 🚀 启动子站模拟程序")
        print("   2. 🌐 启动Web风格界面")
        print("   3. 📊 展示界面功能特性")
        print("   4. 🔗 验证通信连接")
        print("   5. 📁 测试文件操作")
        print("   6. 📈 展示数据可视化")
        print("=" * 80)
        
    def start_substation_simulator(self):
        """启动子站模拟程序"""
        print("\n🚀 正在启动子站模拟程序...")
        print("   📍 程序: substation_optimized.py")
        print("   🌐 监听: 0.0.0.0:102")
        print("   📡 协议: IEC 61850")
        
        try:
            self.substation_process = subprocess.Popen(
                [sys.executable, "substation_optimized.py"],
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待启动
            time.sleep(3)
            
            if self.substation_process.poll() is None:
                print("   ✅ 子站模拟程序启动成功")
                print("   📊 模拟数据点已加载")
                print("   🔌 等待客户端连接...")
                return True
            else:
                print("   ❌ 子站模拟程序启动失败")
                return False
                
        except Exception as e:
            print(f"   ❌ 启动异常: {e}")
            return False
    
    def start_web_interface(self):
        """启动Web风格界面"""
        print("\n🌐 正在启动Web风格界面...")
        print("   📍 程序: main_web_style.py")
        print("   🎨 风格: 现代化Web设计")
        print("   📱 框架: PySide6 + Material Design")
        
        try:
            self.web_interface_process = subprocess.Popen(
                [sys.executable, "main_web_style.py"],
                cwd=os.getcwd(),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待界面加载
            time.sleep(5)
            
            if self.web_interface_process.poll() is None:
                print("   ✅ Web风格界面启动成功")
                print("   🎯 界面已就绪，可以开始操作")
                return True
            else:
                print("   ❌ Web风格界面启动失败")
                return False
                
        except Exception as e:
            print(f"   ❌ 启动异常: {e}")
            return False
    
    def demonstrate_features(self):
        """演示功能特性"""
        print("\n📊 Web界面功能特性演示:")
        print("=" * 60)
        
        features = [
            {
                "category": "🎨 界面设计",
                "items": [
                    "深色顶部功能栏 - 专业电力行业风格",
                    "左侧导航树 - 6大功能模块清晰分类",
                    "主内容区 - 响应式布局，内容丰富",
                    "状态指示器 - 4种颜色实时状态显示"
                ]
            },
            {
                "category": "📁 配置文件管理",
                "items": [
                    "拖拽式文件上传 - 支持SCD/RCD/CSV格式",
                    "文件解析状态 - 实时显示解析进度",
                    "文件列表管理 - 表格式文件信息展示",
                    "点表转换功能 - 智能格式转换"
                ]
            },
            {
                "category": "🔧 通信配置",
                "items": [
                    "网关参数设置 - IP、端口、协议配置",
                    "连接状态监控 - 4种状态实时显示",
                    "连接测试功能 - 一键测试网络连通性",
                    "协议选择支持 - IEC 61850、DL/T 634.5104"
                ]
            },
            {
                "category": "📊 数据监控",
                "items": [
                    "统计卡片展示 - 总数据点、在线设备等",
                    "实时趋势图表 - 24小时电压趋势",
                    "数据表格显示 - 遥信遥测实时数据",
                    "自动数据刷新 - 2秒间隔更新"
                ]
            },
            {
                "category": "📋 报告管理",
                "items": [
                    "验收报告生成 - 自动生成专业报告",
                    "历史记录管理 - 完整的报告历史",
                    "多格式导出 - PDF/Excel格式支持",
                    "报告模板管理 - 灵活的模板系统"
                ]
            }
        ]
        
        for feature in features:
            print(f"\n{feature['category']}:")
            for item in feature['items']:
                print(f"   ✅ {item}")
                time.sleep(0.5)  # 演示效果
    
    def demonstrate_interaction(self):
        """演示交互操作"""
        print("\n🎮 交互操作演示:")
        print("=" * 60)
        
        print("\n📋 用户操作指南:")
        print("   1. 🖱️ 点击左侧导航 - 切换功能模块")
        print("   2. 📁 拖拽文件上传 - 测试文件解析功能")
        print("   3. 🔧 配置通信参数 - 设置IP和端口")
        print("   4. 🔗 测试连接 - 验证与子站通信")
        print("   5. 📊 查看实时数据 - 观察数据更新")
        print("   6. 📈 查看趋势图表 - 数据可视化展示")
        print("   7. 📋 生成验收报告 - 完整功能验证")
        
        print("\n🎯 推荐测试流程:")
        print("   Step 1: 配置文件管理 → 上传SCD文件")
        print("   Step 2: 通信配置 → 设置127.0.0.1:102")
        print("   Step 3: 数据监控 → 查看实时数据")
        print("   Step 4: 报告管理 → 生成验收报告")
    
    def show_test_files(self):
        """显示可用测试文件"""
        print("\n📁 可用测试文件:")
        print("=" * 60)
        
        test_files = [
            ("large_substation_500points.scd", "500个数据点的大型SCD文件"),
            ("large_points_500.csv", "500个数据点的CSV文件"),
            ("test_substation.scd", "标准测试SCD文件"),
            ("demo_scd_points.csv", "演示用CSV数据点文件")
        ]
        
        for filename, description in test_files:
            if os.path.exists(filename):
                size = os.path.getsize(filename)
                print(f"   ✅ {filename}")
                print(f"      📄 {description}")
                print(f"      📊 文件大小: {size:,} bytes")
                print()
            else:
                print(f"   ⚠️ {filename} (文件不存在)")
    
    def monitor_processes(self):
        """监控进程状态"""
        print("\n🔍 进程状态监控:")
        print("=" * 60)
        
        while True:
            try:
                # 检查子站模拟程序
                if self.substation_process:
                    if self.substation_process.poll() is None:
                        print("   🟢 子站模拟程序: 运行中")
                    else:
                        print("   🔴 子站模拟程序: 已停止")
                
                # 检查Web界面
                if self.web_interface_process:
                    if self.web_interface_process.poll() is None:
                        print("   🟢 Web风格界面: 运行中")
                    else:
                        print("   🔴 Web风格界面: 已停止")
                
                print(f"   🕐 监控时间: {datetime.now().strftime('%H:%M:%S')}")
                print("   💡 提示: 按 Ctrl+C 停止演示")
                print("-" * 40)
                
                time.sleep(10)  # 每10秒检查一次
                
            except KeyboardInterrupt:
                print("\n⚠️ 用户中断监控")
                break
            except Exception as e:
                print(f"\n❌ 监控异常: {e}")
                break
    
    def cleanup(self):
        """清理进程"""
        print("\n🧹 正在清理进程...")
        
        if self.web_interface_process:
            try:
                self.web_interface_process.terminate()
                self.web_interface_process.wait(timeout=5)
                print("   ✅ Web界面进程已关闭")
            except:
                try:
                    self.web_interface_process.kill()
                    print("   ⚠️ 强制关闭Web界面进程")
                except:
                    print("   ❌ 无法关闭Web界面进程")
        
        if self.substation_process:
            try:
                self.substation_process.terminate()
                self.substation_process.wait(timeout=5)
                print("   ✅ 子站模拟程序已关闭")
            except:
                try:
                    self.substation_process.kill()
                    print("   ⚠️ 强制关闭子站模拟程序")
                except:
                    print("   ❌ 无法关闭子站模拟程序")
    
    def run_demo(self):
        """运行完整演示"""
        try:
            self.print_banner()
            
            # 启动程序
            if not self.start_substation_simulator():
                print("❌ 子站模拟程序启动失败，演示终止")
                return
            
            if not self.start_web_interface():
                print("❌ Web界面启动失败，演示终止")
                return
            
            # 演示功能
            self.demonstrate_features()
            self.demonstrate_interaction()
            self.show_test_files()
            
            print("\n🎉 程序启动完成！")
            print("=" * 80)
            print("🌐 Web风格界面已启动，请在界面中进行以下操作:")
            print("   1. 📁 测试文件上传功能")
            print("   2. 🔧 配置通信参数")
            print("   3. 📊 查看实时数据监控")
            print("   4. 📈 观察数据可视化效果")
            print("   5. 📋 生成验收报告")
            print("=" * 80)
            
            # 开始监控
            self.monitor_processes()
            
        except KeyboardInterrupt:
            print("\n⚠️ 演示被用户中断")
        except Exception as e:
            print(f"\n❌ 演示过程异常: {e}")
        finally:
            self.cleanup()
            print("\n🎯 演示结束")

def main():
    """主函数"""
    print("🌐 Auto_Point Web风格界面联合演示")
    print("展示现代化对点机界面与子站模拟程序的完整功能")
    print()
    
    demo = AutoPointDemo()
    demo.run_demo()

if __name__ == "__main__":
    main()
