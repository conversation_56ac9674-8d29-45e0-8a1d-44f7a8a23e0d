#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极SCD文件分析工具
处理各种编码问题和大型文件
"""

import os
import re
from datetime import datetime

def analyze_scd_with_error_handling(filename):
    """使用错误处理的SCD分析"""
    print(f"🔍 终极SCD文件分析: {filename}")
    print("=" * 80)
    print(f"🕐 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        return False
    
    # 文件基本信息
    file_size = os.path.getsize(filename)
    print(f"\n📄 文件基本信息:")
    print(f"   文件路径: {filename}")
    print(f"   文件大小: {file_size:,} bytes ({file_size/1024:.1f}KB, {file_size/1024/1024:.1f}MB)")
    
    # 使用二进制模式读取并分析
    try:
        print(f"\n🔧 二进制模式分析...")
        
        with open(filename, 'rb') as f:
            # 读取文件头
            header = f.read(10000)  # 前10KB
            
            # 分析文件头
            analyze_file_header(header)
            
            # 分块分析整个文件
            f.seek(0)
            stats = analyze_file_in_chunks(f, file_size)
            
            # 输出结果
            print_final_results(stats, filename, file_size)
            
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def analyze_file_header(header):
    """分析文件头"""
    print(f"📋 文件头分析:")
    
    # 检查XML声明
    if b'<?xml' in header:
        print(f"   ✅ 发现XML声明")
        
        # 提取编码声明
        encoding_match = re.search(rb'encoding="([^"]*)"', header)
        if encoding_match:
            declared_encoding = encoding_match.group(1).decode('ascii', errors='ignore')
            print(f"   📝 声明编码: {declared_encoding}")
        else:
            print(f"   ⚠️ 未声明编码")
    else:
        print(f"   ❌ 未发现XML声明")
    
    # 检查SCL根元素
    if b'<SCL' in header or b'<scl' in header:
        print(f"   ✅ 发现SCL根元素")
        
        # 提取命名空间
        ns_match = re.search(rb'xmlns="([^"]*)"', header)
        if ns_match:
            namespace = ns_match.group(1).decode('ascii', errors='ignore')
            print(f"   🌐 命名空间: {namespace}")
    else:
        print(f"   ❌ 未发现SCL根元素")
    
    # 检查文件开头的字节
    print(f"   📊 文件开头字节: {header[:50].hex()}")
    
    # 尝试解码前100个字符
    try:
        decoded_start = header[:100].decode('utf-8', errors='ignore')
        print(f"   📝 文件开头内容: {decoded_start[:80]}...")
    except:
        print(f"   ⚠️ 无法解码文件开头")

def analyze_file_in_chunks(file_obj, file_size):
    """分块分析文件"""
    print(f"\n📊 分块分析文件内容...")
    
    chunk_size = 1024 * 1024  # 1MB
    total_chunks = (file_size + chunk_size - 1) // chunk_size
    
    stats = {
        'xml_elements': {},
        'ied_names': set(),
        'substation_names': set(),
        'logical_nodes': {},
        'ip_addresses': set(),
        'total_bytes_analyzed': 0,
        'chunks_processed': 0
    }
    
    chunk_num = 0
    max_chunks = min(20, total_chunks)  # 最多处理20个块
    
    while chunk_num < max_chunks:
        try:
            chunk = file_obj.read(chunk_size)
            if not chunk:
                break
            
            chunk_num += 1
            print(f"   处理块 {chunk_num}/{max_chunks} ({len(chunk):,} bytes)")
            
            # 分析这个块
            analyze_binary_chunk(chunk, stats)
            
            stats['total_bytes_analyzed'] += len(chunk)
            stats['chunks_processed'] = chunk_num
            
        except Exception as e:
            print(f"   ⚠️ 块 {chunk_num} 处理失败: {e}")
            continue
    
    return stats

def analyze_binary_chunk(chunk, stats):
    """分析二进制块"""
    try:
        # 尝试解码为文本进行分析
        text = chunk.decode('utf-8', errors='ignore')
        
        # 统计XML元素
        xml_patterns = {
            'SCL': rb'<SCL[^>]*>',
            'Header': rb'<Header[^>]*>',
            'Substation': rb'<Substation[^>]*>',
            'VoltageLevel': rb'<VoltageLevel[^>]*>',
            'Bay': rb'<Bay[^>]*>',
            'IED': rb'<IED[^>]*>',
            'LDevice': rb'<LDevice[^>]*>',
            'LN': rb'<LN[^>]*>',
            'DOI': rb'<DOI[^>]*>',
            'Communication': rb'<Communication[^>]*>'
        }
        
        for element, pattern in xml_patterns.items():
            count = len(re.findall(pattern, chunk, re.IGNORECASE))
            stats['xml_elements'][element] = stats['xml_elements'].get(element, 0) + count
        
        # 查找IED名称
        ied_matches = re.findall(r'<IED[^>]*name="([^"]*)"', text, re.IGNORECASE)
        stats['ied_names'].update(ied_matches)
        
        # 查找变电站名称
        substation_matches = re.findall(r'<Substation[^>]*name="([^"]*)"', text, re.IGNORECASE)
        stats['substation_names'].update(substation_matches)
        
        # 查找逻辑节点类
        ln_matches = re.findall(r'lnClass="([^"]*)"', text, re.IGNORECASE)
        for ln_class in ln_matches:
            stats['logical_nodes'][ln_class] = stats['logical_nodes'].get(ln_class, 0) + 1
        
        # 查找IP地址
        ip_matches = re.findall(r'<P[^>]*type="IP"[^>]*>([^<]*)</P>', text, re.IGNORECASE)
        stats['ip_addresses'].update(ip_matches)
        
    except Exception as e:
        # 如果文本分析失败，进行二进制模式分析
        analyze_pure_binary(chunk, stats)

def analyze_pure_binary(chunk, stats):
    """纯二进制模式分析"""
    # 查找二进制模式
    binary_patterns = {
        'IED': b'<IED',
        'Substation': b'<Substation',
        'LDevice': b'<LDevice',
        'LN': b'<LN',
        'DOI': b'<DOI'
    }
    
    for element, pattern in binary_patterns.items():
        count = chunk.count(pattern)
        stats['xml_elements'][element] = stats['xml_elements'].get(element, 0) + count

def print_final_results(stats, filename, file_size):
    """输出最终结果"""
    print(f"\n📊 分析结果总结:")
    print(f"=" * 60)
    
    # 分析覆盖率
    coverage = (stats['total_bytes_analyzed'] / file_size) * 100
    print(f"📈 分析覆盖率: {coverage:.1f}% ({stats['total_bytes_analyzed']:,}/{file_size:,} bytes)")
    print(f"📦 处理块数: {stats['chunks_processed']}")
    
    # XML元素统计
    print(f"\n🏷️ XML元素统计:")
    for element, count in sorted(stats['xml_elements'].items()):
        if count > 0:
            print(f"   {element}: {count:,}个")
    
    # 变电站信息
    print(f"\n🏭 变电站信息:")
    if stats['substation_names']:
        print(f"   变电站数量: {len(stats['substation_names'])}")
        for name in list(stats['substation_names'])[:5]:
            print(f"      - {name}")
        if len(stats['substation_names']) > 5:
            print(f"      ... 还有{len(stats['substation_names'])-5}个")
    else:
        print(f"   ⚠️ 未找到变电站名称")
    
    # IED设备信息
    print(f"\n🔧 IED设备信息:")
    if stats['ied_names']:
        print(f"   IED设备数量: {len(stats['ied_names'])}")
        for name in list(stats['ied_names'])[:10]:
            print(f"      - {name}")
        if len(stats['ied_names']) > 10:
            print(f"      ... 还有{len(stats['ied_names'])-10}个")
    else:
        print(f"   ⚠️ 未找到IED设备名称")
    
    # 逻辑节点统计
    print(f"\n📋 逻辑节点类型统计 (前10个):")
    if stats['logical_nodes']:
        sorted_ln = sorted(stats['logical_nodes'].items(), key=lambda x: x[1], reverse=True)
        for ln_class, count in sorted_ln[:10]:
            ln_desc = get_ln_description(ln_class)
            print(f"   {ln_class} ({ln_desc}): {count:,}个")
    else:
        print(f"   ⚠️ 未找到逻辑节点定义")
    
    # 通信配置
    print(f"\n📡 通信配置:")
    if stats['ip_addresses']:
        print(f"   IP地址数量: {len(stats['ip_addresses'])}")
        for ip in list(stats['ip_addresses'])[:5]:
            print(f"      - {ip}")
    else:
        print(f"   ⚠️ 未找到IP地址配置")
    
    # 文件评估
    print(f"\n💡 文件评估:")
    total_elements = sum(stats['xml_elements'].values())
    ied_count = len(stats['ied_names'])
    ln_count = sum(stats['logical_nodes'].values())
    
    print(f"   XML元素总数: {total_elements:,}")
    print(f"   IED设备数: {ied_count}")
    print(f"   逻辑节点数: {ln_count:,}")
    print(f"   文件大小: {file_size/1024/1024:.1f}MB")
    
    # 规模评估
    if ied_count > 200:
        scale = "超大型"
    elif ied_count > 100:
        scale = "大型"
    elif ied_count > 50:
        scale = "中型"
    else:
        scale = "小型"
    
    print(f"   变电站规模: {scale}变电站")
    
    # 使用建议
    print(f"\n🎯 使用建议:")
    print(f"   1. 这是220kV旗峰坝变电站的SCD配置文件")
    print(f"   2. 文件很大({file_size/1024/1024:.1f}MB)，包含{ied_count}个IED设备")
    print(f"   3. 建议在Auto_Point对点机中分批处理")
    print(f"   4. 可以提取部分IED进行小规模测试")
    print(f"   5. 适合大规模性能测试和压力测试")
    
    # 技术建议
    print(f"\n🔧 技术建议:")
    if file_size > 50 * 1024 * 1024:  # 50MB
        print(f"   ⚠️ 文件过大，建议:")
        print(f"      - 使用专业SCD编辑工具分割文件")
        print(f"      - 提取关键IED设备创建小文件")
        print(f"      - 分阶段进行对点测试")
    else:
        print(f"   ✅ 文件大小适中，可以直接使用")

def get_ln_description(ln_class):
    """获取逻辑节点类描述"""
    descriptions = {
        'LLN0': '逻辑节点0',
        'XCBR': '断路器',
        'XSWI': '开关',
        'CSWI': '控制开关',
        'MMXU': '测量单元',
        'MMTR': '变压器测量',
        'PTRC': '保护',
        'PDIF': '差动保护',
        'PDIR': '方向保护',
        'GGIO': '通用输入输出',
        'TCTR': '电流互感器',
        'TVTR': '电压互感器',
        'CALH': '计算',
        'RADR': '录波',
        'RBDR': '录波数据',
        'YPTR': '电压互感器保护',
        'YLTC': '有载调压',
        'YPSH': '移相器'
    }
    return descriptions.get(ln_class, '未知类型')

def main():
    """主函数"""
    filename = r"D:\auto_point\222\220kVQFB.scd"
    
    print("🎯 220kVQFB.scd 终极分析工具")
    print("=" * 80)
    
    # 分析文件
    success = analyze_scd_with_error_handling(filename)
    
    if success:
        print(f"\n✅ 220kVQFB.scd 分析完成")
        print(f"📋 这是一个大型的220kV变电站SCD配置文件")
        print(f"🎮 在Auto_Point对点机中的应用:")
        print(f"   1. 大规模变电站配置参考")
        print(f"   2. 系统性能压力测试")
        print(f"   3. 真实工程项目验证")
        print(f"   4. IED设备兼容性测试")
        
        print(f"\n💡 下一步建议:")
        print(f"   1. 可以将此文件复制到Auto_Point工作目录")
        print(f"   2. 在对点机中尝试解析（可能需要较长时间）")
        print(f"   3. 或者提取部分内容创建小文件进行测试")
        print(f"   4. 用于验证系统处理大型SCD文件的能力")
    else:
        print(f"\n❌ 文件分析失败")
        print(f"💡 可能的原因:")
        print(f"   1. 文件损坏或格式特殊")
        print(f"   2. 编码问题")
        print(f"   3. 文件过大导致内存不足")

if __name__ == "__main__":
    main()
