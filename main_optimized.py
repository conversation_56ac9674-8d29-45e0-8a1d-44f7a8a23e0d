#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版对点机GUI - 简化界面，保留核心功能
"""

import sys
import socket
import json
import pandas as pd
from datetime import datetime
import time
import threading
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QLabel, QPushButton, QFileDialog, QLineEdit,
    QVBoxLayout, QWidget, QHBoxLayout, QTextEdit, QTableWidget, QTableWidgetItem, 
    QMessageBox, QComboBox, QProgressBar, QGroupBox, QFormLayout
)
from PySide6.QtCore import QThread, Signal, QTimer
from PySide6.QtGui import QColor, QFont
from logic import AutoChecker

class CheckThread(QThread):
    """对点检查线程"""
    progress = Signal(int, str, str, str, str)  # 进度, 点名, 期望值, 实际值, 结果
    finished = Signal(list)  # 完成信号，返回所有结果
    
    def __init__(self, checker, interval=0.2):
        super().__init__()
        self.checker = checker
        self.interval = interval
        self.results = []
        
    def run(self):
        """执行对点检查"""
        try:
            # 直接使用AutoChecker的run_check方法
            check_results = self.checker.run_check()

            self.results = []
            total_points = len(check_results)

            for i, point_info in enumerate(check_results):
                try:
                    point_name = point_info.get('SignalName', '')
                    expected_value = point_info.get('ExpectedValue', '')
                    actual_value = point_info.get('actual_value', 'N/A')
                    result = point_info.get('check_result', '未知')
                    signal_type = point_info.get('SignalType', '遥测')

                    # 保存结果
                    result_data = {
                        'SignalName': point_name,
                        'SignalType': signal_type,
                        'ExpectedValue': expected_value,
                        'ActualValue': actual_value,
                        'CheckResult': result,
                        'IEDName': point_info.get('IEDName', 'Unknown'),
                        'Timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    }
                    self.results.append(result_data)

                    # 发送进度信号
                    self.progress.emit(i + 1, point_name, str(expected_value), str(actual_value), result)

                    # 延时
                    time.sleep(self.interval)

                except Exception as e:
                    print(f"处理检查结果 {i} 时出错: {e}")

            self.finished.emit(self.results)

        except Exception as e:
            print(f"对点检查线程异常: {e}")

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("自动对点机 - 优化版")
        self.setGeometry(100, 100, 1200, 800)
        
        # 变量初始化
        self.point_path = ''
        self.ip = ''
        self.port = 102
        self.checker = None
        self.check_thread = None
        self.results = []
        self.is_connected = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # 配置区域
        config_group = QGroupBox("配置参数")
        config_layout = QFormLayout()
        
        # 点表文件
        point_layout = QHBoxLayout()
        self.point_btn = QPushButton("选择点表文件")
        self.point_btn.clicked.connect(self.select_point_table)
        self.point_label = QLabel("未选择")
        point_layout.addWidget(self.point_btn)
        point_layout.addWidget(self.point_label)
        config_layout.addRow("点表文件:", point_layout)
        
        # 网络配置
        network_layout = QHBoxLayout()
        self.ip_edit = QLineEdit("127.0.0.1")
        self.port_edit = QLineEdit("102")
        self.port_edit.setMaximumWidth(80)
        network_layout.addWidget(QLabel("IP:"))
        network_layout.addWidget(self.ip_edit)
        network_layout.addWidget(QLabel("端口:"))
        network_layout.addWidget(self.port_edit)
        network_layout.addStretch()
        config_layout.addRow("网络:", network_layout)
        
        # 协议选择
        self.protocol_combo = QComboBox()
        self.protocol_combo.addItems(["IEC 61850", "DL/T 634.5104"])
        self.protocol_combo.setCurrentIndex(0)  # 默认IEC 61850
        config_layout.addRow("通信协议:", self.protocol_combo)

        # 对点速度
        self.speed_combo = QComboBox()
        self.speed_combo.addItems(["快速 (0.1s)", "正常 (0.2s)", "慢速 (0.5s)"])
        self.speed_combo.setCurrentIndex(1)
        config_layout.addRow("对点速度:", self.speed_combo)

        # 测试模式选择
        self.test_mode_combo = QComboBox()
        self.test_mode_combo.addItems(["自动测试模式", "手动测试模式"])
        self.test_mode_combo.setCurrentIndex(0)  # 默认自动模式
        self.test_mode_combo.currentTextChanged.connect(self.on_test_mode_changed)
        config_layout.addRow("测试模式:", self.test_mode_combo)

        config_group.setLayout(config_layout)
        main_layout.addWidget(config_group)
        
        # 操作区域
        operation_group = QGroupBox("操作控制")
        operation_layout = QVBoxLayout()

        # 第一行：基本操作
        basic_ops_layout = QHBoxLayout()

        self.connect_btn = QPushButton("连接测试")
        self.connect_btn.clicked.connect(self.test_connection)
        basic_ops_layout.addWidget(self.connect_btn)

        # 连接状态
        self.status_label = QLabel("状态: 未连接")
        self.status_label.setStyleSheet("QLabel { color: red; font-weight: bold; }")
        basic_ops_layout.addWidget(self.status_label)

        basic_ops_layout.addStretch()
        operation_layout.addLayout(basic_ops_layout)

        # 自动测试区域
        self.auto_test_group = QGroupBox("自动测试模式")
        auto_test_layout = QHBoxLayout()

        self.start_btn = QPushButton("开始批量对点")
        self.start_btn.clicked.connect(self.start_checking)
        self.start_btn.setEnabled(False)
        auto_test_layout.addWidget(self.start_btn)

        self.export_btn = QPushButton("导出报告")
        self.export_btn.clicked.connect(self.export_results)
        self.export_btn.setEnabled(False)
        auto_test_layout.addWidget(self.export_btn)

        auto_test_layout.addStretch()

        # 添加说明标签
        auto_info_label = QLabel("批量测试所有数据点")
        auto_info_label.setStyleSheet("QLabel { color: gray; font-style: italic; }")
        auto_test_layout.addWidget(auto_info_label)

        self.auto_test_group.setLayout(auto_test_layout)
        operation_layout.addWidget(self.auto_test_group)

        # 手动测试区域
        self.manual_test_group = QGroupBox("手动测试模式")
        manual_layout = QVBoxLayout()

        # 信号选择行
        signal_layout = QHBoxLayout()
        signal_layout.addWidget(QLabel("选择信号:"))
        self.signal_combo = QComboBox()
        self.signal_combo.setMinimumWidth(300)
        self.signal_combo.setEnabled(False)
        signal_layout.addWidget(self.signal_combo)
        signal_layout.addStretch()
        manual_layout.addLayout(signal_layout)

        # 操作按钮行
        manual_ops_layout = QHBoxLayout()

        self.manual_test_btn = QPushButton("测试此点")
        self.manual_test_btn.clicked.connect(self.manual_test_point)
        self.manual_test_btn.setEnabled(False)
        manual_ops_layout.addWidget(self.manual_test_btn)

        self.clear_manual_btn = QPushButton("清空结果")
        self.clear_manual_btn.clicked.connect(self.clear_manual_results)
        self.clear_manual_btn.setEnabled(False)
        manual_ops_layout.addWidget(self.clear_manual_btn)

        self.export_manual_btn = QPushButton("导出手动测试")
        self.export_manual_btn.clicked.connect(self.export_manual_results)
        self.export_manual_btn.setEnabled(False)
        manual_ops_layout.addWidget(self.export_manual_btn)

        manual_ops_layout.addStretch()

        # 手动测试结果显示
        self.manual_result_label = QLabel("等待测试...")
        self.manual_result_label.setMinimumWidth(200)
        self.manual_result_label.setStyleSheet("QLabel { padding: 5px; border: 1px solid gray; }")
        manual_ops_layout.addWidget(self.manual_result_label)

        manual_layout.addLayout(manual_ops_layout)

        # 添加说明
        manual_info_label = QLabel("选择单个数据点进行精确测试，适用于调试和故障排查")
        manual_info_label.setStyleSheet("QLabel { color: gray; font-style: italic; }")
        manual_layout.addWidget(manual_info_label)

        self.manual_test_group.setLayout(manual_layout)
        operation_layout.addWidget(self.manual_test_group)

        operation_group.setLayout(operation_layout)
        main_layout.addWidget(operation_group)
        
        # 进度区域
        progress_group = QGroupBox("对点进度")
        progress_layout = QVBoxLayout()
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        self.progress_label = QLabel("等待开始...")
        progress_layout.addWidget(self.progress_label)
        
        progress_group.setLayout(progress_layout)
        main_layout.addWidget(progress_group)
        
        # 结果表格
        result_group = QGroupBox("对点结果")
        result_layout = QVBoxLayout()
        
        # 筛选
        filter_layout = QHBoxLayout()
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["全部", "只看错误", "只看正确"])
        self.filter_combo.currentTextChanged.connect(self.apply_filter)
        filter_layout.addWidget(QLabel("筛选:"))
        filter_layout.addWidget(self.filter_combo)
        filter_layout.addStretch()
        result_layout.addLayout(filter_layout)
        
        # 表格
        self.result_table = QTableWidget()
        self.result_table.setColumnCount(6)
        self.result_table.setHorizontalHeaderLabels([
            "信号名称", "信号类型", "期望值", "实际值", "结果", "时间戳"
        ])
        # 设置列宽
        self.result_table.setColumnWidth(0, 200)  # 信号名称
        self.result_table.setColumnWidth(1, 80)   # 信号类型
        self.result_table.setColumnWidth(2, 100)  # 期望值
        self.result_table.setColumnWidth(3, 100)  # 实际值
        self.result_table.setColumnWidth(4, 80)   # 结果
        self.result_table.setColumnWidth(5, 120)  # 时间戳
        result_layout.addWidget(self.result_table)
        
        result_group.setLayout(result_layout)
        main_layout.addWidget(result_group)
        
        # 日志区域
        log_group = QGroupBox("运行日志")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setFont(QFont("Consolas", 9))
        log_layout.addWidget(self.log_text)
        
        log_group.setLayout(log_layout)
        main_layout.addWidget(log_group)

        # 初始化测试模式显示
        self.on_test_mode_changed("自动测试模式")

    def select_point_table(self):
        """选择点表文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择点表文件", "", "点表文件 (*.csv *.xls *.xlsx)"
        )
        if file_path:
            self.point_path = file_path
            self.point_label.setText(file_path.split('/')[-1])
            self.log(f"已选择点表文件: {file_path}")

            # 更新手动测试的信号列表
            self.update_signal_list()
            
    def test_connection(self):
        """测试连接"""
        self.ip = self.ip_edit.text().strip()
        try:
            self.port = int(self.port_edit.text().strip())
        except ValueError:
            self.port = 102
            
        if not self.ip:
            QMessageBox.warning(self, "参数错误", "请输入IP地址")
            return
            
        self.log(f"测试连接到 {self.ip}:{self.port}")
        
        try:
            test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            test_socket.settimeout(5)
            test_socket.connect((self.ip, self.port))
            test_socket.close()
            
            self.is_connected = True
            self.status_label.setText("状态: 连接成功")
            self.status_label.setStyleSheet("QLabel { color: green; font-weight: bold; }")

            # 根据测试模式启用相应功能
            current_mode = self.test_mode_combo.currentText()

            if current_mode == "自动测试模式":
                # 启用自动测试功能
                self.start_btn.setEnabled(True)
                self.export_btn.setEnabled(True)
                self.log("✓ 连接成功 - 自动测试模式已就绪")

            elif current_mode == "手动测试模式":
                # 启用手动测试功能
                if self.signal_combo.count() > 1:  # 有信号可选择
                    self.manual_test_btn.setEnabled(True)
                    self.clear_manual_btn.setEnabled(True)
                    self.export_manual_btn.setEnabled(True)
                    self.manual_result_label.setText("可以测试")
                    self.manual_result_label.setStyleSheet("QLabel { color: green; padding: 5px; border: 1px solid green; }")
                else:
                    self.manual_result_label.setText("请先选择点表")
                    self.manual_result_label.setStyleSheet("QLabel { color: orange; padding: 5px; border: 1px solid orange; }")
                self.log("✓ 连接成功 - 手动测试模式已就绪")

            self.log("✓ 连接测试成功")
            
        except Exception as e:
            self.is_connected = False
            self.status_label.setText("状态: 连接失败")
            self.status_label.setStyleSheet("QLabel { color: red; font-weight: bold; }")

            # 禁用所有测试功能
            self.start_btn.setEnabled(False)
            self.export_btn.setEnabled(False)
            self.manual_test_btn.setEnabled(False)
            self.clear_manual_btn.setEnabled(False)
            self.export_manual_btn.setEnabled(False)
            self.manual_result_label.setText("连接失败")
            self.manual_result_label.setStyleSheet("QLabel { color: red; padding: 5px; border: 1px solid red; }")

            self.log(f"✗ 连接测试失败: {e}")
            QMessageBox.warning(self, "连接失败", f"无法连接到 {self.ip}:{self.port}\n错误: {e}")
            
    def start_checking(self):
        """开始对点检查"""
        if not self.point_path:
            QMessageBox.warning(self, "参数缺失", "请先选择点表文件")
            return
            
        if not self.is_connected:
            QMessageBox.warning(self, "连接错误", "请先测试连接")
            return
            
        try:
            # 设置网络参数
            protocol = self.protocol_combo.currentText()
            network_params = {
                'ip': self.ip,
                'port': self.port,
                'protocol': protocol
            }

            # 初始化检查器 (优化版不需要SCD文件)
            self.checker = AutoChecker(scd_path=None, point_table_path=self.point_path, network_params=network_params)

            # 获取对点速度
            speed_text = self.speed_combo.currentText()
            if "0.1s" in speed_text:
                interval = 0.1
            elif "0.5s" in speed_text:
                interval = 0.5
            else:
                interval = 0.2

            # 启动检查线程
            self.check_thread = CheckThread(self.checker, interval)
            self.check_thread.progress.connect(self.update_progress)
            self.check_thread.finished.connect(self.check_finished)

            # 初始化界面
            self.progress_bar.setVisible(True)
            self.progress_bar.setMaximum(len(self.checker.mapped_points))
            self.progress_bar.setValue(0)
            self.progress_label.setText("开始对点检查...")
            self.start_btn.setEnabled(False)
            self.result_table.setRowCount(0)
            self.results = []

            self.check_thread.start()
            self.log("开始对点检查...")

        except Exception as e:
            self.log(f"启动对点检查失败: {e}")
            QMessageBox.critical(self, "错误", f"启动对点检查失败: {e}")
            
    def update_progress(self, current, point_name, expected, actual, result):
        """更新进度"""
        self.progress_bar.setValue(current)
        self.progress_label.setText(f"进度: {current}/{self.progress_bar.maximum()} - {point_name}")
        
        # 添加到结果表格
        row = self.result_table.rowCount()
        self.result_table.insertRow(row)
        
        items = [point_name, "", expected, actual, result, datetime.now().strftime("%H:%M:%S")]
        for col, item_text in enumerate(items):
            item = QTableWidgetItem(str(item_text))
            if col == 4:  # 结果列
                if "正确" in result:
                    item.setBackground(QColor(200, 255, 200))
                elif "错误" in result:
                    item.setBackground(QColor(255, 200, 200))
                else:
                    item.setBackground(QColor(255, 255, 180))
            self.result_table.setItem(row, col, item)
            
        # 滚动到最新行
        self.result_table.scrollToBottom()
        
    def check_finished(self, results):
        """对点检查完成"""
        self.results = results
        self.progress_label.setText(f"对点完成! 共检查 {len(results)} 个数据点")
        self.start_btn.setEnabled(True)
        self.export_btn.setEnabled(True)
        
        # 统计结果
        correct_count = sum(1 for r in results if r['CheckResult'] == '正确')
        error_count = sum(1 for r in results if r['CheckResult'] == '错误')
        
        self.log(f"对点检查完成!")
        self.log(f"总计: {len(results)} 个, 正确: {correct_count} 个, 错误: {error_count} 个")
        self.log(f"正确率: {correct_count/len(results)*100:.1f}%")
        
    def apply_filter(self, filter_text):
        """应用筛选"""
        if not self.results:
            return
            
        self.result_table.setRowCount(0)
        
        for result in self.results:
            if filter_text == "只看错误" and result['CheckResult'] != '错误':
                continue
            elif filter_text == "只看正确" and result['CheckResult'] != '正确':
                continue
                
            row = self.result_table.rowCount()
            self.result_table.insertRow(row)
            
            items = [
                result['SignalName'],
                result['SignalType'],
                result['ExpectedValue'],
                result['ActualValue'],
                result['CheckResult'],
                result['Timestamp']
            ]
            
            for col, item_text in enumerate(items):
                item = QTableWidgetItem(str(item_text))
                if col == 4:  # 结果列
                    if "正确" in result['CheckResult']:
                        item.setBackground(QColor(200, 255, 200))
                    elif "错误" in result['CheckResult']:
                        item.setBackground(QColor(255, 200, 200))
                    else:
                        item.setBackground(QColor(255, 255, 180))
                self.result_table.setItem(row, col, item)
                
    def export_results(self):
        """导出结果"""
        if not self.results:
            QMessageBox.warning(self, "无数据", "没有结果可导出")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存报告", f"对点报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
            "Excel文件 (*.xlsx);;CSV文件 (*.csv)"
        )
        
        if file_path:
            try:
                df = pd.DataFrame(self.results)
                if file_path.endswith('.xlsx'):
                    df.to_excel(file_path, index=False)
                else:
                    df.to_csv(file_path, index=False, encoding='utf-8-sig')
                    
                self.log(f"✓ 报告已导出: {file_path}")
                QMessageBox.information(self, "导出成功", f"报告已保存到:\n{file_path}")
                
            except Exception as e:
                self.log(f"✗ 导出失败: {e}")
                QMessageBox.critical(self, "导出失败", f"导出失败: {e}")

    def on_test_mode_changed(self, mode_text):
        """测试模式切换处理"""
        try:
            if mode_text == "自动测试模式":
                # 显示自动测试区域，隐藏手动测试区域
                self.auto_test_group.setVisible(True)
                self.manual_test_group.setVisible(False)
                self.log("切换到自动测试模式 - 批量测试所有数据点")

            elif mode_text == "手动测试模式":
                # 显示手动测试区域，隐藏自动测试区域
                self.auto_test_group.setVisible(False)
                self.manual_test_group.setVisible(True)
                self.log("切换到手动测试模式 - 单点精确测试")

                # 如果已有点表，更新信号列表
                if self.point_path:
                    self.update_signal_list()

        except Exception as e:
            self.log(f"测试模式切换异常: {e}")

    def clear_manual_results(self):
        """清空手动测试结果"""
        try:
            # 清空结果表格中的手动测试结果
            rows_to_remove = []
            for row in range(self.result_table.rowCount()):
                timestamp_item = self.result_table.item(row, 5)
                if timestamp_item and "(手动)" in timestamp_item.text():
                    rows_to_remove.append(row)

            # 从后往前删除，避免索引变化
            for row in reversed(rows_to_remove):
                self.result_table.removeRow(row)

            # 重置手动测试结果显示
            self.manual_result_label.setText("等待测试...")
            self.manual_result_label.setStyleSheet("QLabel { padding: 5px; border: 1px solid gray; }")

            self.log(f"已清空 {len(rows_to_remove)} 个手动测试结果")

        except Exception as e:
            self.log(f"清空手动测试结果失败: {e}")

    def export_manual_results(self):
        """导出手动测试结果"""
        try:
            # 收集手动测试结果
            manual_results = []
            for row in range(self.result_table.rowCount()):
                timestamp_item = self.result_table.item(row, 5)
                if timestamp_item and "(手动)" in timestamp_item.text():
                    result_row = {}
                    headers = ["信号名称", "信号类型", "期望值", "实际值", "结果", "时间戳"]
                    for col, header in enumerate(headers):
                        item = self.result_table.item(row, col)
                        result_row[header] = item.text() if item else ""
                    manual_results.append(result_row)

            if not manual_results:
                QMessageBox.information(self, "导出提示", "没有手动测试结果可导出")
                return

            # 选择保存路径
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出手动测试结果",
                f"manual_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                "CSV文件 (*.csv);;Excel文件 (*.xlsx)"
            )

            if file_path:
                df = pd.DataFrame(manual_results)

                if file_path.endswith('.xlsx'):
                    df.to_excel(file_path, index=False)
                else:
                    df.to_csv(file_path, index=False, encoding='utf-8-sig')

                self.log(f"✓ 手动测试结果已导出: {file_path}")
                QMessageBox.information(self, "导出成功", f"手动测试结果已保存到:\n{file_path}")

        except Exception as e:
            self.log(f"✗ 导出手动测试结果失败: {e}")
            QMessageBox.critical(self, "导出失败", f"导出失败: {e}")

    def update_signal_list(self):
        """更新手动测试的信号列表"""
        try:
            if not self.point_path:
                return

            # 解析点表文件
            from config_parser import parse_point_table
            points = parse_point_table(self.point_path)

            # 清空并更新信号列表
            self.signal_combo.clear()
            self.signal_combo.addItem("请选择信号...")

            for point in points:
                signal_name = point.get('SignalName', '')
                signal_type = point.get('DataType', '')
                if signal_name:
                    display_text = f"{signal_name} ({signal_type})"
                    self.signal_combo.addItem(display_text, point)

            # 启用信号选择
            self.signal_combo.setEnabled(True)

            # 如果在手动模式且已连接，启用相关按钮
            if (self.test_mode_combo.currentText() == "手动测试模式" and
                self.is_connected and len(points) > 0):
                self.clear_manual_btn.setEnabled(True)
                self.export_manual_btn.setEnabled(True)
                if self.signal_combo.count() > 1:  # 有信号可选择
                    self.manual_test_btn.setEnabled(True)

            self.log(f"已加载 {len(points)} 个信号到手动测试列表")

        except Exception as e:
            self.log(f"更新信号列表失败: {e}")
            self.signal_combo.setEnabled(False)

    def manual_test_point(self):
        """手动测试单个数据点"""
        try:
            # 检查连接状态
            if not self.is_connected:
                self.manual_result_label.setText("❌ 未连接")
                self.manual_result_label.setStyleSheet("QLabel { color: red; }")
                QMessageBox.warning(self, "连接错误", "请先测试连接")
                return

            # 获取选中的信号
            current_index = self.signal_combo.currentIndex()
            if current_index <= 0:
                self.manual_result_label.setText("❌ 未选择信号")
                self.manual_result_label.setStyleSheet("QLabel { color: red; }")
                QMessageBox.warning(self, "选择错误", "请选择要测试的信号")
                return

            selected_point = self.signal_combo.currentData()
            if not selected_point:
                return

            # 更新状态
            self.manual_result_label.setText("🔄 测试中...")
            self.manual_result_label.setStyleSheet("QLabel { color: blue; }")
            self.manual_test_btn.setEnabled(False)

            # 执行单点测试
            signal_name = selected_point.get('SignalName', '')
            expected_value = selected_point.get('ExpectedValue', '')

            self.log(f"开始手动测试信号: {signal_name}")

            # 使用现有的checker进行测试
            if not self.checker:
                self.initialize_checker()

            # 执行单点检查
            result = self.checker.check_single_point(selected_point)

            # 显示结果
            if result:
                actual_value = result.get('actual_value', 'N/A')
                is_correct = result.get('is_correct', False)

                if is_correct:
                    self.manual_result_label.setText(f"✅ 正确 ({actual_value})")
                    self.manual_result_label.setStyleSheet("QLabel { color: green; font-weight: bold; }")
                    self.log(f"✓ 手动测试成功: {signal_name} = {actual_value}")
                else:
                    self.manual_result_label.setText(f"❌ 错误 ({actual_value})")
                    self.manual_result_label.setStyleSheet("QLabel { color: red; font-weight: bold; }")
                    self.log(f"✗ 手动测试失败: {signal_name}, 期望:{expected_value}, 实际:{actual_value}")

                # 添加到结果表格
                self.add_manual_result_to_table(selected_point, result)

            else:
                self.manual_result_label.setText("❌ 测试失败")
                self.manual_result_label.setStyleSheet("QLabel { color: red; }")
                self.log(f"✗ 手动测试异常: {signal_name}")

        except Exception as e:
            self.manual_result_label.setText("❌ 异常")
            self.manual_result_label.setStyleSheet("QLabel { color: red; }")
            self.log(f"手动测试异常: {e}")

        finally:
            # 恢复按钮状态
            self.manual_test_btn.setEnabled(True)

    def add_manual_result_to_table(self, point, result):
        """将手动测试结果添加到表格"""
        try:
            row_count = self.result_table.rowCount()
            self.result_table.insertRow(row_count)

            signal_name = point.get('SignalName', '')
            signal_type = point.get('DataType', '')
            expected_value = point.get('ExpectedValue', '')
            actual_value = result.get('actual_value', 'N/A')
            is_correct = result.get('is_correct', False)

            # 添加数据到表格
            self.result_table.setItem(row_count, 0, QTableWidgetItem(signal_name))
            self.result_table.setItem(row_count, 1, QTableWidgetItem(signal_type))
            self.result_table.setItem(row_count, 2, QTableWidgetItem(str(expected_value)))
            self.result_table.setItem(row_count, 3, QTableWidgetItem(str(actual_value)))

            # 结果列
            result_item = QTableWidgetItem("✓ 正确" if is_correct else "✗ 错误")
            if is_correct:
                result_item.setBackground(QColor(144, 238, 144))  # 浅绿色
            else:
                result_item.setBackground(QColor(255, 182, 193))  # 浅红色
            self.result_table.setItem(row_count, 4, result_item)

            # 时间戳
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.result_table.setItem(row_count, 5, QTableWidgetItem(f"{timestamp} (手动)"))

            # 滚动到最新行
            self.result_table.scrollToBottom()

        except Exception as e:
            self.log(f"添加手动测试结果到表格失败: {e}")

    def initialize_checker(self):
        """初始化检查器"""
        try:
            from logic import PointChecker
            protocol = self.protocol_combo.currentText()
            self.checker = PointChecker(self.ip, self.port, protocol)
            self.log("检查器初始化成功")
        except Exception as e:
            self.log(f"检查器初始化失败: {e}")
            raise

    def log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")

def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
