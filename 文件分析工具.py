#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件分析工具
分析SCD文件的内容和问题
"""

import os
import sys

def analyze_file_header(file_path, bytes_to_read=1024):
    """分析文件头部"""
    print(f"🔍 分析文件: {file_path}")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print("❌ 文件不存在")
        return
    
    file_size = os.path.getsize(file_path)
    print(f"📏 文件大小: {file_size:,} 字节 ({file_size/1024/1024:.1f}MB)")
    
    try:
        # 读取文件头部的原始字节
        with open(file_path, 'rb') as f:
            header_bytes = f.read(bytes_to_read)
        
        print(f"\n📋 文件头部 ({len(header_bytes)} 字节):")
        print("-" * 40)
        
        # 显示十六进制
        print("🔢 十六进制格式:")
        for i in range(0, min(len(header_bytes), 256), 16):
            hex_part = ' '.join(f'{b:02x}' for b in header_bytes[i:i+16])
            ascii_part = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in header_bytes[i:i+16])
            print(f"{i:04x}: {hex_part:<48} {ascii_part}")
        
        # 尝试不同编码解码
        print(f"\n📝 文本内容尝试:")
        print("-" * 40)
        
        encodings = ['utf-8', 'gbk', 'gb2312', 'iso-8859-1', 'utf-16']
        for encoding in encodings:
            try:
                text = header_bytes.decode(encoding)
                print(f"✅ {encoding}: {text[:200]}...")
                break
            except UnicodeDecodeError as e:
                print(f"❌ {encoding}: 解码失败 - {e}")
        
        # 查找XML声明
        print(f"\n🔍 XML结构分析:")
        print("-" * 40)
        
        try:
            text = header_bytes.decode('utf-8', errors='ignore')
            if '<?xml' in text:
                xml_start = text.find('<?xml')
                xml_end = text.find('?>', xml_start) + 2
                xml_declaration = text[xml_start:xml_end]
                print(f"✅ XML声明: {xml_declaration}")
            else:
                print("❌ 未找到XML声明")
            
            if '<SCL' in text:
                scl_start = text.find('<SCL')
                scl_end = text.find('>', scl_start) + 1
                scl_tag = text[scl_start:scl_end]
                print(f"✅ SCL根元素: {scl_tag}")
            else:
                print("❌ 未找到SCL根元素")
                
        except Exception as e:
            print(f"❌ XML分析失败: {e}")
        
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")

def analyze_file_corruption(file_path):
    """分析文件损坏位置"""
    print(f"\n🚨 损坏位置分析:")
    print("-" * 40)
    
    try:
        # 尝试逐块读取文件，找到损坏位置
        chunk_size = 1024 * 1024  # 1MB块
        with open(file_path, 'rb') as f:
            chunk_num = 0
            while True:
                chunk = f.read(chunk_size)
                if not chunk:
                    break
                
                try:
                    # 尝试解码这个块
                    chunk.decode('utf-8')
                    print(f"✅ 块 {chunk_num} (位置 {chunk_num * chunk_size:,}): 正常")
                except UnicodeDecodeError as e:
                    print(f"❌ 块 {chunk_num} (位置 {chunk_num * chunk_size:,}): 损坏 - {e}")
                    
                    # 显示损坏位置附近的内容
                    error_pos = chunk_num * chunk_size + e.start
                    print(f"   损坏字节位置: {error_pos:,}")
                    
                    # 显示损坏位置附近的十六进制
                    start_pos = max(0, e.start - 32)
                    end_pos = min(len(chunk), e.start + 32)
                    problem_bytes = chunk[start_pos:end_pos]
                    
                    print(f"   损坏位置附近的十六进制:")
                    for i in range(0, len(problem_bytes), 16):
                        hex_part = ' '.join(f'{b:02x}' for b in problem_bytes[i:i+16])
                        print(f"   {start_pos + i:04x}: {hex_part}")
                    
                    break
                
                chunk_num += 1
                if chunk_num > 100:  # 限制检查范围
                    print("⚠️ 检查范围限制，停止分析")
                    break
                    
    except Exception as e:
        print(f"❌ 损坏分析失败: {e}")

def main():
    """主函数"""
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        file_path = input("请输入要分析的文件路径: ").strip()
    
    if not file_path:
        print("❌ 未指定文件路径")
        return
    
    print("🎯 SCD文件分析工具")
    print("=" * 60)
    
    # 分析文件头部
    analyze_file_header(file_path)
    
    # 分析损坏位置
    analyze_file_corruption(file_path)
    
    print("\n" + "=" * 60)
    print("🎉 分析完成")

if __name__ == "__main__":
    main()
