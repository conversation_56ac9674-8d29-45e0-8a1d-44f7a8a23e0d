#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通信数据监控窗口
实时显示通信过程中的数据传输信息
"""

import sys
from datetime import datetime
from PySide6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                               QHBoxLayout, QTextEdit, QLabel, QPushButton, 
                               QComboBox, QCheckBox, QGroupBox, QSplitter,
                               QTableWidget, QTableWidgetItem, QHeaderView)
from PySide6.QtCore import Qt, QTimer, Signal, QThread
from PySide6.QtGui import QFont, QTextCursor

class DataMonitorWindow(QMainWindow):
    """数据监控窗口"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Auto_Point 通信数据监控")
        self.setGeometry(300, 300, 1200, 800)
        self.setup_ui()
        self.setup_monitoring()
        
        # 数据缓存
        self.sent_data = []
        self.received_data = []
        self.max_records = 1000  # 最大记录数
    
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 标题和状态
        header_layout = QHBoxLayout()
        
        title_label = QLabel("通信数据监控")
        title_label.setFont(QFont("Microsoft YaHei", 16, QFont.Bold))
        title_label.setStyleSheet("color: #262626; margin-bottom: 10px;")
        
        self.status_label = QLabel("连接状态: 未连接")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                padding: 8px 12px;
                color: #8c8c8c;
            }
        """)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addWidget(self.status_label)
        
        layout.addLayout(header_layout)
        
        # 控制面板
        control_group = QGroupBox("监控控制")
        control_layout = QHBoxLayout(control_group)
        
        # 显示格式选择
        format_label = QLabel("显示格式:")
        self.format_combo = QComboBox()
        self.format_combo.addItems(["十六进制 (HEX)", "二进制 (BIN)", "ASCII码", "混合显示"])
        self.format_combo.currentTextChanged.connect(self.on_format_changed)
        
        # 自动滚动
        self.auto_scroll_cb = QCheckBox("自动滚动")
        self.auto_scroll_cb.setChecked(True)
        
        # 时间戳
        self.timestamp_cb = QCheckBox("显示时间戳")
        self.timestamp_cb.setChecked(True)
        
        # 控制按钮
        self.start_btn = QPushButton("开始监控")
        self.start_btn.clicked.connect(self.start_monitoring)
        self.start_btn.setStyleSheet("""
            QPushButton {
                background-color: #52c41a;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #73d13d;
            }
        """)
        
        self.stop_btn = QPushButton("停止监控")
        self.stop_btn.clicked.connect(self.stop_monitoring)
        self.stop_btn.setEnabled(False)
        self.stop_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff4d4f;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #ff7875;
            }
            QPushButton:disabled {
                background-color: #d9d9d9;
                color: #8c8c8c;
            }
        """)
        
        self.clear_btn = QPushButton("清空数据")
        self.clear_btn.clicked.connect(self.clear_data)
        self.clear_btn.setStyleSheet("""
            QPushButton {
                background-color: #1890ff;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #40a9ff;
            }
        """)
        
        control_layout.addWidget(format_label)
        control_layout.addWidget(self.format_combo)
        control_layout.addWidget(self.auto_scroll_cb)
        control_layout.addWidget(self.timestamp_cb)
        control_layout.addStretch()
        control_layout.addWidget(self.start_btn)
        control_layout.addWidget(self.stop_btn)
        control_layout.addWidget(self.clear_btn)
        
        layout.addWidget(control_group)
        
        # 主要显示区域
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：发送数据
        sent_group = QGroupBox("发送数据 (TX)")
        sent_layout = QVBoxLayout(sent_group)
        
        self.sent_text = QTextEdit()
        self.sent_text.setFont(QFont("Consolas", 10))
        self.sent_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                background-color: #f6ffed;
            }
        """)
        
        sent_layout.addWidget(self.sent_text)
        
        # 右侧：接收数据
        received_group = QGroupBox("接收数据 (RX)")
        received_layout = QVBoxLayout(received_group)
        
        self.received_text = QTextEdit()
        self.received_text.setFont(QFont("Consolas", 10))
        self.received_text.setStyleSheet("""
            QTextEdit {
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                background-color: #f0f8ff;
            }
        """)
        
        received_layout.addWidget(self.received_text)
        
        splitter.addWidget(sent_group)
        splitter.addWidget(received_group)
        splitter.setSizes([600, 600])
        
        layout.addWidget(splitter)
        
        # 底部：统计信息
        stats_group = QGroupBox("传输统计")
        stats_layout = QHBoxLayout(stats_group)
        
        self.sent_count_label = QLabel("发送: 0 字节")
        self.received_count_label = QLabel("接收: 0 字节")
        self.error_count_label = QLabel("错误: 0 次")
        self.connection_time_label = QLabel("连接时间: --")
        
        stats_layout.addWidget(self.sent_count_label)
        stats_layout.addWidget(self.received_count_label)
        stats_layout.addWidget(self.error_count_label)
        stats_layout.addWidget(self.connection_time_label)
        stats_layout.addStretch()
        
        layout.addWidget(stats_group)
        
        # 初始化统计
        self.sent_bytes = 0
        self.received_bytes = 0
        self.error_count = 0
        self.connection_start_time = None
    
    def setup_monitoring(self):
        """设置监控机制"""
        # 定时器用于更新显示
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_display)
        
        # 连接状态检查定时器
        self.connection_timer = QTimer()
        self.connection_timer.timeout.connect(self.check_connection_status)
        self.connection_timer.start(1000)  # 每秒检查一次
        
        self.monitoring_active = False
    
    def start_monitoring(self):
        """开始监控"""
        self.monitoring_active = True
        self.connection_start_time = datetime.now()
        
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        
        self.update_timer.start(100)  # 每100ms更新一次
        
        self.status_label.setText("连接状态: 监控中")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #f6ffed;
                border: 1px solid #b7eb8f;
                border-radius: 6px;
                padding: 8px 12px;
                color: #52c41a;
            }
        """)
        
        print("✅ 开始通信数据监控")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        
        self.update_timer.stop()
        
        self.status_label.setText("连接状态: 已停止")
        self.status_label.setStyleSheet("""
            QLabel {
                background-color: #fff2e8;
                border: 1px solid #ffbb96;
                border-radius: 6px;
                padding: 8px 12px;
                color: #fa8c16;
            }
        """)
        
        print("⏹️ 停止通信数据监控")
    
    def clear_data(self):
        """清空数据"""
        self.sent_text.clear()
        self.received_text.clear()
        
        self.sent_data.clear()
        self.received_data.clear()
        
        self.sent_bytes = 0
        self.received_bytes = 0
        self.error_count = 0
        
        self.update_statistics()
        
        print("🗑️ 清空监控数据")
    
    def add_sent_data(self, data):
        """添加发送数据"""
        if not self.monitoring_active:
            return
        
        timestamp = datetime.now()
        self.sent_data.append({
            'timestamp': timestamp,
            'data': data,
            'size': len(data)
        })
        
        self.sent_bytes += len(data)
        
        # 限制记录数量
        if len(self.sent_data) > self.max_records:
            self.sent_data.pop(0)
    
    def add_received_data(self, data):
        """添加接收数据"""
        if not self.monitoring_active:
            return
        
        timestamp = datetime.now()
        self.received_data.append({
            'timestamp': timestamp,
            'data': data,
            'size': len(data)
        })
        
        self.received_bytes += len(data)
        
        # 限制记录数量
        if len(self.received_data) > self.max_records:
            self.received_data.pop(0)
    
    def update_display(self):
        """更新显示"""
        if not self.monitoring_active:
            return
        
        # 更新发送数据显示
        self.update_text_display(self.sent_text, self.sent_data, "TX")
        
        # 更新接收数据显示
        self.update_text_display(self.received_text, self.received_data, "RX")
        
        # 更新统计信息
        self.update_statistics()
    
    def update_text_display(self, text_widget, data_list, direction):
        """更新文本显示"""
        if not data_list:
            return
        
        # 获取当前格式
        format_type = self.format_combo.currentText()
        show_timestamp = self.timestamp_cb.isChecked()
        
        # 只显示最新的数据
        recent_data = data_list[-10:]  # 只显示最近10条
        
        display_text = ""
        for record in recent_data:
            if show_timestamp:
                timestamp_str = record['timestamp'].strftime('%H:%M:%S.%f')[:-3]
                display_text += f"[{timestamp_str}] {direction}: "
            else:
                display_text += f"{direction}: "
            
            # 根据格式显示数据
            formatted_data = self.format_data(record['data'], format_type)
            display_text += formatted_data + "\n"
        
        # 更新文本控件
        text_widget.setPlainText(display_text)
        
        # 自动滚动到底部
        if self.auto_scroll_cb.isChecked():
            cursor = text_widget.textCursor()
            cursor.movePosition(QTextCursor.End)
            text_widget.setTextCursor(cursor)
    
    def format_data(self, data, format_type):
        """格式化数据显示"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        
        if format_type == "十六进制 (HEX)":
            return ' '.join(f'{b:02X}' for b in data)
        elif format_type == "二进制 (BIN)":
            return ' '.join(f'{b:08b}' for b in data)
        elif format_type == "ASCII码":
            try:
                return data.decode('utf-8', errors='replace')
            except:
                return str(data)
        elif format_type == "混合显示":
            hex_str = ' '.join(f'{b:02X}' for b in data)
            try:
                ascii_str = data.decode('utf-8', errors='replace')
                return f"HEX: {hex_str} | ASCII: {ascii_str}"
            except:
                return f"HEX: {hex_str} | RAW: {str(data)}"
        else:
            return str(data)
    
    def update_statistics(self):
        """更新统计信息"""
        self.sent_count_label.setText(f"发送: {self.sent_bytes} 字节")
        self.received_count_label.setText(f"接收: {self.received_bytes} 字节")
        self.error_count_label.setText(f"错误: {self.error_count} 次")
        
        if self.connection_start_time:
            elapsed = datetime.now() - self.connection_start_time
            elapsed_str = str(elapsed).split('.')[0]  # 去掉微秒
            self.connection_time_label.setText(f"连接时间: {elapsed_str}")
    
    def check_connection_status(self):
        """检查连接状态"""
        # 这里可以添加实际的连接状态检查逻辑
        pass
    
    def on_format_changed(self):
        """格式改变时重新显示"""
        if self.monitoring_active:
            self.update_display()
    
    def simulate_data_transmission(self):
        """模拟数据传输（用于测试）"""
        import random
        
        # 模拟发送数据
        sent_data = bytes([random.randint(0, 255) for _ in range(8)])
        self.add_sent_data(sent_data)
        
        # 模拟接收数据
        received_data = bytes([random.randint(0, 255) for _ in range(6)])
        self.add_received_data(received_data)

def main():
    """测试主函数"""
    app = QApplication(sys.argv)
    
    window = DataMonitorWindow()
    window.show()
    
    # 模拟数据传输
    timer = QTimer()
    timer.timeout.connect(window.simulate_data_transmission)
    timer.start(2000)  # 每2秒模拟一次数据传输
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
