#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单检查子站模拟器状态
不依赖psutil模块的简化版检查
"""

import socket
import time
import subprocess
import os
from datetime import datetime

def check_port_status():
    """检查端口状态"""
    print("🔍 检查子站模拟器端口状态")
    print("-" * 50)
    
    # 子站模拟器常用端口
    ports_to_check = [102, 2404, 8080, 8888]
    active_ports = []
    
    for port in ports_to_check:
        try:
            # 创建socket连接测试
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"   ✅ 端口 {port} 正在监听")
                active_ports.append(port)
            else:
                print(f"   ❌ 端口 {port} 未监听")
        except Exception as e:
            print(f"   ⚠️ 端口 {port} 检查异常: {e}")
    
    return active_ports

def check_python_processes():
    """检查Python进程"""
    print("\n🔍 检查Python进程状态")
    print("-" * 50)
    
    try:
        if os.name == 'nt':  # Windows
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                python_processes = [line for line in lines if 'python.exe' in line.lower()]
                
                if python_processes:
                    print(f"   ✅ 发现 {len(python_processes)} 个Python进程:")
                    for i, proc in enumerate(python_processes[:3]):  # 只显示前3个
                        print(f"      {i+1}. {proc.strip()}")
                    return True
                else:
                    print("   ❌ 未发现Python进程")
                    return False
            else:
                print("   ⚠️ 无法获取进程列表")
                return False
        else:  # Linux/Mac
            result = subprocess.run(['ps', 'aux'], capture_output=True, text=True, timeout=10)
            if 'python' in result.stdout and 'substation' in result.stdout:
                print("   ✅ 发现子站模拟器相关进程")
                return True
            else:
                print("   ❌ 未发现子站模拟器进程")
                return False
    except subprocess.TimeoutExpired:
        print("   ⚠️ 进程检查超时")
        return False
    except Exception as e:
        print(f"   ⚠️ 进程检查失败: {e}")
        return False

def test_connection_to_substation():
    """测试连接子站模拟器"""
    print("\n🔍 测试连接子站模拟器")
    print("-" * 50)
    
    # 尝试连接默认端口
    test_ports = [102, 2404]
    
    for port in test_ports:
        try:
            print(f"   🔄 尝试连接 localhost:{port}...")
            
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex(('localhost', port))
            
            if result == 0:
                print(f"   ✅ 成功连接到端口 {port}")
                
                # 发送测试数据
                try:
                    test_message = b"PING"
                    sock.send(test_message)
                    print(f"   ✅ 测试数据发送成功")
                    
                    # 尝试接收响应
                    sock.settimeout(1)
                    try:
                        response = sock.recv(1024)
                        print(f"   ✅ 收到响应: {len(response)} 字节")
                    except socket.timeout:
                        print(f"   ℹ️ 未收到响应 (正常，可能是协议不匹配)")
                except Exception as e:
                    print(f"   ⚠️ 数据传输异常: {e}")
                
                sock.close()
                return True
            else:
                print(f"   ❌ 连接端口 {port} 失败 (错误码: {result})")
            
            sock.close()
        except Exception as e:
            print(f"   ❌ 连接端口 {port} 异常: {e}")
    
    return False

def check_substation_files():
    """检查子站模拟器文件"""
    print("\n🔍 检查子站模拟器文件")
    print("-" * 50)
    
    files_to_check = [
        'substation_optimized.py',
        'large_substation_2000points.scd'
    ]
    
    all_exist = True
    for file in files_to_check:
        if os.path.exists(file):
            size = os.path.getsize(file) / 1024  # KB
            print(f"   ✅ {file} ({size:.1f}KB)")
        else:
            print(f"   ❌ {file} - 文件缺失")
            all_exist = False
    
    return all_exist

def get_system_info():
    """获取系统信息"""
    print("\n🔍 系统信息")
    print("-" * 50)
    
    try:
        import platform
        print(f"   🖥️ 操作系统: {platform.system()} {platform.release()}")
        print(f"   🐍 Python版本: {platform.python_version()}")
        print(f"   💻 架构: {platform.machine()}")
        
        # 检查网络接口
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        print(f"   🌐 主机名: {hostname}")
        print(f"   📡 本地IP: {local_ip}")
        
        return True
    except Exception as e:
        print(f"   ⚠️ 系统信息获取失败: {e}")
        return False

def main():
    """主检查函数"""
    print("🎯 子站模拟器简单状态检查")
    print("=" * 80)
    print(f"🕐 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"📁 工作目录: {os.getcwd()}")
    
    results = {}
    
    # 1. 检查文件存在
    results['文件检查'] = check_substation_files()
    
    # 2. 检查端口状态
    active_ports = check_port_status()
    results['端口监听'] = len(active_ports) > 0
    
    # 3. 检查Python进程
    results['进程检查'] = check_python_processes()
    
    # 4. 测试连接
    results['连接测试'] = test_connection_to_substation()
    
    # 5. 系统信息
    results['系统信息'] = get_system_info()
    
    # 生成总结
    print("\n" + "=" * 80)
    print("📊 检查结果总结")
    print("=" * 80)
    
    total_checks = len(results)
    passed_checks = sum(1 for result in results.values() if result)
    
    print(f"📈 检查统计:")
    print(f"   🧪 总检查项: {total_checks}")
    print(f"   ✅ 通过检查: {passed_checks}")
    print(f"   ❌ 失败检查: {total_checks - passed_checks}")
    print(f"   📊 通过率: {passed_checks/total_checks*100:.1f}%")
    
    print(f"\n📋 详细结果:")
    for check_name, result in results.items():
        status = "✅ 正常" if result else "❌ 异常"
        print(f"   {check_name}: {status}")
    
    # 状态分析
    print(f"\n💡 状态分析:")
    
    if results['文件检查']:
        print(f"   ✅ 子站模拟器文件完整")
    else:
        print(f"   ❌ 子站模拟器文件缺失")
    
    if results['进程检查']:
        print(f"   ✅ Python进程正在运行")
    else:
        print(f"   ⚠️ 未检测到Python进程")
    
    if results['端口监听']:
        print(f"   ✅ 发现活动端口: {active_ports}")
    else:
        print(f"   ❌ 未发现监听端口")
    
    if results['连接测试']:
        print(f"   ✅ 子站模拟器连接正常")
    else:
        print(f"   ❌ 子站模拟器连接失败")
    
    # 给出建议
    print(f"\n🎯 使用建议:")
    
    if results['连接测试'] or results['端口监听']:
        print(f"   ✅ 子站模拟器似乎正在运行")
        print(f"   💡 可以在Auto_Point Web界面中测试连接")
        print(f"   🔧 建议在通信配置中使用 localhost:102 或 localhost:2404")
    elif results['进程检查']:
        print(f"   ⚠️ Python进程运行中，但网络服务可能未启动")
        print(f"   💡 检查子站模拟器GUI是否正常显示")
        print(f"   🔧 确认在GUI中点击了'启动服务'按钮")
    else:
        print(f"   ❌ 子站模拟器可能未正常启动")
        print(f"   💡 建议重新运行: python substation_optimized.py")
        print(f"   🔧 检查是否有错误信息或GUI窗口")
    
    print(f"\n📱 下一步操作:")
    print(f"   1. 检查桌面是否有子站模拟器GUI窗口")
    print(f"   2. 在GUI中确认服务已启动")
    print(f"   3. 在Auto_Point Web界面中配置通信参数")
    print(f"   4. 测试对点机与子站的连接")
    
    return passed_checks >= 3  # 至少3项通过认为基本正常

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✅ 子站模拟器状态检查完成 - 基本正常")
    else:
        print(f"\n⚠️ 子站模拟器状态检查完成 - 可能需要调整")
