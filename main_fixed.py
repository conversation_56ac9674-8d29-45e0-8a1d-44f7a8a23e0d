import sys
import time
import socket
import json
import traceback
from datetime import datetime
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QLabel, QPushButton, QFileDialog, QLineEdit,
    QVBoxLayout, QWidget, QHBoxLayout, QTextEdit, QTableWidget, QTableWidgetItem, QMessageBox, QComboBox,
    QMenuBar, QMenu, QTabWidget, QFormLayout, QProgressBar, QGroupBox, QSplitter,
    QDialog, QDialogButtonBox, QCheckBox, QRadioButton, QButtonGroup
)
from PySide6.QtCore import QThread, Signal, QTimer
from PySide6.QtGui import QColor, QFont, QAction
import pandas as pd
from logic import AutoChecker
import math

class ConnectionDialog(QDialog):
    """连接确认对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("连接确认")
        self.setModal(True)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # 连接信息显示
        info_label = QLabel("请确认以下连接信息：")
        info_label.setStyleSheet("QLabel { font-weight: bold; }")
        layout.addWidget(info_label)
        
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(150)
        layout.addWidget(self.info_text)
        
        # 确认选项
        self.ready_check = QCheckBox("我已准备好开始对点测试")
        layout.addWidget(self.ready_check)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def set_connection_info(self, info_dict):
        """设置连接信息"""
        info_text = f"""
协议标准: {info_dict.get('protocol', '未知')}
目标IP: {info_dict.get('ip', '未知')}
端口: {info_dict.get('port', '未知')}
子网掩码: {info_dict.get('subnet_mask', '未知')}
网关: {info_dict.get('gateway', '未知')}
点表文件: {info_dict.get('point_table', '未知')}
SCD文件: {info_dict.get('scd_file', '未知')}
对点速度: {info_dict.get('speed', '未知')}
        """
        self.info_text.setPlainText(info_text.strip())
    
    def is_ready(self):
        """检查是否已准备好"""
        return self.ready_check.isChecked()

class ReportFormatDialog(QDialog):
    """报告格式选择对话框"""
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("选择报告格式")
        self.setModal(True)
        self.setup_ui()
        
    def setup_ui(self):
        layout = QVBoxLayout()
        
        # 报告格式选择
        format_group = QGroupBox("报告格式")
        format_layout = QVBoxLayout()
        
        self.format_group = QButtonGroup()
        
        self.excel_radio = QRadioButton("Excel详细报告 (.xlsx)")
        self.excel_radio.setToolTip("包含多个工作表：汇总、详细数据、错误数据、统计分析")
        self.excel_radio.setChecked(True)
        format_layout.addWidget(self.excel_radio)
        self.format_group.addButton(self.excel_radio, 0)
        
        self.csv_radio = QRadioButton("CSV数据文件 (.csv)")
        self.csv_radio.setToolTip("简单的逗号分隔值文件，便于导入其他系统")
        format_layout.addWidget(self.csv_radio)
        self.format_group.addButton(self.csv_radio, 1)
        
        self.html_radio = QRadioButton("HTML网页报告 (.html)")
        self.html_radio.setToolTip("可在浏览器中查看的网页格式报告")
        format_layout.addWidget(self.html_radio)
        self.format_group.addButton(self.html_radio, 2)
        
        self.pdf_radio = QRadioButton("PDF报告 (.pdf)")
        self.pdf_radio.setToolTip("便携式文档格式，需要安装reportlab库")
        format_layout.addWidget(self.pdf_radio)
        self.format_group.addButton(self.pdf_radio, 3)
        
        format_group.setLayout(format_layout)
        layout.addWidget(format_group)
        
        # 内容选择
        content_group = QGroupBox("报告内容")
        content_layout = QVBoxLayout()
        
        self.summary_check = QCheckBox("包含测试汇总")
        self.summary_check.setChecked(True)
        self.summary_check.setToolTip("包含测试参数、统计概要等信息")
        content_layout.addWidget(self.summary_check)
        
        self.details_check = QCheckBox("包含详细数据")
        self.details_check.setChecked(True)
        self.details_check.setToolTip("包含每个数据点的详细对比结果")
        content_layout.addWidget(self.details_check)
        
        content_group.setLayout(content_layout)
        layout.addWidget(content_group)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
        
        self.setLayout(layout)
    
    def get_format(self):
        """获取选择的报告格式"""
        checked_id = self.format_group.checkedId()
        formats = ["Excel详细报告", "CSV数据", "HTML报告", "PDF报告"]
        return formats[checked_id] if checked_id >= 0 else "Excel详细报告"
    
    def include_summary(self):
        """是否包含汇总"""
        return self.summary_check.isChecked()
    
    def include_details(self):
        """是否包含详细数据"""
        return self.details_check.isChecked()

class PointTablePreview(QDialog):
    """点表预览对话框"""
    def __init__(self, point_data, parent=None):
        super().__init__(parent)
        self.setWindowTitle("点表预览")
        self.setModal(True)
        self.setGeometry(200, 200, 800, 600)
        self.setup_ui(point_data)
        
    def setup_ui(self, point_data):
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("点表内容预览")
        title_label.setStyleSheet("QLabel { font-size: 14px; font-weight: bold; }")
        layout.addWidget(title_label)
        
        # 点表表格
        self.table = QTableWidget()
        if point_data and len(point_data) > 0:
            headers = list(point_data[0].keys())
            self.table.setColumnCount(len(headers))
            self.table.setHorizontalHeaderLabels(headers)
            self.table.setRowCount(len(point_data))
            
            for row_idx, row in enumerate(point_data):
                for col_idx, key in enumerate(headers):
                    item = QTableWidgetItem(str(row.get(key, '')))
                    self.table.setItem(row_idx, col_idx, item)
        
        layout.addWidget(self.table)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok)
        button_box.accepted.connect(self.accept)
        layout.addWidget(button_box)
        
        self.setLayout(layout)

class CommunicationThread(QThread):
    """通信线程"""
    status_update = Signal(str, str)  # 状态, 详细信息
    connection_result = Signal(bool, str)  # 成功/失败, 消息
    
    def __init__(self, host, port):
        super().__init__()
        self.host = host
        self.port = port
        self.socket = None
        self.is_connected = False
        
    def run(self):
        """测试连接"""
        try:
            self.status_update.emit("connecting", f"正在连接到 {self.host}:{self.port}...")
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(5)
            self.socket.connect((self.host, self.port))
            self.is_connected = True
            self.connection_result.emit(True, f"成功连接到 {self.host}:{self.port}")
        except Exception as e:
            self.connection_result.emit(False, f"连接失败: {str(e)}")
        finally:
            if self.socket:
                self.socket.close()
    
    def disconnect(self):
        """断开连接"""
        if self.socket:
            self.socket.close()
            self.is_connected = False

def safe_float(x):
    try:
        f = float(x)
        if math.isnan(f):
            return 0.0
        return f
    except Exception:
        return 0.0

def safe_bool(x):
    """安全地转换为布尔值，支持多种输入格式"""
    try:
        if isinstance(x, bool):
            return x
        elif isinstance(x, str):
            x_str = x.strip().lower()
            if x_str in ['true', '1', 'on', 'yes']:
                return True
            elif x_str in ['false', '0', 'off', 'no']:
                return False
            else:
                # 尝试转换为数字再判断
                return bool(int(float(x)))
        else:
            # 数字类型直接转换
            return bool(int(float(x)))
    except Exception:
        return False

class CheckThread(QThread):
    progress = Signal(int, int, str, str, object)  # 新增: 传递实际值
    finished = Signal(list)
    status_update = Signal(str, str)  # 状态, 详细信息

    def __init__(self, checker, interval=0.2, analog_tolerance=0.1):
        super().__init__()
        self.checker = checker
        self.interval = interval
        self.analog_tolerance = analog_tolerance  # 遥测允许误差

    def run(self):
        print("[调试] CheckThread.run() 已进入")
        if hasattr(self.checker, 'adapter') and hasattr(self.checker, 'network_params'):
            ip = self.checker.network_params.get('ip', '')
            port = self.checker.network_params.get('port', 102)
            subnet_mask = self.checker.network_params.get('subnet_mask', '')
            gateway = self.checker.network_params.get('gateway', '')
            print(f"[调试] CheckThread.run() 自动调用connect: ip={ip}, port={port}")
            self.checker.adapter.connect(ip, port, subnet_mask=subnet_mask, gateway=gateway)
        if hasattr(self.checker, 'mapped_points'):
            print(f"[调试] CheckThread.run() mapped_points共 {len(self.checker.mapped_points)} 条")
            print(f"[调试] CheckThread.run() 前10个点名: {[p.get('SignalName') for p in self.checker.mapped_points[:10]]}")
            print(f"[调试] CheckThread.run() 所有点名: {[p.get('SignalName') for p in self.checker.mapped_points]}")
        print(f"[调试] CheckThread.run() mapped_points类型: {type(self.checker.mapped_points)}")
        print(f"[调试] CheckThread.run() mapped_points长度: {len(self.checker.mapped_points)}")
        self.status_update.emit("checking", "开始对点测试...")
        results = []
        total = len(self.checker.mapped_points)
        for idx, point in enumerate(self.checker.mapped_points, 1):
            try:
                variable = point.get('SignalName') or point.get('变量名')
                # 修复期望值获取逻辑 - 不能使用or操作符，因为False是有效值
                expected = point.get('ExpectedValue')
                if expected is None:
                    expected = point.get('期望值')
                print(f"[调试] 期望值获取: 点名={variable}, ExpectedValue={point.get('ExpectedValue')}, 最终expected={expected}")
                value = None
                check_result = ''
                # 读取实际值（兼容AutoChecker/adapter）
                if hasattr(self.checker, 'adapter'):
                    try:
                        value = self.checker.adapter.read_data(variable)
                    except Exception:
                        value = None
                if value is None:
                    value = point.get('actual_value') or point.get('value') or point.get('Value') or point.get('实际值')
                # 判断类型字段 - 修复字段映射问题
                point_type = point.get('Type') or point.get('DataType') or point.get('类型') or ''
                print(f"[调试] 字段映射检查: 点名={variable}")
                print(f"[调试]   - Type字段: {point.get('Type')}")
                print(f"[调试]   - DataType字段: {point.get('DataType')}")
                print(f"[调试]   - 最终类型: {point_type}")
                # 比对逻辑 - 修复遥信比对问题
                print(f"[调试] 比对前检查: 点名={variable}, 期望值={expected}, 实际值={value}, 类型={point_type}")
                
                # 如果没有期望值，只显示采集值
                if expected is None or str(expected).strip() == '':
                    check_result = f'采集值:{value}'
                    print(f"[调试] 无期望值，显示采集值: {variable}")
                elif value is None:
                    check_result = f'无法采集数据'
                    print(f"[调试] 无法采集数据: {variable}")
                else:
                    # 根据点类型进行比对
                    if str(point_type).strip() == '遥测':
                        try:
                            expected_float = float(expected)
                            actual_float = float(value)
                            if abs(actual_float - expected_float) <= self.analog_tolerance:
                                check_result = '正确'
                            else:
                                check_result = f'错误(期望:{expected}, 实际:{value})'
                            print(f"[调试] 遥测比对: {variable}, 期望={expected_float}, 实际={actual_float}, 结果={check_result}")
                        except Exception as e:
                            check_result = f'错误(期望:{expected}, 实际:{value})'
                            print(f"[调试] 遥测比对异常: {e}")
                    elif str(point_type).strip() == '遥信':
                        try:
                            expected_bool = safe_bool(expected)
                            actual_bool = safe_bool(value)
                            print(f"[调试] 遥信比对详细: 点名={variable}")
                            print(f"[调试]   - 原始期望值: {expected} (类型: {type(expected)})")
                            print(f"[调试]   - 转换期望值: {expected_bool} (类型: {type(expected_bool)})")
                            print(f"[调试]   - 原始实际值: {value} (类型: {type(value)})")
                            print(f"[调试]   - 转换实际值: {actual_bool} (类型: {type(actual_bool)})")
                            
                            # 如果双方布尔值一致，则判断为正确，反之错误
                            if expected_bool == actual_bool:
                                check_result = '正确'
                                print(f"[调试]   - 比对结果: 正确 (期望{expected_bool} == 实际{actual_bool})")
                            else:
                                check_result = '错误'
                                print(f"[调试]   - 比对结果: 错误 (期望{expected_bool} != 实际{actual_bool})")
                        except Exception as e:
                            check_result = '错误'
                            print(f"[调试] 遥信比对异常: {e}")
                            traceback.print_exc()
                    else:
                        # 其他类型默认严格等于
                        if str(value) == str(expected):
                            check_result = '正确'
                        else:
                            check_result = f'错误(期望:{expected}, 实际:{value})'
                        print(f"[调试] 其他类型比对: {variable}, 期望={expected}, 实际={value}, 结果={check_result}")
                result = point.copy()
                result['actual_value'] = value
                result['check_result'] = check_result
                results.append(result)
                self.progress.emit(idx, total, variable, check_result, value)
                time.sleep(self.interval)
            except Exception as e:
                print(f"[调试] 对点线程采集第{idx}个点时异常: {e}, 点名: {point.get('SignalName')}")
                traceback.print_exc()
        self.status_update.emit("completed", "对点测试完成")
        self.finished.emit(results)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("自动对点机 - 检验模式")
        self.setGeometry(100, 100, 1400, 1000)

        # 变量初始化
        self.scd_path = ''
        self.point_path = ''
        self.ip = ''
        self.subnet_mask = ''
        self.gateway = ''
        self.port = 102
        self.protocol = 'IEC 61850'
        self.checker = None
        self.check_thread = None
        self.comm_thread = None
        self.current_results = []
        self.check_interval = 0.2
        self.all_results = []
        self.is_connected = False
        self.point_data = []

        # 创建菜单栏
        self.create_menu()
        
        # 创建标签页
        self.tabs = QTabWidget()
        self.setCentralWidget(self.tabs)
        
        # 创建对点操作页
        self.create_operation_tab()
        
        # 创建日志信息页
        self.create_log_tab()
        
        # 创建对点报告页
        self.create_report_tab()

    def create_menu(self):
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件")
        open_point_action = QAction("打开点表", self)
        open_point_action.triggered.connect(self.select_point)
        file_menu.addAction(open_point_action)
        
        open_scd_action = QAction("打开SCD", self)
        open_scd_action.triggered.connect(self.select_scd)
        file_menu.addAction(open_scd_action)
        
        export_action = QAction("导出报告", self)
        export_action.triggered.connect(self.export_report)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 设置菜单
        settings_menu = menubar.addMenu("设置")
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助")
        about_action = QAction("关于", self)
        about_action.triggered.connect(lambda: QMessageBox.information(self, "关于", "自动对点机 - 检验模式\n版本1.0"))
        help_menu.addAction(about_action)

    def create_operation_tab(self):
        self.op_tab = QWidget()
        main_layout = QVBoxLayout()
        
        # 创建分割器
        splitter = QSplitter()
        
        # 左侧参数配置区域
        left_widget = QWidget()
        left_layout = QVBoxLayout()
        
        # 参数设置区域
        param_group = QGroupBox("参数设置")
        param_layout = QFormLayout()
        
        # 文件选择
        self.scd_btn = QPushButton("选择SCD文件")
        self.scd_btn.clicked.connect(self.select_scd)
        self.scd_label = QLabel("未选择")
        param_layout.addRow(self.scd_btn, self.scd_label)
        
        self.point_btn = QPushButton("选择点表文件")
        self.point_btn.clicked.connect(self.select_point)
        self.point_label = QLabel("未选择")
        param_layout.addRow(self.point_btn, self.point_label)
        
        # 网络参数
        self.ip_edit = QLineEdit()
        param_layout.addRow(QLabel("IED IP:"), self.ip_edit)
        
        self.mask_edit = QLineEdit()
        param_layout.addRow(QLabel("子网掩码:"), self.mask_edit)
        
        self.gateway_edit = QLineEdit()
        param_layout.addRow(QLabel("网关:"), self.gateway_edit)
        
        self.port_edit = QLineEdit("102")
        param_layout.addRow(QLabel("端口:"), self.port_edit)
        
        # 协议和速度
        self.protocol_combo = QComboBox()
        self.protocol_combo.addItems(["IEC 61850", "DL/T 634.5104"])
        self.protocol_combo.currentTextChanged.connect(self.on_protocol_changed)
        param_layout.addRow(QLabel("协议标准:"), self.protocol_combo)
        
        self.speed_combo = QComboBox()
        self.speed_combo.addItems(["0.1秒", "0.2秒", "0.5秒", "1秒"])
        self.speed_combo.setCurrentIndex(1)
        param_layout.addRow(QLabel("对点速度:"), self.speed_combo)
        
        param_group.setLayout(param_layout)
        left_layout.addWidget(param_group)
        
        # 连接控制区域
        connection_group = QGroupBox("连接控制")
        connection_layout = QVBoxLayout()
        
        # 连接状态
        self.connection_status = QLabel("连接状态：未连接")
        self.connection_status.setStyleSheet("QLabel { font-weight: bold; color: red; }")
        connection_layout.addWidget(self.connection_status)
        
        # 连接按钮
        self.connect_btn = QPushButton("测试连接")
        self.connect_btn.clicked.connect(self.test_connection)
        connection_layout.addWidget(self.connect_btn)
        
        # 点表预览按钮
        self.preview_btn = QPushButton("预览点表")
        self.preview_btn.clicked.connect(self.preview_point_table)
        self.preview_btn.setEnabled(False)
        connection_layout.addWidget(self.preview_btn)
        
        connection_group.setLayout(connection_layout)
        left_layout.addWidget(connection_group)
        
        # 操作控制区域
        control_group = QGroupBox("操作控制")
        control_layout = QVBoxLayout()
        
        self.check_btn = QPushButton("开始对点测试")
        self.check_btn.clicked.connect(self.run_check)
        self.check_btn.setStyleSheet("QPushButton { font-size: 14px; padding: 10px; }")
        self.check_btn.setEnabled(False)
        control_layout.addWidget(self.check_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        control_layout.addWidget(self.progress_bar)
        
        # 进度标签
        self.progress_label = QLabel("进度：未开始")
        self.progress_label.setStyleSheet("QLabel { font-size: 12px; color: gray; }")
        control_layout.addWidget(self.progress_label)
        
        control_group.setLayout(control_layout)
        left_layout.addWidget(control_group)
        
        left_widget.setLayout(left_layout)
        splitter.addWidget(left_widget)
        
        # 右侧实时显示区域
        right_widget = QWidget()
        right_layout = QVBoxLayout()
        
        # 实时对点显示
        realtime_group = QGroupBox("实时对点信息")
        realtime_layout = QVBoxLayout()
        
        # 当前对点信息
        self.current_point_label = QLabel("当前对点：无")
        self.current_point_label.setStyleSheet("QLabel { font-size: 12px; font-weight: bold; }")
        realtime_layout.addWidget(self.current_point_label)
        
        # 对点结果表格
        self.realtime_table = QTableWidget()
        self.realtime_table.setColumnCount(4)
        self.realtime_table.setHorizontalHeaderLabels(['点名', '本侧期望值', '对侧实际值', '结果'])
        self.realtime_table.setMaximumHeight(200)
        realtime_layout.addWidget(self.realtime_table)
        
        realtime_group.setLayout(realtime_layout)
        right_layout.addWidget(realtime_group)
        
        # 筛选区域
        filter_layout = QHBoxLayout()
        self.filter_combo = QComboBox()
        self.filter_combo.addItems(["全部", "只看错误", "只看未匹配"])
        self.filter_combo.currentTextChanged.connect(self.apply_filter)
        filter_layout.addWidget(QLabel("结果筛选:"))
        filter_layout.addWidget(self.filter_combo)
        right_layout.addLayout(filter_layout)
        
        # 结果表格
        self.result_table = QTableWidget()
        right_layout.addWidget(self.result_table)
        
        # 导出按钮
        self.export_btn = QPushButton("导出对点报告")
        self.export_btn.clicked.connect(self.export_report)
        self.export_btn.setEnabled(False)
        right_layout.addWidget(self.export_btn)
        
        right_widget.setLayout(right_layout)
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([450, 950])
        
        main_layout.addWidget(splitter)
        self.op_tab.setLayout(main_layout)
        self.tabs.addTab(self.op_tab, "对点操作")

    def create_log_tab(self):
        self.log_tab = QWidget()
        log_layout = QVBoxLayout()
        
        self.log_edit = QTextEdit()
        self.log_edit.setReadOnly(True)
        self.log_edit.setFont(QFont("Consolas", 10))
        log_layout.addWidget(self.log_edit)
        
        self.log_tab.setLayout(log_layout)
        self.tabs.addTab(self.log_tab, "日志信息")

    def create_report_tab(self):
        self.report_tab = QWidget()
        report_layout = QVBoxLayout()
        
        # 报告标题
        title_label = QLabel("对点报告")
        title_label.setStyleSheet("QLabel { font-size: 16px; font-weight: bold; margin: 10px; }")
        report_layout.addWidget(title_label)
        
        # 统计信息
        self.stats_label = QLabel("统计信息：等待对点完成...")
        self.stats_label.setStyleSheet("QLabel { font-size: 12px; margin: 5px; }")
        report_layout.addWidget(self.stats_label)
        
        # 报告表格
        self.report_table = QTableWidget()
        report_layout.addWidget(self.report_table)
        
        self.report_tab.setLayout(report_layout)
        self.tabs.addTab(self.report_tab, "对点报告")

    def select_scd(self):
        path, _ = QFileDialog.getOpenFileName(self, "选择SCD文件", "", "SCD Files (*.scd *.xml)")
        if path:
            self.scd_path = path
            self.scd_label.setText(path)
            self.log(f"已选择SCD文件: {path}")

    def select_point(self):
        path, _ = QFileDialog.getOpenFileName(self, "选择点表文件", "", "点表文件 (*.csv *.xls *.xlsx)")
        if path:
            self.point_path = path
            self.point_label.setText(path)
            self.log(f"已选择点表文件: {path}")
            self.load_point_table(path)

    def load_point_table(self, path):
        """加载点表文件"""
        try:
            if path.endswith('.csv'):
                df = pd.read_csv(path)
            else:
                df = pd.read_excel(path)
            self.point_data = df.to_dict('records')
            print(f"[调试] 主站加载点表共 {len(self.point_data)} 条")
            print(f"[调试] 主站前10个点名: {[p.get('SignalName') for p in self.point_data[:10]]}")
            print(f"[调试] 主站所有点名: {[p.get('SignalName') for p in self.point_data]}")
            self.preview_btn.setEnabled(True)
            self.log(f"成功加载点表，共 {len(self.point_data)} 个数据点")
        except Exception as e:
            self.log(f"加载点表失败: {e}")
            QMessageBox.warning(self, "错误", f"加载点表失败: {e}")

    def preview_point_table(self):
        """预览点表"""
        if self.point_data:
            dialog = PointTablePreview(self.point_data, self)
            dialog.exec()
        else:
            QMessageBox.warning(self, "提示", "请先加载点表文件")

    def on_protocol_changed(self, text):
        if text == "DL/T 634.5104":
            self.scd_btn.setEnabled(False)
            self.scd_label.setEnabled(False)
        else:
            self.scd_btn.setEnabled(True)
            self.scd_label.setEnabled(True)

    def test_connection(self):
        """测试连接"""
        self.ip = self.ip_edit.text().strip()
        try:
            self.port = int(self.port_edit.text().strip())
        except ValueError:
            self.port = 102
        
        if not self.ip:
            QMessageBox.warning(self, "参数缺失", "请输入IED IP地址")
            return
        
        self.log(f"开始测试连接到 {self.ip}:{self.port}")
        self.connect_btn.setEnabled(False)
        self.connect_btn.setText("连接中...")
        
        self.comm_thread = CommunicationThread(self.ip, self.port)
        self.comm_thread.connection_result.connect(self.on_connection_result)
        self.comm_thread.start()

    def on_connection_result(self, success, message):
        """连接结果处理"""
        self.connect_btn.setEnabled(True)
        self.connect_btn.setText("测试连接")
        
        if success:
            self.is_connected = True
            self.connection_status.setText("连接状态：已连接")
            self.connection_status.setStyleSheet("QLabel { font-weight: bold; color: green; }")
            self.check_btn.setEnabled(True)
            self.log(f"✓ {message}")
        else:
            self.is_connected = False
            self.connection_status.setText("连接状态：连接失败")
            self.connection_status.setStyleSheet("QLabel { font-weight: bold; color: red; }")
            self.check_btn.setEnabled(False)
            self.log(f"✗ {message}")

    def run_check(self):
        """开始对点测试"""
        self.ip = self.ip_edit.text().strip()
        self.subnet_mask = self.mask_edit.text().strip()
        self.gateway = self.gateway_edit.text().strip()
        try:
            self.port = int(self.port_edit.text().strip())
        except ValueError:
            self.port = 102
        self.protocol = self.protocol_combo.currentText()
        speed_map = {"0.1秒": 0.1, "0.2秒": 0.2, "0.5秒": 0.5, "1秒": 1.0}
        self.check_interval = speed_map.get(self.speed_combo.currentText(), 0.2)
        
        if not self.is_connected:
            QMessageBox.warning(self, "连接错误", "请先测试连接")
            return
        
        if not self.point_data:
            QMessageBox.warning(self, "参数缺失", "请先加载点表文件")
            return
        
        # 显示连接确认对话框
        dialog = ConnectionDialog(self)
        connection_info = {
            'protocol': self.protocol,
            'ip': self.ip,
            'port': self.port,
            'subnet_mask': self.subnet_mask,
            'gateway': self.gateway,
            'point_table': self.point_path,
            'scd_file': self.scd_path if self.protocol == "IEC 61850" else "不需要",
            'speed': self.speed_combo.currentText()
        }
        dialog.set_connection_info(connection_info)
        
        if dialog.exec() != QDialog.Accepted:
            return
        
        if not dialog.is_ready():
            QMessageBox.warning(self, "确认", "请确认已准备好开始对点测试")
            return
        
        self.log(f"开始对点校核...\n协议: {self.protocol}  IP: {self.ip}  端口: {self.port}")
        
        try:
            self.checker = AutoChecker(
                self.scd_path if self.protocol == "IEC 61850" else None,
                self.point_path,
                network_params={
                    'ip': self.ip,
                    'subnet_mask': self.subnet_mask,
                    'gateway': self.gateway,
                    'port': self.port,
                    'protocol': self.protocol
                }
            )
            # 初始化进度显示
            self.progress_bar.setVisible(True)
            self.progress_bar.setMaximum(len(self.checker.mapped_points))
            self.progress_bar.setValue(0)
            self.progress_label.setText("进度：开始对点...")
            self.export_btn.setEnabled(False)
            self.current_results = []
            # 清空实时表格
            self.realtime_table.setRowCount(0)
            self.check_thread = CheckThread(self.checker, interval=self.check_interval)
            self.check_thread.progress.connect(self.update_progress)
            self.check_thread.finished.connect(self.check_finished)
            self.check_thread.status_update.connect(self.update_status)
            self.check_btn.setEnabled(False)
            self.check_thread.start()
        except Exception as e:
            self.log(f"对点校核异常: {e}")
            QMessageBox.critical(self, "错误", str(e))

    def update_status(self, status, details):
        """更新状态信息"""
        self.log(f"[{status.upper()}] {details}")

    def update_progress(self, idx, total, point_name, status, actual_value=None):
        """更新进度和实时显示"""
        self.progress_bar.setValue(idx)
        self.progress_label.setText(f"进度：{idx}/{total} 当前：{point_name}")
        self.current_point_label.setText(f"当前对点：{point_name} - {status}")
        row = self.realtime_table.rowCount()
        self.realtime_table.insertRow(row)
        expected_value = "未知"
        point_type = ""
        for point in self.checker.mapped_points:
            if point.get('SignalName') == point_name:
                expected_value = point.get('ExpectedValue', '未知')
                point_type = point.get('Type', '')
                break
        # 优先用传递的actual_value
        if actual_value is None:
            for r in self.current_results:
                if r.get('SignalName') == point_name:
                    actual_value = r.get('actual_value') or r.get('value') or r.get('Value') or r.get('实际值')
                    break
        if actual_value is None:
            actual_value = "无数据"
        
        # 对遥信点进行布尔值显示转换
        display_expected = expected_value
        display_actual = actual_value
        if str(point_type).strip() == '遥信':
            try:
                expected_bool = safe_bool(expected_value)
                actual_bool = safe_bool(actual_value)
                display_expected = f"{expected_value}→{expected_bool}"
                display_actual = f"{actual_value}→{actual_bool}"
            except:
                pass
        
        self.realtime_table.setItem(row, 0, QTableWidgetItem(point_name))
        self.realtime_table.setItem(row, 1, QTableWidgetItem(str(display_expected)))
        self.realtime_table.setItem(row, 2, QTableWidgetItem(str(display_actual)))
        self.realtime_table.setItem(row, 3, QTableWidgetItem(status))
        # 判断结果显示颜色
        item = self.realtime_table.item(row, 3)
        if status == '正确':
            item.setBackground(QColor(200, 255, 200))  # 绿色
        elif '错误' in status:
            item.setBackground(QColor(255, 200, 200))  # 红色
        else:
            # 其他状态（如采集值显示）保持默认颜色
            pass
        self.realtime_table.scrollToBottom()

    def check_finished(self, results):
        self.progress_label.setText("进度：对点完成")
        self.progress_bar.setVisible(False)
        self.current_results = results
        self.show_results(results)
        self.update_report(results)
        self.export_btn.setEnabled(True)
        self.check_btn.setEnabled(True)
        QMessageBox.information(self, "对点完成", "对点流程已完成，可导出报告。")

    def update_report(self, results):
        """更新对点报告"""
        if not results:
            return
        
        # 统计信息
        total = len(results)
        correct = sum(1 for r in results if '正确' in str(r.get('check_result', '')))
        error = sum(1 for r in results if '错误' in str(r.get('check_result', '')))
        unmatched = total - correct - error
        
        stats_text = f"总计：{total} | 正确：{correct} | 错误：{error} | 未匹配：{unmatched}"
        self.stats_label.setText(f"统计信息：{stats_text}")
        
        # 更新报告表格
        headers = list(results[0].keys())
        self.report_table.setColumnCount(len(headers))
        self.report_table.setHorizontalHeaderLabels(headers)
        self.report_table.setRowCount(len(results))
        
        for row_idx, row in enumerate(results):
            for col_idx, key in enumerate(headers):
                item = QTableWidgetItem(str(row.get(key, '')))
                if key == 'check_result':
                    val = str(row.get(key, ''))
                    if '正确' in val:
                        item.setBackground(QColor(200, 255, 200))
                    elif '错误' in val:
                        item.setBackground(QColor(255, 200, 200))
                    elif '未唯一匹配' in val:
                        item.setBackground(QColor(255, 255, 180))
                self.report_table.setItem(row_idx, col_idx, item)

    def export_report(self):
        if not self.current_results:
            QMessageBox.warning(self, "无数据", "没有可导出的对点结果。")
            return
        
        # 显示报告格式选择对话框
        dialog = ReportFormatDialog(self)
        if dialog.exec() != QDialog.Accepted:
            return
        
        report_format = dialog.get_format()
        include_summary = dialog.include_summary()
        include_details = dialog.include_details()
        
        # 根据格式选择文件扩展名
        if report_format == "Excel详细报告":
            default_name = "对点详细报告.xlsx"
            file_filter = "Excel文件 (*.xlsx)"
        elif report_format == "CSV数据":
            default_name = "对点数据.csv"
            file_filter = "CSV文件 (*.csv)"
        elif report_format == "HTML报告":
            default_name = "对点报告.html"
            file_filter = "HTML文件 (*.html)"
        else:  # PDF报告
            default_name = "对点报告.pdf"
            file_filter = "PDF文件 (*.pdf)"
        
        path, _ = QFileDialog.getSaveFileName(self, "保存对点报告", default_name, file_filter)
        if path:
            try:
                if report_format == "Excel详细报告":
                    self.export_excel_report(path, include_summary, include_details)
                elif report_format == "CSV数据":
                    self.export_csv_report(path)
                elif report_format == "HTML报告":
                    self.export_html_report(path, include_summary, include_details)
                elif report_format == "PDF报告":
                    self.export_pdf_report(path, include_summary, include_details)
                
                QMessageBox.information(self, "导出成功", f"报告已导出到: {path}")
                self.log(f"报告导出成功: {path}")
            except Exception as e:
                QMessageBox.critical(self, "导出失败", f"导出失败: {e}")
                self.log(f"报告导出失败: {e}")
    
    def export_excel_report(self, path, include_summary, include_details):
        """导出Excel详细报告"""
        try:
            # 尝试使用openpyxl引擎
            with pd.ExcelWriter(path, engine='openpyxl') as writer:
                self._write_excel_content(writer, include_summary, include_details)
        except ImportError:
            try:
                # 回退到xlsxwriter引擎
                with pd.ExcelWriter(path, engine='xlsxwriter') as writer:
                    self._write_excel_content(writer, include_summary, include_details)
            except ImportError:
                # Excel引擎不可用，提示用户并导出为CSV
                from PySide6.QtWidgets import QMessageBox
                reply = QMessageBox.question(
                    self, 
                    "Excel引擎不可用", 
                    "系统未检测到Excel支持库(openpyxl或xlsxwriter)。\n\n是否改为导出CSV格式的详细报告？",
                    QMessageBox.Yes | QMessageBox.No
                )
                if reply == QMessageBox.Yes:
                    # 导出为CSV格式
                    csv_path = path.replace('.xlsx', '_详细报告.csv')
                    self.export_csv_with_summary(csv_path, include_summary, include_details)
                    raise Exception(f"已导出CSV格式报告: {csv_path}")
                else:
                    raise Exception("用户取消导出")
    
    def export_csv_with_summary(self, path, include_summary, include_details):
        """导出包含汇总信息的CSV报告"""
        lines = []
        
        if include_summary:
            lines.append("# 测试汇总信息")
            summary_data = self.generate_summary_data()
            for item in summary_data:
                lines.append(f"# {item[0]}: {item[1]}")
            lines.append("")
        
        if include_details and self.current_results:
            lines.append("# 详细对点数据")
            
            # 处理遥信点显示格式
            processed_results = []
            for result in self.current_results:
                processed_result = result.copy()
                # 对遥信点添加转换信息
                if result.get('Type') == '遥信':
                    expected = result.get('ExpectedValue', '')
                    actual = result.get('actual_value', '')
                    try:
                        expected_bool = safe_bool(expected)
                        actual_bool = safe_bool(actual)
                        processed_result['期望值_转换'] = f"{expected}→{expected_bool}"
                        processed_result['实际值_转换'] = f"{actual}→{actual_bool}"
                    except:
                        pass
                processed_results.append(processed_result)
            
            df = pd.DataFrame(processed_results)
            csv_content = df.to_csv(index=False, encoding='utf-8-sig')
            lines.append(csv_content)
        
        # 写入文件
        with open(path, 'w', encoding='utf-8-sig') as f:
            f.write('\n'.join(lines))
    
    def _write_excel_content(self, writer, include_summary, include_details):
        """写入Excel内容"""
        # 汇总信息
        if include_summary:
            summary_data = self.generate_summary_data()
            summary_df = pd.DataFrame(summary_data)
            summary_df.to_excel(writer, sheet_name='测试汇总', index=False)
        
        # 详细数据
        if include_details:
            # 处理遥信点显示格式
            processed_results = []
            for result in self.current_results:
                processed_result = result.copy()
                # 对遥信点添加转换信息
                if result.get('Type') == '遥信':
                    expected = result.get('ExpectedValue', '')
                    actual = result.get('actual_value', '')
                    try:
                        expected_bool = safe_bool(expected)
                        actual_bool = safe_bool(actual)
                        processed_result['期望值_显示'] = f"{expected}→{expected_bool}"
                        processed_result['实际值_显示'] = f"{actual}→{actual_bool}"
                    except:
                        pass
                processed_results.append(processed_result)
            
            df = pd.DataFrame(processed_results)
            df.to_excel(writer, sheet_name='详细数据', index=False)
        
        # 错误汇总
        error_results = [r for r in self.current_results if "错误" in str(r.get("check_result", ""))]
        if error_results:
            error_df = pd.DataFrame(error_results)
            error_df.to_excel(writer, sheet_name='错误数据', index=False)
        
        # 统计分析
        stats_data = self.generate_statistics_data()
        stats_df = pd.DataFrame(stats_data)
        stats_df.to_excel(writer, sheet_name='统计分析', index=False)
    
    def export_csv_report(self, path):
        """导出CSV数据报告"""
        df = pd.DataFrame(self.current_results)
        df.to_csv(path, index=False, encoding='utf-8-sig')
    
    def export_html_report(self, path, include_summary, include_details):
        """导出HTML报告"""
        html_content = self.generate_html_report(include_summary, include_details)
        with open(path, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def export_pdf_report(self, path, include_summary, include_details):
        """导出PDF报告（需要安装reportlab库）"""
        try:
            from reportlab.lib import colors
            from reportlab.lib.pagesizes import letter, A4
            from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
            from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
            from reportlab.pdfbase import pdfmetrics
            from reportlab.pdfbase.ttfonts import TTFont
            
            # 注册中文字体（可选，如果系统有的话）
            try:
                pdfmetrics.registerFont(TTFont('SimSun', 'C:/Windows/Fonts/simsun.ttc'))
                font_name = 'SimSun'
            except:
                font_name = 'Helvetica'
            
            doc = SimpleDocTemplate(path, pagesize=A4)
            elements = []
            styles = getSampleStyleSheet()
            
            # 标题
            title_style = ParagraphStyle('CustomTitle', parent=styles['Heading1'], 
                                       fontName=font_name, fontSize=16, alignment=1)
            title = Paragraph("自动对点测试报告", title_style)
            elements.append(title)
            elements.append(Spacer(1, 12))
            
            # 汇总信息
            if include_summary:
                summary_data = self.generate_summary_data()
                summary_table = Table(summary_data)
                summary_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, -1), font_name),
                    ('FONTSIZE', (0, 0), (-1, -1), 9),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                elements.append(summary_table)
                elements.append(Spacer(1, 12))
            
            # 详细数据（只显示前50条，避免PDF太大）
            if include_details and self.current_results:
                detail_data = [['点名', '期望值', '实际值', '结果']]
                for i, result in enumerate(self.current_results[:50]):
                    detail_data.append([
                        str(result.get('SignalName', '')),
                        str(result.get('ExpectedValue', '')),
                        str(result.get('actual_value', '')),
                        str(result.get('check_result', ''))
                    ])
                
                detail_table = Table(detail_data)
                detail_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, -1), font_name),
                    ('FONTSIZE', (0, 0), (-1, -1), 8),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                elements.append(detail_table)
            
            doc.build(elements)
            
        except ImportError:
            # 如果没有安装reportlab，则生成简单的文本报告
            txt_content = self.generate_text_report(include_summary, include_details)
            txt_path = path.replace('.pdf', '.txt')
            with open(txt_path, 'w', encoding='utf-8') as f:
                f.write(txt_content)
            raise Exception(f"未安装reportlab库，已生成文本报告: {txt_path}")
    
    def generate_summary_data(self):
        """生成汇总数据"""
        if not self.current_results:
            return []
        
        total_points = len(self.current_results)
        correct_points = len([r for r in self.current_results if r.get('check_result') == '正确'])
        error_points = len([r for r in self.current_results if '错误' in str(r.get('check_result', ''))])
        
        # 按类型统计
        type_stats = {}
        for result in self.current_results:
            point_type = result.get('Type', '未知')
            if point_type not in type_stats:
                type_stats[point_type] = {'total': 0, 'correct': 0, 'error': 0}
            type_stats[point_type]['total'] += 1
            if result.get('check_result') == '正确':
                type_stats[point_type]['correct'] += 1
            elif '错误' in str(result.get('check_result', '')):
                type_stats[point_type]['error'] += 1
        
        summary_data = [
            ['项目', '值'],
            ['测试时间', datetime.now().strftime("%Y-%m-%d %H:%M:%S")],
            ['测试协议', getattr(self, 'protocol', '未知')],
            ['目标IP', getattr(self, 'ip', '未知')],
            ['目标端口', str(getattr(self, 'port', '未知'))],
            ['点表文件', getattr(self, 'point_path', '未知')],
            ['总点数', str(total_points)],
            ['正确点数', str(correct_points)],
            ['错误点数', str(error_points)],
            ['正确率', f"{(correct_points/total_points*100):.1f}%" if total_points > 0 else "0%"],
        ]
        
        # 添加类型统计
        for point_type, stats in type_stats.items():
            summary_data.extend([
                [f'{point_type}_总数', str(stats['total'])],
                [f'{point_type}_正确', str(stats['correct'])],
                [f'{point_type}_错误', str(stats['error'])],
            ])
        
        return summary_data
    
    def generate_statistics_data(self):
        """生成统计分析数据"""
        if not self.current_results:
            return []
        
        stats_data = [['统计项', '数量', '百分比']]
        
        total = len(self.current_results)
        
        # 按结果分类统计
        result_counts = {}
        for result in self.current_results:
            check_result = result.get('check_result', '未知')
            if '正确' in check_result:
                key = '正确'
            elif '错误' in check_result:
                key = '错误'
            else:
                key = '其他'
            result_counts[key] = result_counts.get(key, 0) + 1
        
        for result_type, count in result_counts.items():
            percentage = f"{(count/total*100):.1f}%" if total > 0 else "0%"
            stats_data.append([result_type, str(count), percentage])
        
        # 按点类型分类统计
        type_counts = {}
        for result in self.current_results:
            point_type = result.get('Type', '未知')
            type_counts[point_type] = type_counts.get(point_type, 0) + 1
        
        stats_data.append(['', '', ''])  # 分隔行
        stats_data.append(['按类型统计', '', ''])
        for point_type, count in type_counts.items():
            percentage = f"{(count/total*100):.1f}%" if total > 0 else "0%"
            stats_data.append([point_type, str(count), percentage])
        
        return stats_data
    
    def generate_html_report(self, include_summary, include_details):
        """生成HTML报告内容"""
        html = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>自动对点测试报告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1 { color: #2c3e50; text-align: center; }
                h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 5px; }
                table { border-collapse: collapse; width: 100%; margin: 10px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #3498db; color: white; }
                tr:nth-child(even) { background-color: #f2f2f2; }
                .correct { background-color: #d4edda; }
                .error { background-color: #f8d7da; }
                .summary { background-color: #e9ecef; padding: 15px; border-radius: 5px; margin: 10px 0; }
            </style>
        </head>
        <body>
            <h1>自动对点测试报告</h1>
        """
        
        if include_summary:
            html += "<h2>测试汇总</h2><div class='summary'>"
            summary_data = self.generate_summary_data()
            for item in summary_data[1:]:  # 跳过表头
                html += f"<p><strong>{item[0]}:</strong> {item[1]}</p>"
            html += "</div>"
        
        if include_details and self.current_results:
            html += "<h2>详细结果</h2><table><tr><th>点名</th><th>类型</th><th>期望值</th><th>实际值</th><th>结果</th></tr>"
            for result in self.current_results:
                check_result = result.get('check_result', '')
                css_class = 'correct' if '正确' in check_result else 'error' if '错误' in check_result else ''
                
                # 对遥信点添加转换显示
                expected_display = str(result.get('ExpectedValue', ''))
                actual_display = str(result.get('actual_value', ''))
                if result.get('Type') == '遥信':
                    expected = result.get('ExpectedValue', '')
                    actual = result.get('actual_value', '')
                    try:
                        expected_bool = safe_bool(expected)
                        actual_bool = safe_bool(actual)
                        expected_display = f"{expected}→{expected_bool}"
                        actual_display = f"{actual}→{actual_bool}"
                    except:
                        pass
                
                html += f"""<tr class='{css_class}'>
                    <td>{result.get('SignalName', '')}</td>
                    <td>{result.get('Type', '')}</td>
                    <td>{expected_display}</td>
                    <td>{actual_display}</td>
                    <td>{check_result}</td>
                </tr>"""
            html += "</table>"
        
        # 统计图表（简单文本版）
        html += "<h2>统计分析</h2>"
        stats_data = self.generate_statistics_data()
        html += "<table><tr><th>统计项</th><th>数量</th><th>百分比</th></tr>"
        for stat in stats_data[1:]:  # 跳过表头
            html += f"<tr><td>{stat[0]}</td><td>{stat[1]}</td><td>{stat[2]}</td></tr>"
        html += "</table>"
        
        html += f"""
            <div class='summary'>
                <p><strong>报告生成时间:</strong> {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def generate_text_report(self, include_summary, include_details):
        """生成文本报告内容"""
        content = "自动对点测试报告\n"
        content += "=" * 50 + "\n\n"
        
        if include_summary:
            content += "测试汇总:\n" + "-" * 20 + "\n"
            summary_data = self.generate_summary_data()
            for item in summary_data[1:]:  # 跳过表头
                content += f"{item[0]}: {item[1]}\n"
            content += "\n"
        
        if include_details and self.current_results:
            content += "详细结果:\n" + "-" * 20 + "\n"
            for i, result in enumerate(self.current_results[:100]):  # 限制显示数量
                content += f"{i+1}. {result.get('SignalName', '')} "
                content += f"({result.get('Type', '')}) "
                content += f"期望:{result.get('ExpectedValue', '')} "
                content += f"实际:{result.get('actual_value', '')} "
                content += f"结果:{result.get('check_result', '')}\n"
            content += "\n"
        
        # 统计信息
        content += "统计分析:\n" + "-" * 20 + "\n"
        stats_data = self.generate_statistics_data()
        for stat in stats_data[1:]:  # 跳过表头
            if stat[0]:  # 非空行
                content += f"{stat[0]}: {stat[1]} ({stat[2]})\n"
        
        content += f"\n报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        
        return content

    def show_results(self, results):
        self.all_results = results or []
        self.apply_filter()

    def apply_filter(self):
        results = self.all_results
        filter_text = self.filter_combo.currentText()
        if filter_text == "只看错误":
            results = [r for r in self.all_results if "错误" in str(r.get("check_result", ""))]
        elif filter_text == "只看未匹配":
            results = [r for r in self.all_results if "未唯一匹配" in str(r.get("check_result", ""))]
        
        if not results:
            self.result_table.setRowCount(0)
            self.result_table.setColumnCount(0)
            return
        
        headers = list(results[0].keys())
        self.result_table.setColumnCount(len(headers))
        self.result_table.setHorizontalHeaderLabels(headers)
        self.result_table.setRowCount(len(results))
        
        for row_idx, row in enumerate(results):
            for col_idx, key in enumerate(headers):
                cell_value = row.get(key, '')
                
                # 对遥信点的期望值和实际值进行显示转换
                if key in ['ExpectedValue', '期望值'] and row.get('Type') == '遥信':
                    try:
                        original_value = cell_value
                        bool_value = safe_bool(cell_value)
                        cell_value = f"{original_value}→{bool_value}"
                    except:
                        pass
                elif key in ['actual_value', '实际值'] and row.get('Type') == '遥信':
                    try:
                        original_value = cell_value
                        bool_value = safe_bool(cell_value)
                        cell_value = f"{original_value}→{bool_value}"
                    except:
                        pass
                
                item = QTableWidgetItem(str(cell_value))
                if key == 'check_result':
                    val = str(row.get(key, ''))
                    if '正确' in val:
                        item.setBackground(QColor(200, 255, 200))
                    elif '错误' in val:
                        item.setBackground(QColor(255, 200, 200))
                    elif '未唯一匹配' in val:
                        item.setBackground(QColor(255, 255, 180))
                self.result_table.setItem(row_idx, col_idx, item)

    def log(self, msg):
        self.log_edit.append(msg)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec()) 