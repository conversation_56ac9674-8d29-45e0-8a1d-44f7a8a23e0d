# Auto_Point Web风格对点机 - 用户操作指南

## 🚀 启动程序

```bash
python main_web_functional.py
```

## 📋 界面概览

### **整体布局**
- **顶部功能栏**: 系统标题、网络状态、实时时间
- **左侧导航栏**: 6大功能模块，树形结构
- **主内容区**: 动态页面内容，支持实际操作

### **功能模块**
1. 📁 **配置文件管理** - 文件上传、解析、预览
2. 🔧 **通信配置** - 网络参数、连接测试
3. 🏭 **仿真模型管理** - 设备管理、参数配置
4. 📊 **遥信遥测管理** - 实时数据、趋势图表
5. 🎮 **遥控验收** - 自动对点、手动测试
6. 📋 **报告管理** - 验收报告、历史记录

## 📁 配置文件管理操作

### **文件上传功能** ✅ 可操作
1. **选择文件**
   - 点击 "选择文件" 按钮
   - 支持格式: SCD、RCD、CSV
   - 自动显示文件信息

2. **解析文件**
   - 点击 "解析文件" 按钮
   - 实时显示解析进度
   - 支持真实文件解析

3. **查看结果**
   - 文件信息区域显示详细信息
   - 数据预览表格显示解析结果
   - 支持CSV和SCD文件格式

### **操作步骤**
```
1. 点击左侧导航 "📁 配置文件管理" → "SCD文件解析"
2. 点击 "选择文件" 按钮
3. 选择测试文件 (如: large_substation_500points.scd)
4. 点击 "解析文件" 按钮
5. 等待解析完成，查看结果
```

### **测试文件推荐**
- ✅ `large_substation_500points.scd` (500个数据点)
- ✅ `large_points_500.csv` (CSV格式数据)
- ✅ `test_substation.scd` (标准测试文件)

## 🔧 通信配置操作

### **网络配置功能** ✅ 可操作
1. **参数设置**
   - IP地址: 默认 127.0.0.1
   - 端口: 默认 102
   - 协议: IEC 61850 / DL/T 634.5104

2. **连接测试**
   - 点击 "测试连接" 按钮
   - 实时显示连接状态
   - 自动更新网络状态指示器

3. **配置保存**
   - 点击 "保存配置" 按钮
   - 配置保存到 communication_config.json

### **操作步骤**
```
1. 点击左侧导航 "🔧 通信配置" → "网关配置"
2. 设置IP地址: 127.0.0.1
3. 设置端口: 102
4. 选择协议: IEC 61850
5. 点击 "测试连接" 按钮
6. 查看连接状态结果
7. 点击 "保存配置" 保存设置
```

## 🎮 自动对点操作

### **对点测试功能** ✅ 可操作
1. **测试配置**
   - 测试模式: 自动对点、手动测试、批量验证
   - 测试范围: 全部信号、遥信信号、遥测信号

2. **执行测试**
   - 点击 "开始测试" 按钮
   - 实时显示测试进度
   - 支持停止测试功能

3. **查看结果**
   - 测试结果表格显示详细信息
   - 通过/失败状态颜色区分
   - 包含期望值、实际值对比

### **操作步骤**
```
1. 点击左侧导航 "🎮 遥控验收" → "自动对点"
2. 选择测试模式: 自动对点
3. 选择测试范围: 全部信号
4. 点击 "开始测试" 按钮
5. 观察测试进度和状态
6. 查看测试结果表格
7. 分析通过/失败的信号
```

## 🎯 实际操作演示

### **完整测试流程**

#### **第一步: 文件管理**
1. 启动程序: `python main_web_functional.py`
2. 点击 "📁 配置文件管理" → "SCD文件解析"
3. 选择文件: `large_substation_500points.scd`
4. 解析文件，查看500个数据点信息

#### **第二步: 通信配置**
1. 点击 "🔧 通信配置" → "网关配置"
2. 设置IP: 127.0.0.1，端口: 102
3. 测试连接（需要子站模拟器运行）
4. 保存配置

#### **第三步: 自动对点**
1. 点击 "🎮 遥控验收" → "自动对点"
2. 配置测试参数
3. 开始自动对点测试
4. 查看测试结果和成功率

## 🔍 状态指示说明

### **网络状态指示器**
- 🟢 **在线** (绿色): 连接正常
- 🔴 **离线** (红色): 连接断开
- 🟠 **警告** (橙色): 连接异常
- 🔵 **处理中** (蓝色): 正在连接

### **界面状态反馈**
- ✅ **成功操作**: 绿色提示信息
- ❌ **失败操作**: 红色错误信息
- ⚠️ **警告信息**: 橙色警告提示
- 📊 **进度显示**: 蓝色进度条

## 💡 操作技巧

### **文件操作技巧**
1. **支持的文件格式**
   - SCD文件: IEC 61850配置文件
   - RCD文件: 配置描述文件
   - CSV文件: 逗号分隔值文件

2. **文件大小建议**
   - 小文件 (<10KB): 快速解析
   - 中等文件 (10KB-100KB): 正常解析
   - 大文件 (>100KB): 可能需要等待

### **连接测试技巧**
1. **本地测试**: 使用 127.0.0.1:102
2. **远程测试**: 使用实际IP地址
3. **端口选择**: IEC 61850通常使用102端口

### **对点测试技巧**
1. **测试前准备**: 确保通信连接正常
2. **测试模式选择**: 新手建议使用"自动对点"
3. **结果分析**: 重点关注失败的信号点

## 🚨 常见问题解决

### **文件解析失败**
- **问题**: 文件格式不支持
- **解决**: 检查文件扩展名，确保是SCD/RCD/CSV格式

### **连接测试失败**
- **问题**: 无法连接到指定地址
- **解决**: 
  1. 检查IP地址和端口是否正确
  2. 确保子站模拟器正在运行
  3. 检查防火墙设置

### **对点测试异常**
- **问题**: 测试过程中出现错误
- **解决**:
  1. 确保配置文件已正确解析
  2. 验证通信连接正常
  3. 检查测试参数设置

## 🎉 功能亮点

### **真实功能实现**
- ✅ **文件解析**: 支持真实SCD/CSV文件解析
- ✅ **网络连接**: 实际TCP连接测试
- ✅ **多线程处理**: 异步操作，界面不卡顿
- ✅ **进度反馈**: 实时显示操作进度
- ✅ **错误处理**: 完善的异常处理机制

### **用户体验优化**
- 🎨 **现代化界面**: Material Design风格
- 🔄 **实时更新**: 状态指示器动态变化
- 📊 **数据可视化**: 表格、进度条、状态图标
- 💾 **配置保存**: 自动保存用户配置
- 🎯 **操作引导**: 清晰的操作流程

## 📞 技术支持

### **日志查看**
- 程序运行日志显示在控制台
- 操作结果通过弹窗提示
- 状态信息实时更新

### **配置文件**
- 通信配置: `communication_config.json`
- 程序设置: 界面内直接配置

### **扩展功能**
- 支持集成现有业务逻辑模块
- 可扩展新的功能页面
- 模块化设计便于维护

---

**🎯 总结**: 这是一个功能完整、可实际操作的Web风格对点机界面，支持真实的文件解析、网络连接测试和自动对点功能。用户可以通过直观的界面进行所有必要的对点机操作。
