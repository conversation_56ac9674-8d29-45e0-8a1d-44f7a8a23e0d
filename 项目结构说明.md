# Auto_Point 自动对点系统 - 项目结构说明

## 📁 项目目录结构

```
auto_point/
├── README.md                    # 项目说明文档
├── requirements.txt             # Python依赖包列表
├── 项目结构说明.md              # 本文档
├── 界面优化对比.md              # 界面优化效果对比报告
├── 文件清理分析报告.md          # 文件清理过程记录
│
├── 📱 GUI版本 (推荐日常使用)
│   ├── main_fixed.py           # 原始版GUI对点机 (功能完整)
│   ├── main_optimized.py       # 优化版GUI对点机 ⭐ 推荐
│   ├── substation_simulator.py # 原始版子站模拟器 (功能完整)
│   └── substation_optimized.py # 优化版子站模拟器 ⭐ 推荐
│
├── 💻 命令行版本 (自动化友好)
│   ├── auto_checker_lite.py    # 命令行版对点机
│   └── substation_simulator_lite.py # 命令行版子站模拟器
│
├── 🔧 核心模块
│   ├── logic.py                # 对点检查核心逻辑
│   ├── core_iec61850.py        # IEC 61850协议实现
│   ├── config_parser.py        # 配置文件解析器
│   └── mapping.py              # 数据点映射逻辑
│
├── 🧪 测试工具
│   └── test_104_protocol.py    # DL/T 634.5104规约专用测试工具
│
└── 📊 测试数据
    ├── test_points_fixed.csv   # IEC 61850规约测试点表 (104个数据点)
    └── test_points_104.csv     # DL/T 634.5104规约测试点表 (78个数据点)
```

## 🎯 版本选择指南

### 🌟 **推荐组合 (日常使用)**
- **对点机**: `main_optimized.py` 
- **子站模拟器**: `substation_optimized.py`
- **特点**: 界面简洁、操作流畅、功能完整

### 🔧 **完整功能组合**
- **对点机**: `main_fixed.py`
- **子站模拟器**: `substation_simulator.py` 
- **特点**: 功能最全面、配置选项丰富

### ⚡ **自动化组合**
- **对点机**: `auto_checker_lite.py`
- **子站模拟器**: `substation_simulator_lite.py`
- **特点**: 命令行操作、脚本友好、CI/CD集成

## 📋 功能特性对比

| 特性 | 优化版GUI | 原始版GUI | 命令行版 |
|------|-----------|-----------|----------|
| **界面复杂度** | 简洁 | 复杂 | 无界面 |
| **操作步骤** | 4步 | 8步 | 1条命令 |
| **协议支持** | IEC 61850 + 104 | IEC 61850 + 104 | IEC 61850 + 104 |
| **报告导出** | Excel/CSV | 多种格式 | CSV |
| **自动化支持** | 中等 | 低 | 高 |
| **学习成本** | 低 | 高 | 中等 |
| **适用场景** | 日常测试 | 复杂需求 | 批量处理 |

## 🚀 快速开始

### 1. 环境准备
```bash
# 安装依赖
pip install -r requirements.txt

# 或使用conda
conda install pandas openpyxl tkinter
```

### 2. 启动系统 (推荐方式)
```bash
# 启动优化版子站模拟器
python substation_optimized.py

# 启动优化版对点机
python main_optimized.py
```

### 3. 操作流程
1. **子站模拟器**: 选择协议 → 加载点表 → 启动服务器
2. **对点机**: 选择协议 → 选择点表 → 连接测试 → 开始对点
3. **查看结果**: 实时显示 → 导出报告

## 📊 支持的通信协议

### 🔌 **IEC 61850** (智能变电站标准)
- **端口**: 102
- **测试点表**: `test_points_fixed.csv` (104个数据点)
- **应用**: 智能变电站、数字化变电站

### 🔌 **DL/T 634.5104** (电力远动标准)
- **端口**: 102  
- **测试点表**: `test_points_104.csv` (78个数据点)
- **应用**: 传统变电站、远动通信

## 📈 测试数据说明

### IEC 61850测试点表 (test_points_fixed.csv)
- **总数**: 104个数据点
- **遥测**: 85个 (电压、电流、功率等)
- **遥信**: 11个 (断路器状态、告警等)
- **遥控**: 8个 (设备控制命令)

### 104规约测试点表 (test_points_104.csv)  
- **总数**: 78个数据点
- **遥测**: 50个 (变电站电气参数)
- **遥信**: 20个 (设备状态信息)
- **遥控**: 8个 (设备控制命令)

## 🎯 使用场景

### 🏭 **变电站调试**
- 新建变电站设备调试
- 改造项目设备验证
- 定期维护检查

### 🧪 **系统测试**
- 通信协议验证
- 数据传输测试
- 性能压力测试

### 📚 **培训教学**
- 变电站自动化培训
- 通信协议教学
- 操作技能训练

## 📞 技术支持

- **项目文档**: 查看 `README.md`
- **界面对比**: 查看 `界面优化对比.md`
- **清理记录**: 查看 `文件清理分析报告.md`

## 🔄 版本历史

- **v3.0** - 添加104规约支持，界面优化
- **v2.0** - 修复遥控点问题，优化性能
- **v1.0** - 基础功能实现

---

**项目已完成文件清理，从70个文件精简到18个核心文件，减少约74%的冗余文件。**
**系统现已具备在实际变电站环境中使用的完整能力！** 🎉
