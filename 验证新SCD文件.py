#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证新编制的SCD文件
检查SCD文件的格式、结构和内容
"""

import xml.etree.ElementTree as ET
import os
from datetime import datetime

def validate_scd_file(filename):
    """验证SCD文件"""
    print(f"🔍 验证SCD文件: {filename}")
    print("-" * 60)
    
    if not os.path.exists(filename):
        print(f"   ❌ 文件不存在: {filename}")
        return False
    
    file_size = os.path.getsize(filename) / 1024  # KB
    print(f"   📏 文件大小: {file_size:.1f}KB")
    
    try:
        # 解析XML文件
        tree = ET.parse(filename)
        root = tree.getroot()
        
        print(f"   ✅ XML格式验证通过")
        print(f"   📋 根元素: {root.tag}")
        
        # 检查命名空间
        if 'http://www.iec.ch/61850/2003/SCL' in root.tag:
            print(f"   ✅ IEC 61850命名空间正确")
        else:
            print(f"   ⚠️ 命名空间可能不正确")
        
        return analyze_scd_content(root)
        
    except ET.ParseError as e:
        print(f"   ❌ XML解析失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 文件验证失败: {e}")
        return False

def analyze_scd_content(root):
    """分析SCD文件内容"""
    print(f"\n📊 SCD文件内容分析:")
    print("-" * 60)
    
    # 定义命名空间
    ns = {'scl': 'http://www.iec.ch/61850/2003/SCL'}
    
    # 检查Header
    header = root.find('scl:Header', ns)
    if header is not None:
        print(f"   ✅ Header信息:")
        print(f"      ID: {header.get('id', 'N/A')}")
        print(f"      版本: {header.get('version', 'N/A')}")
        print(f"      修订: {header.get('revision', 'N/A')}")
        print(f"      工具: {header.get('toolID', 'N/A')}")
        
        text_elem = header.find('scl:Text', ns)
        if text_elem is not None:
            print(f"      描述: {text_elem.text}")
    else:
        print(f"   ⚠️ 缺少Header信息")
    
    # 检查Substation
    substations = root.findall('scl:Substation', ns)
    print(f"\n   🏭 变电站信息:")
    print(f"      变电站数量: {len(substations)}")
    
    for substation in substations:
        name = substation.get('name', 'Unknown')
        desc = substation.get('desc', 'No description')
        print(f"      名称: {name}")
        print(f"      描述: {desc}")
        
        # 检查电压等级
        voltage_levels = substation.findall('scl:VoltageLevel', ns)
        print(f"      电压等级数: {len(voltage_levels)}")
        
        for vl in voltage_levels:
            vl_name = vl.get('name', 'Unknown')
            voltage = vl.find('scl:Voltage', ns)
            voltage_value = voltage.text if voltage is not None else 'Unknown'
            voltage_unit = voltage.get('unit', '') if voltage is not None else ''
            voltage_mult = voltage.get('multiplier', '') if voltage is not None else ''
            
            print(f"         - {vl_name}: {voltage_value}{voltage_mult}{voltage_unit}")
            
            # 检查间隔
            bays = vl.findall('scl:Bay', ns)
            print(f"           间隔数: {len(bays)}")
            
            for bay in bays:
                bay_name = bay.get('name', 'Unknown')
                equipment = bay.findall('scl:ConductingEquipment', ns)
                print(f"             - {bay_name}: {len(equipment)}个设备")
    
    # 检查IED
    ieds = root.findall('scl:IED', ns)
    print(f"\n   🔧 IED设备信息:")
    print(f"      IED数量: {len(ieds)}")
    
    total_logical_nodes = 0
    total_data_objects = 0
    
    for ied in ieds:
        name = ied.get('name', 'Unknown')
        manufacturer = ied.get('manufacturer', 'Unknown')
        ied_type = ied.get('type', 'Unknown')
        print(f"      - {name} ({manufacturer}, {ied_type})")
        
        # 检查逻辑设备
        ldevices = ied.findall('.//scl:LDevice', ns)
        for ldevice in ldevices:
            ld_inst = ldevice.get('inst', 'Unknown')
            
            # 检查逻辑节点
            lnodes = ldevice.findall('scl:LN', ns) + ldevice.findall('scl:LN0', ns)
            total_logical_nodes += len(lnodes)
            
            for lnode in lnodes:
                ln_class = lnode.get('lnClass', 'Unknown')
                ln_inst = lnode.get('inst', '')
                
                # 检查数据对象
                dois = lnode.findall('scl:DOI', ns)
                total_data_objects += len(dois)
                
                if len(dois) > 0:
                    print(f"         {ld_inst}.{ln_class}{ln_inst}: {len(dois)}个数据对象")
    
    print(f"      总逻辑节点数: {total_logical_nodes}")
    print(f"      总数据对象数: {total_data_objects}")
    
    # 检查通信配置
    communication = root.find('scl:Communication', ns)
    if communication is not None:
        print(f"\n   📡 通信配置:")
        subnets = communication.findall('scl:SubNetwork', ns)
        print(f"      子网数量: {len(subnets)}")
        
        for subnet in subnets:
            subnet_name = subnet.get('name', 'Unknown')
            subnet_type = subnet.get('type', 'Unknown')
            print(f"      - {subnet_name} ({subnet_type})")
            
            connected_aps = subnet.findall('scl:ConnectedAP', ns)
            print(f"        连接的访问点: {len(connected_aps)}")
            
            for cap in connected_aps:
                ied_name = cap.get('iedName', 'Unknown')
                addresses = cap.findall('.//scl:P', ns)
                ip_address = None
                for addr in addresses:
                    if addr.get('type') == 'IP':
                        ip_address = addr.text
                        break
                print(f"          {ied_name}: {ip_address or 'No IP'}")
    else:
        print(f"\n   ⚠️ 缺少通信配置")
    
    # 检查数据类型模板
    data_type_templates = root.find('scl:DataTypeTemplates', ns)
    if data_type_templates is not None:
        print(f"\n   📋 数据类型模板:")
        
        lnode_types = data_type_templates.findall('scl:LNodeType', ns)
        do_types = data_type_templates.findall('scl:DOType', ns)
        da_types = data_type_templates.findall('scl:DAType', ns)
        enum_types = data_type_templates.findall('scl:EnumType', ns)
        
        print(f"      逻辑节点类型: {len(lnode_types)}")
        print(f"      数据对象类型: {len(do_types)}")
        print(f"      数据属性类型: {len(da_types)}")
        print(f"      枚举类型: {len(enum_types)}")
        
        # 显示一些具体类型
        for lnt in lnode_types[:3]:  # 只显示前3个
            lnt_id = lnt.get('id', 'Unknown')
            lnt_class = lnt.get('lnClass', 'Unknown')
            dos = lnt.findall('scl:DO', ns)
            print(f"         {lnt_class} ({lnt_id}): {len(dos)}个数据对象")
    else:
        print(f"\n   ⚠️ 缺少数据类型模板")
    
    return True

def generate_summary_report(filename):
    """生成SCD文件摘要报告"""
    print(f"\n📄 生成摘要报告")
    print("=" * 80)
    
    try:
        tree = ET.parse(filename)
        root = tree.getroot()
        ns = {'scl': 'http://www.iec.ch/61850/2003/SCL'}
        
        # 统计信息
        substations = len(root.findall('scl:Substation', ns))
        voltage_levels = len(root.findall('.//scl:VoltageLevel', ns))
        bays = len(root.findall('.//scl:Bay', ns))
        equipment = len(root.findall('.//scl:ConductingEquipment', ns))
        ieds = len(root.findall('scl:IED', ns))
        logical_devices = len(root.findall('.//scl:LDevice', ns))
        logical_nodes = len(root.findall('.//scl:LN', ns)) + len(root.findall('.//scl:LN0', ns))
        data_objects = len(root.findall('.//scl:DOI', ns))
        
        # 生成报告
        report = f"""
SCD文件摘要报告
================================================================================
文件名称: {filename}
生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
文件大小: {os.path.getsize(filename) / 1024:.1f}KB

变电站结构:
   变电站数量: {substations}
   电压等级数: {voltage_levels}
   间隔数量: {bays}
   设备数量: {equipment}

IED配置:
   IED设备数: {ieds}
   逻辑设备数: {logical_devices}
   逻辑节点数: {logical_nodes}
   数据对象数: {data_objects}

文件状态: ✅ 格式正确，结构完整
适用场景: Auto_Point对点测试
标准兼容: IEC 61850
================================================================================
        """
        
        # 保存报告
        report_file = f"SCD_Report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(report)
        print(f"📄 详细报告已保存: {report_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 新SCD文件验证工具")
    print("=" * 80)
    print(f"🕐 验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    filename = "new_substation_config.scd"
    
    # 验证文件
    if validate_scd_file(filename):
        print(f"\n✅ SCD文件验证通过")
        
        # 生成摘要报告
        if generate_summary_report(filename):
            print(f"\n🎉 新SCD文件编制完成并验证通过!")
            print(f"📁 文件名: {filename}")
            print(f"💡 使用方法:")
            print(f"   1. 在Auto_Point对点机中加载此SCD文件")
            print(f"   2. 解析文件获取变电站配置")
            print(f"   3. 转换为点表进行对点测试")
            
            return True
        else:
            print(f"\n⚠️ 报告生成失败，但文件验证通过")
            return True
    else:
        print(f"\n❌ SCD文件验证失败")
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✅ SCD文件编制和验证完成")
    else:
        print(f"\n❌ SCD文件编制或验证过程中出现问题")
