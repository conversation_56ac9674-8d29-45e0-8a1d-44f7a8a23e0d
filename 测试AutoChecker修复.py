#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试AutoChecker修复
验证AutoChecker类的初始化和方法调用是否正常
"""

import os
from datetime import datetime

def test_autochecker_import():
    """测试AutoChecker导入"""
    print("🧪 测试AutoChecker导入")
    print("-" * 40)
    
    try:
        from logic import AutoChecker
        print("   ✅ AutoChecker导入成功")
        return True, AutoChecker
    except ImportError as e:
        print(f"   ❌ AutoChecker导入失败: {e}")
        return False, None

def test_autochecker_init(AutoChecker):
    """测试AutoChecker初始化"""
    print("\n🧪 测试AutoChecker初始化")
    print("-" * 40)
    
    # 查找可用的文件
    scd_files = [f for f in os.listdir('.') if f.endswith('.scd')]
    csv_files = [f for f in os.listdir('.') if f.endswith('.csv') and ('point' in f.lower() or 'gui_' in f.lower())]
    
    if not scd_files:
        print("   ⚠️ 未找到SCD文件")
        return False
    
    if not csv_files:
        print("   ⚠️ 未找到点表文件")
        return False
    
    scd_path = scd_files[0]
    point_table_path = csv_files[0]
    
    print(f"   📄 使用SCD文件: {scd_path}")
    print(f"   📋 使用点表文件: {point_table_path}")
    
    try:
        # 测试基本初始化
        network_params = {
            'ip': '127.0.0.1',
            'port': 102,
            'protocol': 'IEC 61850'
        }
        
        checker = AutoChecker(
            scd_path=scd_path,
            point_table_path=point_table_path,
            network_params=network_params
        )
        
        print("   ✅ AutoChecker初始化成功")
        print(f"   📊 映射点数: {len(checker.mapped_points)}")
        print(f"   🔧 协议: {checker.protocol}")
        
        return True, checker
        
    except Exception as e:
        print(f"   ❌ AutoChecker初始化失败: {e}")
        return False, None

def test_autochecker_methods(checker):
    """测试AutoChecker方法"""
    print("\n🧪 测试AutoChecker方法")
    print("-" * 40)
    
    try:
        # 检查是否有run_check方法
        if hasattr(checker, 'run_check'):
            print("   ✅ run_check方法存在")
        else:
            print("   ❌ run_check方法不存在")
            return False
        
        # 检查其他重要属性
        attributes = ['mapped_points', 'network_params', 'protocol', 'adapter']
        for attr in attributes:
            if hasattr(checker, attr):
                print(f"   ✅ {attr}属性存在")
            else:
                print(f"   ❌ {attr}属性不存在")
        
        print(f"   📋 网络参数: {checker.network_params}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 方法测试失败: {e}")
        return False

def test_single_point_checker():
    """测试SinglePointChecker"""
    print("\n🧪 测试SinglePointChecker")
    print("-" * 40)
    
    try:
        from logic import SinglePointChecker
        print("   ✅ SinglePointChecker导入成功")
        
        # 测试初始化
        single_checker = SinglePointChecker(
            ip='127.0.0.1',
            port=102,
            protocol='IEC 61850'
        )
        
        print("   ✅ SinglePointChecker初始化成功")
        print(f"   🌐 IP: {single_checker.ip}")
        print(f"   🔌 端口: {single_checker.port}")
        print(f"   📡 协议: {single_checker.protocol}")
        
        # 检查方法
        methods = ['connect', 'disconnect', 'check_single_point']
        for method in methods:
            if hasattr(single_checker, method):
                print(f"   ✅ {method}方法存在")
            else:
                print(f"   ❌ {method}方法不存在")
        
        return True
        
    except Exception as e:
        print(f"   ❌ SinglePointChecker测试失败: {e}")
        return False

def generate_test_report(results):
    """生成测试报告"""
    print("\n📄 生成测试报告")
    print("=" * 60)
    
    success_count = sum(results)
    total_tests = len(results)
    
    report_content = f"""
AutoChecker修复测试报告
================================================================================
测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
测试目的: 验证AutoChecker类的修复是否成功

测试结果:
   总测试项: {total_tests}
   通过测试: {success_count}
   失败测试: {total_tests - success_count}
   通过率: {success_count/total_tests*100:.1f}%

测试详情:
   1. AutoChecker导入: {'✅ 通过' if results[0] else '❌ 失败'}
   2. AutoChecker初始化: {'✅ 通过' if results[1] else '❌ 失败'}
   3. AutoChecker方法: {'✅ 通过' if results[2] else '❌ 失败'}
   4. SinglePointChecker: {'✅ 通过' if results[3] else '❌ 失败'}

修复状态: {'✅ 修复成功' if success_count == total_tests else '⚠️ 部分修复'}

建议:
{'- 所有测试通过，可以正常使用对点功能' if success_count == total_tests else '- 部分测试失败，需要进一步检查'}
- 在Web界面中重新尝试对点测试
- 确保文件路径正确传递
- 验证网络连接参数

注意事项:
- AutoChecker需要SCD文件和点表文件
- 网络参数必须正确配置
- 确保子站服务正在运行
================================================================================
    """
    
    # 保存报告
    report_file = f"AutoChecker修复测试报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(report_content)
    print(f"📄 详细报告已保存: {report_file}")
    
    return success_count == total_tests

def main():
    """主函数"""
    print("🎯 AutoChecker修复测试")
    print("=" * 60)
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # 1. 测试导入
    success, AutoChecker = test_autochecker_import()
    results.append(success)
    
    if not success:
        print("\n❌ AutoChecker导入失败，无法继续测试")
        return False
    
    # 2. 测试初始化
    success, checker = test_autochecker_init(AutoChecker)
    results.append(success)
    
    if not success:
        print("\n❌ AutoChecker初始化失败，无法继续测试")
        results.extend([False, False])  # 剩余测试标记为失败
        generate_test_report(results)
        return False
    
    # 3. 测试方法
    success = test_autochecker_methods(checker)
    results.append(success)
    
    # 4. 测试SinglePointChecker
    success = test_single_point_checker()
    results.append(success)
    
    # 5. 生成报告
    overall_success = generate_test_report(results)
    
    print(f"\n🎉 测试完成总结:")
    if overall_success:
        print("   ✅ 所有测试通过，AutoChecker修复成功！")
        print("   🎯 现在可以在Web界面中正常使用对点功能")
        print("   💡 建议:")
        print("      1. 在对点机中加载SCD文件或点表文件")
        print("      2. 配置正确的网络连接参数")
        print("      3. 重新尝试自动对点测试")
    else:
        print("   ⚠️ 部分测试失败，需要进一步检查")
        print("   🔧 建议检查:")
        print("      1. 文件路径是否正确")
        print("      2. 依赖模块是否完整")
        print("      3. 网络配置是否正确")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✅ AutoChecker修复验证成功！")
    else:
        print(f"\n❌ AutoChecker修复验证失败！")
