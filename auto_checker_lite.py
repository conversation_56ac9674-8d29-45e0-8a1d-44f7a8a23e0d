#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精简版自动对点机
只保留核心功能：连接、对点、结果显示、导出
"""

import sys
import socket
import json
import pandas as pd
from datetime import datetime
import time

class AutoCheckerLite:
    """精简版自动对点机"""
    
    def __init__(self):
        self.socket = None
        self.is_connected = False
        self.point_data = []
        self.results = []
        
    def load_point_table(self, file_path):
        """加载点表文件"""
        try:
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            else:
                df = pd.read_excel(file_path)
            
            self.point_data = df.to_dict('records')
            print(f"✓ 成功加载点表，共 {len(self.point_data)} 个数据点")
            return True
            
        except Exception as e:
            print(f"✗ 加载点表失败: {e}")
            return False
    
    def connect(self, host, port):
        """连接到子站"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            self.socket.connect((host, port))
            self.is_connected = True
            print(f"✓ 成功连接到 {host}:{port}")
            return True
            
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.socket:
            try:
                self.socket.close()
            except:
                pass
            self.socket = None
        self.is_connected = False
        print("✓ 连接已断开")
    
    def read_point(self, point_name):
        """读取单个数据点"""
        if not self.is_connected:
            return None
            
        try:
            # 发送读取请求
            request = f"READ {point_name}"
            self.socket.send(request.encode('utf-8'))
            
            # 接收响应
            response = self.socket.recv(4096)
            if response:
                response_data = json.loads(response.decode('utf-8'))
                if response_data.get('status') == 'success':
                    return response_data.get('data', {}).get('value')
            return None
            
        except Exception as e:
            print(f"✗ 读取数据点 {point_name} 失败: {e}")
            return None
    
    def compare_values(self, expected, actual, signal_type):
        """比较期望值和实际值"""
        try:
            if signal_type.strip() in ['遥信', '遥控']:
                # 布尔值比较
                expected_bool = self._convert_to_bool(expected)
                actual_bool = self._convert_to_bool(actual)
                return expected_bool == actual_bool, expected_bool, actual_bool
            else:
                # 数值比较
                expected_float = float(expected)
                actual_float = float(actual)
                # 允许小的误差
                diff = abs(expected_float - actual_float)
                tolerance = max(abs(expected_float) * 0.01, 0.1)  # 1%或0.1的容差
                return diff <= tolerance, expected_float, actual_float

        except Exception as e:
            # 如果数值转换失败，可能是布尔值，再试一次布尔比较
            try:
                expected_bool = self._convert_to_bool(expected)
                actual_bool = self._convert_to_bool(actual)
                return expected_bool == actual_bool, expected_bool, actual_bool
            except:
                print(f"✗ 值比较异常: {e}")
                return False, expected, actual
    
    def _convert_to_bool(self, value):
        """转换值为布尔类型"""
        if isinstance(value, bool):
            return value
        elif isinstance(value, str):
            value_str = value.strip().lower()
            if value_str in ['true', '1', 'on', 'yes']:
                return True
            elif value_str in ['false', '0', 'off', 'no']:
                return False
            else:
                try:
                    return bool(int(float(value)))
                except:
                    return False
        else:
            try:
                return bool(int(float(value)))
            except:
                return False
    
    def start_checking(self):
        """开始对点检查"""
        if not self.is_connected:
            print("✗ 未连接到子站")
            return False
            
        if not self.point_data:
            print("✗ 未加载点表")
            return False
        
        print(f"开始对点检查，共 {len(self.point_data)} 个数据点...")
        print("-" * 80)
        
        self.results = []
        correct_count = 0
        error_count = 0
        
        for i, point in enumerate(self.point_data):
            signal_name = point.get('SignalName', '')
            signal_type = point.get('SignalType', '遥测')
            expected_value = point.get('ExpectedValue', 0)
            ied_name = point.get('IEDName', 'Unknown')
            
            if not signal_name:
                continue
            
            # 显示进度
            progress = (i + 1) / len(self.point_data) * 100
            print(f"进度: {progress:.1f}% - 检查 {signal_name}...", end=' ')
            
            # 读取实际值
            actual_value = self.read_point(signal_name)
            
            if actual_value is None:
                result = "读取失败"
                print("✗ 读取失败")
                error_count += 1
            else:
                # 比较值
                is_match, exp_val, act_val = self.compare_values(expected_value, actual_value, signal_type)
                
                if is_match:
                    result = "正确"
                    print("✓ 正确")
                    correct_count += 1
                else:
                    result = "错误"
                    print(f"✗ 错误 (期望:{exp_val}, 实际:{act_val})")
                    error_count += 1
            
            # 保存结果
            self.results.append({
                'IEDName': ied_name,
                'SignalName': signal_name,
                'SignalType': signal_type,
                'ExpectedValue': expected_value,
                'ActualValue': actual_value,
                'CheckResult': result,
                'Timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            })
            
            # 短暂延时，避免过快请求
            time.sleep(0.1)
        
        print("-" * 80)
        print(f"对点检查完成！")
        print(f"总计: {len(self.point_data)} 个数据点")
        print(f"正确: {correct_count} 个")
        print(f"错误: {error_count} 个")
        print(f"正确率: {correct_count/len(self.point_data)*100:.1f}%")
        
        return True
    
    def export_results(self, output_file):
        """导出结果到文件"""
        if not self.results:
            print("✗ 没有结果可导出")
            return False

        try:
            df = pd.DataFrame(self.results)

            # 尝试导出Excel，如果失败则导出CSV
            if output_file.endswith('.xlsx'):
                try:
                    df.to_excel(output_file, index=False)
                    print(f"✓ 结果已导出到: {output_file}")
                    return True
                except ImportError:
                    # 如果没有openpyxl，改为CSV格式
                    csv_file = output_file.replace('.xlsx', '.csv')
                    df.to_csv(csv_file, index=False, encoding='utf-8-sig')
                    print(f"✓ 结果已导出到: {csv_file} (CSV格式)")
                    return True
            else:
                df.to_csv(output_file, index=False, encoding='utf-8-sig')
                print(f"✓ 结果已导出到: {output_file}")
                return True

        except Exception as e:
            print(f"✗ 导出失败: {e}")
            return False
    
    def show_error_summary(self):
        """显示错误汇总"""
        if not self.results:
            print("✗ 没有结果数据")
            return
            
        error_results = [r for r in self.results if r['CheckResult'] == '错误']
        
        if not error_results:
            print("✓ 没有错误数据点")
            return
        
        print(f"\n错误数据点汇总 (共 {len(error_results)} 个):")
        print("-" * 100)
        print(f"{'IED名称':<15} {'信号名称':<25} {'信号类型':<8} {'期望值':<15} {'实际值':<15}")
        print("-" * 100)
        
        for result in error_results:
            print(f"{result['IEDName']:<15} {result['SignalName']:<25} {result['SignalType']:<8} "
                  f"{result['ExpectedValue']:<15} {result['ActualValue']:<15}")

def main():
    """主函数"""
    if len(sys.argv) < 4:
        print("使用方法: python auto_checker_lite.py <点表文件> <子站IP> <子站端口> [输出文件]")
        print("示例: python auto_checker_lite.py test_points_fixed.csv 127.0.0.1 102 result.xlsx")
        return
    
    point_table_path = sys.argv[1]
    host = sys.argv[2]
    port = int(sys.argv[3])
    output_file = sys.argv[4] if len(sys.argv) > 4 else f"check_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
    
    # 创建对点机实例
    checker = AutoCheckerLite()
    
    try:
        # 加载点表
        if not checker.load_point_table(point_table_path):
            return
        
        # 连接子站
        if not checker.connect(host, port):
            return
        
        # 开始对点
        if checker.start_checking():
            # 显示错误汇总
            checker.show_error_summary()
            
            # 导出结果
            checker.export_results(output_file)
        
    except KeyboardInterrupt:
        print("\n收到停止信号...")
    finally:
        # 断开连接
        checker.disconnect()

if __name__ == "__main__":
    main()
