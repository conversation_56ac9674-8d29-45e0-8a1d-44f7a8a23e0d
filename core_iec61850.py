import ctypes
import os
import socket
import json
import time
from typing import Callable, Any

class IEC61850Adapter:
    def __init__(self, dll_path=None, protocol='IEC 61850'):
        self.protocol = protocol
        self.connection = None
        self.data_callback = None
        self.socket_connection = None
        
        # 如果是IEC 61850协议，尝试加载DLL
        if protocol == 'IEC 61850' and dll_path:
            if os.path.exists(dll_path):
                try:
                    self.lib = ctypes.CDLL(dll_path)
                    print(f"成功加载libiec61850.dll: {dll_path}")
                except Exception as e:
                    print(f"加载libiec61850.dll失败: {e}")
                    self.lib = None
            else:
                print(f"libiec61850.dll未找到: {dll_path}")
                self.lib = None

    def set_data_callback(self, callback: Callable[[Any], None]):
        self.data_callback = callback

    def connect(self, ip: str, port: int = 102, subnet_mask: str = '', gateway: str = ''):
        print(f"[调试] connect() 被调用，参数: ip={ip}, port={port}, protocol={self.protocol}")
        try:
            if self.protocol == 'IEC 61850':
                # 尝试使用libiec61850连接
                if hasattr(self, 'lib') and self.lib:
                    print(f"使用libiec61850连接IED: {ip}:{port}")
                    # 这里应该调用实际的libiec61850函数
                    self.connection = True
                else:
                    # 回退到socket连接
                    print(f"使用socket连接IED: {ip}:{port}")
                    self.socket_connection = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    self.socket_connection.settimeout(10)
                    self.socket_connection.connect((ip, port))
                    self.connection = True
            else:
                # DL/T 634.5104协议
                print(f"使用DL/T 634.5104连接: {ip}:{port}")
                self.socket_connection = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.socket_connection.settimeout(10)
                self.socket_connection.connect((ip, port))
                self.connection = True
            print(f"[调试] 连接成功，self.connection={self.connection}, self.socket_connection={self.socket_connection}")
            print(f"连接成功 - 协议: {self.protocol}  IP: {ip}  掩码: {subnet_mask}  网关: {gateway}  端口: {port}")
            return True
        except Exception as e:
            print(f"[调试] 连接异常: {e}")
            self.connection = None
            if self.socket_connection:
                try:
                    self.socket_connection.close()
                except:
                    pass
                self.socket_connection = None
            return False

    def read_data(self, variable: str):
        """读取遥测/遥信数据"""
        try:
            print(f"[调试] read_data() 被调用，variable={variable}, connection={self.connection}, socket_connection={self.socket_connection}")
            if not self.connection:
                print(f"[调试] 未连接IED，无法读取 {variable}")
                raise RuntimeError("未连接IED")
            if self.socket_connection:
                # 使用socket通信
                request = f"READ {variable}"
                print(f"[调试] 发送请求: {request}")
                self.socket_connection.send(request.encode('utf-8'))
                # 接收响应
                response = self.socket_connection.recv(1024).decode('utf-8')
                print(f"[调试] 收到原始响应: {response}")
                try:
                    # 尝试解析JSON响应
                    data = json.loads(response)
                    # 修正：从data['data']['value']获取实际值
                    value = data.get('data', {}).get('value', None)
                    print(f"[调试] 解析JSON响应: {data}, 采集值: {value}")
                    if self.data_callback:
                        self.data_callback({"variable": variable, "value": value, "response": data})
                    return value
                except json.JSONDecodeError:
                    print(f"[调试] 响应不是JSON，直接返回: {response}")
                    return response
            else:
                # 使用libiec61850
                if hasattr(self, 'lib') and self.lib:
                    value = 123.45  # 模拟值
                    print(f"[调试] 读取变量 {variable}: {value}")
                    if self.data_callback:
                        self.data_callback({"variable": variable, "value": value})
                    return value
                else:
                    print(f"[调试] 无可用连接，无法读取 {variable}")
                    raise RuntimeError("无可用连接")
        except Exception as e:
            print(f"[调试] 读取数据失败: {e}")
            return None

    def send_command(self, variable: str, value: Any):
        """下发遥控命令"""
        try:
            if not self.connection:
                raise RuntimeError("未连接IED")
                
            if self.socket_connection:
                # 使用socket通信
                request = f"WRITE {variable}={value}"
                self.socket_connection.send(request.encode('utf-8'))
                
                # 接收响应
                response = self.socket_connection.recv(1024).decode('utf-8')
                print(f"下发命令 {variable} = {value}, 响应: {response}")
                
                try:
                    data = json.loads(response)
                    return data.get('status') == 'success'
                except json.JSONDecodeError:
                    return 'success' in response.lower()
            else:
                # 使用libiec61850
                if hasattr(self, 'lib') and self.lib:
                    print(f"下发命令 {variable} = {value}")
                    return True
                else:
                    raise RuntimeError("无可用连接")
                    
        except Exception as e:
            print(f"下发命令失败: {e}")
            return False

    def scan_all_points(self):
        """扫描所有数据点"""
        try:
            if not self.connection:
                raise RuntimeError("未连接IED")
                
            if self.socket_connection:
                request = "SCAN"
                self.socket_connection.send(request.encode('utf-8'))
                
                response = self.socket_connection.recv(4096).decode('utf-8')
                print(f"扫描响应: {response}")
                
                try:
                    return json.loads(response)
                except json.JSONDecodeError:
                    return {"error": "Invalid response format"}
            else:
                return {"error": "No socket connection"}
                
        except Exception as e:
            print(f"扫描失败: {e}")
            return {"error": str(e)}

    def disconnect(self):
        """断开连接"""
        try:
            if self.socket_connection:
                self.socket_connection.close()
                self.socket_connection = None
            self.connection = None
            print("连接已断开")
        except Exception as e:
            print(f"断开连接异常: {e}")

    def __del__(self):
        self.disconnect() 