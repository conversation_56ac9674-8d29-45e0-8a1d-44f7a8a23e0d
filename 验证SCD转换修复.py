#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证SCD转换修复
测试修复后的SCD转换功能是否正常工作
"""

import os
from datetime import datetime
from scd_to_point_converter import SCDToPointConverter

def test_scd_conversion():
    """测试SCD转换功能"""
    print("🧪 验证SCD转换修复")
    print("=" * 60)
    print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试文件
    test_file = "large_substation_2000points.scd"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return False
    
    try:
        print(f"📁 测试文件: {test_file}")
        file_size = os.path.getsize(test_file) / (1024 * 1024)  # MB
        print(f"📏 文件大小: {file_size:.1f}MB")
        
        # 创建转换器
        print(f"\n🔄 创建SCD转换器...")
        converter = SCDToPointConverter()
        
        # 解析SCD文件
        print(f"🔄 解析SCD文件...")
        result = converter.parse_scd_file(test_file)
        
        if result:
            points_count = len(converter.signal_points)
            print(f"✅ SCD文件解析成功!")
            print(f"📊 解析结果:")
            print(f"   🏭 IED设备数量: {result.get('total_ieds', 0)}")
            print(f"   📋 信号点数量: {points_count}")
            
            if points_count >= 1800:  # 期望至少1800个数据点
                print(f"✅ 数据点数量正常 (>= 1800)")
                
                # 统计信号类型
                signal_types = {}
                for point in converter.signal_points:
                    signal_type = point.get('signal_type', 'Unknown')
                    signal_types[signal_type] = signal_types.get(signal_type, 0) + 1
                
                print(f"📈 信号类型分布:")
                for signal_type, count in signal_types.items():
                    type_name = {'DI': '遥信', 'AI': '遥测', 'DO': '遥控', 'AO': '遥调'}.get(signal_type, signal_type)
                    print(f"   {type_name}({signal_type}): {count}个")
                
                # 转换为点表
                print(f"\n🔄 转换为点表...")
                csv_file = converter.convert_to_point_table('csv')
                
                if os.path.exists(csv_file):
                    # 检查CSV文件
                    with open(csv_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        csv_points = len(lines) - 1  # 减去标题行
                    
                    csv_size = os.path.getsize(csv_file) / 1024  # KB
                    
                    print(f"✅ 点表转换成功!")
                    print(f"📄 输出文件: {csv_file}")
                    print(f"📏 文件大小: {csv_size:.1f}KB")
                    print(f"📋 CSV行数: {len(lines)}行 (含标题)")
                    print(f"📊 数据点数: {csv_points}个")
                    
                    if csv_points == points_count:
                        print(f"✅ 数据完整性验证通过!")
                        
                        # 显示前几行内容
                        print(f"\n📝 CSV文件前5行:")
                        for i, line in enumerate(lines[:5]):
                            print(f"   {i+1}: {line.strip()}")
                        
                        return True
                    else:
                        print(f"❌ 数据完整性验证失败: 解析{points_count}个，CSV{csv_points}个")
                        return False
                else:
                    print(f"❌ 点表文件生成失败")
                    return False
            else:
                print(f"❌ 数据点数量不足: {points_count} < 1800")
                return False
        else:
            print(f"❌ SCD文件解析失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎯 SCD转换修复验证")
    print("=" * 80)
    
    success = test_scd_conversion()
    
    print(f"\n{'='*80}")
    if success:
        print(f"🎉 SCD转换修复验证成功!")
        print(f"✅ Web界面中的SCD转换功能现在应该正常工作")
        print(f"💡 建议:")
        print(f"   1. 在Web界面中测试SCD文件上传和解析")
        print(f"   2. 验证转换结果显示2000个数据点")
        print(f"   3. 检查生成的点表文件内容")
        print(f"   4. 确认信号类型分布正确")
    else:
        print(f"❌ SCD转换修复验证失败")
        print(f"💡 建议:")
        print(f"   1. 检查SCD转换器代码")
        print(f"   2. 确认测试文件完整性")
        print(f"   3. 查看详细错误信息")
    
    return success

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n✅ 验证完成 - 修复成功")
    else:
        print(f"\n❌ 验证完成 - 仍有问题")
