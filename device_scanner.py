#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设备扫描器 - 全景信息扫描功能
支持网络设备发现和数据点自动扫描
"""

import socket
import threading
import time
import json
import ipaddress
import pandas as pd
from datetime import datetime
import concurrent.futures

class DeviceScanner:
    """设备扫描器"""
    
    def __init__(self):
        self.discovered_devices = []
        self.scan_results = {}
        
    def ping_host(self, ip, port=102, timeout=1):
        """检查主机是否可达"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((ip, port))
            sock.close()
            return result == 0
        except:
            return False
    
    def scan_network_range(self, ip_range, port=102, max_workers=50):
        """扫描网络范围内的设备"""
        print(f"开始扫描网络范围: {ip_range}:{port}")
        
        try:
            network = ipaddress.IPv4Network(ip_range, strict=False)
            hosts = list(network.hosts())
            
            discovered = []
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有ping任务
                future_to_ip = {
                    executor.submit(self.ping_host, str(ip), port): str(ip) 
                    for ip in hosts
                }
                
                # 收集结果
                for future in concurrent.futures.as_completed(future_to_ip):
                    ip = future_to_ip[future]
                    try:
                        if future.result():
                            discovered.append(ip)
                            print(f"发现设备: {ip}:{port}")
                    except Exception as e:
                        print(f"扫描 {ip} 时出错: {e}")
            
            self.discovered_devices = discovered
            print(f"扫描完成，发现 {len(discovered)} 个设备")
            return discovered
            
        except Exception as e:
            print(f"网络扫描异常: {e}")
            return []
    
    def discover_device_info(self, ip, port=102):
        """发现设备信息"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            sock.connect((ip, port))
            
            # 发送设备信息查询请求
            requests = [
                "INFO",  # 设备信息
                "SCAN",  # 扫描数据点
                "VERSION",  # 版本信息
            ]
            
            device_info = {
                'ip': ip,
                'port': port,
                'timestamp': datetime.now().isoformat(),
                'status': 'online',
                'info': {},
                'data_points': []
            }
            
            for request in requests:
                try:
                    sock.send(request.encode('utf-8'))
                    response = sock.recv(4096).decode('utf-8')
                    
                    try:
                        data = json.loads(response)
                        if request == "INFO":
                            device_info['info'] = data
                        elif request == "SCAN":
                            device_info['data_points'] = data.get('points', [])
                        elif request == "VERSION":
                            device_info['version'] = data
                    except json.JSONDecodeError:
                        device_info[request.lower()] = response
                        
                    time.sleep(0.1)  # 避免请求过快
                    
                except Exception as e:
                    print(f"请求 {request} 失败: {e}")
            
            sock.close()
            return device_info
            
        except Exception as e:
            print(f"发现设备信息失败 {ip}:{port} - {e}")
            return {
                'ip': ip,
                'port': port,
                'timestamp': datetime.now().isoformat(),
                'status': 'error',
                'error': str(e)
            }
    
    def discover_data_points(self, ip, port=102):
        """发现设备的所有数据点"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            sock.connect((ip, port))
            
            # 发送数据点扫描请求
            scan_request = json.dumps({
                "type": "scan",
                "target": "all_points"
            })
            
            sock.send(scan_request.encode('utf-8'))
            response = sock.recv(8192).decode('utf-8')
            sock.close()
            
            try:
                data = json.loads(response)
                if data.get('status') == 'success':
                    return data.get('points', [])
                else:
                    print(f"扫描失败: {data.get('message', 'Unknown error')}")
                    return []
            except json.JSONDecodeError:
                print(f"响应格式错误: {response}")
                return []
                
        except Exception as e:
            print(f"数据点发现失败 {ip}:{port} - {e}")
            return []
    
    def auto_generate_point_table(self, discovered_points, output_path=None):
        """自动生成点表"""
        if not discovered_points:
            print("没有发现数据点")
            return None
            
        # 整理数据点信息
        point_table_data = []
        
        for device_ip, points in discovered_points.items():
            for point in points:
                point_data = {
                    'SignalName': point.get('name', 'Unknown'),
                    'ExpectedValue': self._get_default_value(point.get('type', '遥测')),
                    'IED': point.get('ied', f'IED_{device_ip.replace(".", "_")}'),
                    'DataType': point.get('type', '遥测'),
                    'Description': point.get('description', point.get('name', 'Unknown')),
                    'Unit': point.get('unit', self._get_default_unit(point.get('type', '遥测'))),
                    'Quality': 'good',
                    'DeviceIP': device_ip,
                    'DiscoveryTime': datetime.now().isoformat()
                }
                point_table_data.append(point_data)
        
        # 创建DataFrame
        df = pd.DataFrame(point_table_data)
        
        if output_path:
            df.to_csv(output_path, index=False, encoding='utf-8-sig')
            print(f"自动生成的点表已保存到: {output_path}")
        
        return df
    
    def _get_default_value(self, signal_type):
        """获取信号类型的默认值"""
        if signal_type in ['遥信', '遥控']:
            return 'false'
        else:  # 遥测
            return '0.0'
    
    def _get_default_unit(self, signal_type):
        """获取信号类型的默认单位"""
        if signal_type in ['遥信', '遥控']:
            return '状态'
        else:  # 遥测
            return 'V'
    
    def full_network_scan(self, ip_range, port=102, output_dir="scan_results"):
        """完整的网络扫描流程"""
        print("=== 开始全景网络扫描 ===")
        
        # 1. 扫描网络设备
        devices = self.scan_network_range(ip_range, port)
        
        if not devices:
            print("未发现任何设备")
            return None
        
        # 2. 发现设备信息
        print("\n=== 发现设备详细信息 ===")
        device_details = {}
        
        for device_ip in devices:
            print(f"正在扫描设备: {device_ip}")
            info = self.discover_device_info(device_ip, port)
            device_details[device_ip] = info
        
        # 3. 发现数据点
        print("\n=== 发现数据点信息 ===")
        all_points = {}
        
        for device_ip in devices:
            print(f"正在扫描数据点: {device_ip}")
            points = self.discover_data_points(device_ip, port)
            if points:
                all_points[device_ip] = points
                print(f"设备 {device_ip} 发现 {len(points)} 个数据点")
        
        # 4. 生成报告
        print("\n=== 生成扫描报告 ===")
        
        # 设备信息报告
        device_report_path = f"{output_dir}/device_scan_report.json"
        os.makedirs(output_dir, exist_ok=True)
        
        with open(device_report_path, 'w', encoding='utf-8') as f:
            json.dump(device_details, f, ensure_ascii=False, indent=2)
        print(f"设备扫描报告: {device_report_path}")
        
        # 自动生成点表
        if all_points:
            point_table_path = f"{output_dir}/auto_generated_points.csv"
            df = self.auto_generate_point_table(all_points, point_table_path)
            
            # 生成扫描统计
            stats = {
                'scan_time': datetime.now().isoformat(),
                'ip_range': ip_range,
                'port': port,
                'devices_found': len(devices),
                'devices_with_points': len(all_points),
                'total_points': sum(len(points) for points in all_points.values()),
                'point_types': {}
            }
            
            if df is not None:
                stats['point_types'] = df['DataType'].value_counts().to_dict()
            
            stats_path = f"{output_dir}/scan_statistics.json"
            with open(stats_path, 'w', encoding='utf-8') as f:
                json.dump(stats, f, ensure_ascii=False, indent=2)
            print(f"扫描统计: {stats_path}")
        
        print(f"\n✅ 全景扫描完成！")
        print(f"发现设备: {len(devices)} 个")
        print(f"发现数据点: {sum(len(points) for points in all_points.values())} 个")
        
        return {
            'devices': device_details,
            'points': all_points,
            'statistics': stats
        }

def test_device_scanner():
    """测试设备扫描器"""
    scanner = DeviceScanner()
    
    # 测试本地网络扫描
    print("测试本地设备扫描...")
    
    # 扫描本地回环地址
    devices = scanner.scan_network_range("127.0.0.1/32", 102)
    
    if devices:
        print(f"发现设备: {devices}")
        
        # 测试设备信息发现
        for device in devices:
            info = scanner.discover_device_info(device, 102)
            print(f"设备信息: {info}")
    else:
        print("未发现设备，请确保子站模拟器正在运行")

if __name__ == "__main__":
    import os
    test_device_scanner()
