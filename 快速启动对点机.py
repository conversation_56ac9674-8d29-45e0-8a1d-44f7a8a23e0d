#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动对点机
简化版启动脚本
"""

import sys
import os
from datetime import datetime

def quick_start():
    """快速启动"""
    print("🚀 Auto_Point 对点机快速启动")
    print("=" * 60)
    print(f"🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查环境
    print(f"🐍 Python版本: {sys.version}")
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 检查关键文件
    key_files = [
        "main_web_functional.py",
        "scd_converter.py", 
        "logic.py",
        "220kVQFB_recovered.scd",
        "point_table_regex_20250705_142137.csv"
    ]
    
    print("\n📋 检查关键文件:")
    missing_files = []
    for file in key_files:
        if os.path.exists(file):
            size = os.path.getsize(file)
            if size > 1024*1024:
                print(f"   ✅ {file} ({size/1024/1024:.1f}MB)")
            else:
                print(f"   ✅ {file} ({size/1024:.1f}KB)")
        else:
            print(f"   ❌ {file} (缺失)")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠️ 缺少关键文件: {missing_files}")
        print("💡 请确保所有文件都在当前目录中")
        return False
    
    # 检查依赖
    print("\n📦 检查依赖模块:")
    required_modules = ['PySide6', 'pandas', 'xml.etree.ElementTree']
    
    for module in required_modules:
        try:
            if module == 'xml.etree.ElementTree':
                import xml.etree.ElementTree
                print(f"   ✅ {module}")
            else:
                __import__(module)
                print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module} (未安装)")
            return False
    
    # 启动主程序
    print("\n🌐 启动Web服务器...")
    try:
        # 导入主程序
        import main_web_functional
        
        print("✅ 主程序模块导入成功")
        print("🌐 Web服务器启动中...")
        print("💡 启动成功后请访问: http://localhost:8080")
        print("\n📋 使用说明:")
        print("   1. 浏览器访问 http://localhost:8080")
        print("   2. 进入 '配置文件管理' 模块")
        print("   3. 使用 '同时加载SCD和点表' 功能")
        print("   4. 选择文件:")
        print("      - SCD: 220kVQFB_recovered.scd")
        print("      - 点表: point_table_regex_20250705_142137.csv")
        print("   5. 点击 '同时加载SCD和点表'")
        print("   6. 如有XML警告，选择 '继续加载'")
        
        print("\n🚀 正在启动...")
        
        # 启动主程序
        main_web_functional.main()
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断程序")
        return True
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 错误详情:")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        success = quick_start()
        if success:
            print("\n🎉 程序正常退出")
        else:
            print("\n❌ 程序启动失败")
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
