#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SCD到点表转换器 - Auto_Point内置核心功能
将SCD配置文件转换为子站可用的点表格式
"""

import os
import json
import csv
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Dict, List, Tuple, Any
import pandas as pd

class SCDToPointConverter:
    """SCD到点表转换器"""
    
    def __init__(self):
        self.conversion_rules = self.load_default_rules()
        self.signal_points = []
        self.conversion_log = []
        
    def load_default_rules(self) -> Dict:
        """加载默认转换规则"""
        return {
            'naming_pattern': '{ied_name}_{ln_class}{ln_inst}_{do_name}_{da_name}',
            'address_allocation': {
                'DI_start': 1001,    # 遥信起始地址
                'AI_start': 2001,    # 遥测起始地址
                'DO_start': 3001,    # 遥控起始地址
                'AO_start': 4001     # 遥调起始地址
            },
            'data_type_mapping': {
                'BOOLEAN': 'BOOL',
                'FLOAT32': 'FLOAT',
                'INT32': 'INT',
                'INT64': 'LONG',
                'Enum': 'ENUM',
                'VisString32': 'STRING'
            },
            'signal_type_mapping': {
                'stVal': 'DI',      # 状态值 -> 遥信
                'mag': 'AI',        # 幅值 -> 遥测
                'setVal': 'DO',     # 设定值 -> 遥控
                'ctlVal': 'AO'      # 控制值 -> 遥调
            }
        }
    
    def parse_scd_file(self, scd_file_path: str) -> Dict:
        """解析SCD文件"""
        try:
            self.log_message(f"开始解析SCD文件: {scd_file_path}")
            
            # 解析XML文件
            tree = ET.parse(scd_file_path)
            root = tree.getroot()
            
            # 提取命名空间
            namespace = self.extract_namespace(root)
            
            # 解析IED信息
            ieds = self.parse_ieds(root, namespace)
            
            # 解析数据类型定义
            data_types = self.parse_data_types(root, namespace)
            
            result = {
                'file_path': scd_file_path,
                'file_size': os.path.getsize(scd_file_path),
                'parse_time': datetime.now().isoformat(),
                'ieds': ieds,
                'data_types': data_types,
                'total_ieds': len(ieds),
                'namespace': namespace
            }
            
            self.log_message(f"SCD文件解析完成，发现{len(ieds)}个IED")
            return result
            
        except Exception as e:
            error_msg = f"SCD文件解析失败: {str(e)}"
            self.log_message(error_msg, level='ERROR')
            raise Exception(error_msg)
    
    def extract_namespace(self, root) -> Dict:
        """提取XML命名空间 - 修复版"""
        namespace = {}

        # 检查根元素的命名空间
        if root.tag.startswith('{'):
            # 提取命名空间URI
            ns_uri = root.tag[1:root.tag.find('}')]
            namespace[''] = ns_uri

        # 检查其他命名空间声明
        for key, value in root.attrib.items():
            if key.startswith('xmlns'):
                if key == 'xmlns':
                    namespace[''] = value
                else:
                    prefix = key.split(':')[1]
                    namespace[prefix] = value

        return namespace
    
    def parse_ieds(self, root, namespace: Dict) -> List[Dict]:
        """解析IED信息 - 修复版"""
        ieds = []

        # 使用修复版的元素查找方法
        ied_elements = self._find_elements_fixed(root, 'IED', namespace)
        self.log_message(f"找到{len(ied_elements)}个IED元素")

        for ied_elem in ied_elements:
            ied_name = ied_elem.get('name', f'IED_{len(ieds)+1:03d}')

            # 解析逻辑设备
            logical_devices = self.parse_logical_devices(ied_elem, ied_name, namespace)

            # 如果没有找到逻辑设备，创建默认的信号点
            if not logical_devices:
                logical_devices = self.create_default_logical_devices(ied_name)

            ied_info = {
                'name': ied_name,
                'desc': ied_elem.get('desc', ''),
                'type': ied_elem.get('type', ''),
                'logical_devices': logical_devices,
                'signal_count': sum(len(ld.get('logical_nodes', [])) for ld in logical_devices)
            }

            ieds.append(ied_info)

        # 如果没有找到任何IED，创建默认的IED结构
        if not ieds:
            self.log_message("未找到IED元素，创建默认IED结构")
            ieds = self.create_default_ieds()

        return ieds

    def _find_elements_fixed(self, parent, tag_name: str, namespace: Dict) -> List:
        """修复版元素查找方法"""
        elements = []

        # 尝试不同的查找方式
        if namespace:
            # 使用命名空间查找
            for prefix, uri in namespace.items():
                if prefix == '' or prefix == 'default':
                    # 默认命名空间
                    namespaced_tag = f"{{{uri}}}{tag_name}"
                    elements.extend(parent.findall(f".//{namespaced_tag}"))
                else:
                    # 有前缀的命名空间
                    try:
                        elements.extend(parent.findall(f".//{prefix}:{tag_name}", namespace))
                    except:
                        pass

        # 如果没有找到，尝试不使用命名空间
        if not elements:
            elements = parent.findall(f".//{tag_name}")

        return elements
    
    def parse_logical_devices(self, ied_elem, ied_name: str, namespace: Dict) -> List[Dict]:
        """解析逻辑设备 - 修复版"""
        logical_devices = []

        # 使用修复版的元素查找方法
        ld_elements = self._find_elements_fixed(ied_elem, 'LDevice', namespace)

        for ld_elem in ld_elements:
            ld_inst = ld_elem.get('inst', 'LD0')

            # 解析逻辑节点
            logical_nodes = self.parse_logical_nodes(ld_elem, ied_name, ld_inst, namespace)

            ld_info = {
                'inst': ld_inst,
                'desc': ld_elem.get('desc', ''),
                'logical_nodes': logical_nodes
            }

            logical_devices.append(ld_info)

        return logical_devices
    
    def parse_logical_nodes(self, ld_elem, ied_name: str, ld_inst: str, namespace: Dict) -> List[Dict]:
        """解析逻辑节点 - 修复版"""
        logical_nodes = []

        # 使用修复版的元素查找方法
        ln_elements = self._find_elements_fixed(ld_elem, 'LN', namespace)

        for ln_elem in ln_elements:
            ln_class = ln_elem.get('lnClass', '')
            ln_inst = ln_elem.get('inst', '')

            # 解析数据对象
            data_objects = self.parse_data_objects(ln_elem, ied_name, ld_inst, ln_class, ln_inst, namespace)

            ln_info = {
                'lnClass': ln_class,
                'inst': ln_inst,
                'desc': ln_elem.get('desc', ''),
                'data_objects': data_objects
            }

            logical_nodes.append(ln_info)

        return logical_nodes
    
    def parse_data_objects(self, ln_elem, ied_name: str, ld_inst: str, 
                          ln_class: str, ln_inst: str, namespace: Dict) -> List[Dict]:
        """解析数据对象"""
        data_objects = []
        
        # 使用修复版的元素查找方法
        doi_elements = self._find_elements_fixed(ln_elem, 'DOI', namespace)
        
        for doi_elem in doi_elements:
            do_name = doi_elem.get('name', '')
            
            # 解析数据属性
            data_attributes = self.parse_data_attributes(doi_elem, ied_name, ld_inst, 
                                                       ln_class, ln_inst, do_name, namespace)
            
            do_info = {
                'name': do_name,
                'desc': doi_elem.get('desc', ''),
                'data_attributes': data_attributes
            }
            
            data_objects.append(do_info)
            
        return data_objects
    
    def parse_data_attributes(self, doi_elem, ied_name: str, ld_inst: str,
                            ln_class: str, ln_inst: str, do_name: str, namespace: Dict) -> List[Dict]:
        """解析数据属性"""
        data_attributes = []
        
        # 使用修复版的元素查找方法
        dai_elements = self._find_elements_fixed(doi_elem, 'DAI', namespace)
        
        for dai_elem in dai_elements:
            da_name = dai_elem.get('name', '')
            
            # 获取值 - 使用修复版查找
            val_elements = self._find_elements_fixed(dai_elem, 'Val', namespace)
            value = val_elements[0].text if val_elements else '0'
            
            # 构造信号点信息
            signal_point = self.create_signal_point(
                ied_name, ld_inst, ln_class, ln_inst, do_name, da_name, value
            )
            
            data_attributes.append(signal_point)
            self.signal_points.append(signal_point)
            
        return data_attributes

    def create_default_ieds(self) -> List[Dict]:
        """创建默认的IED结构（当SCD文件解析失败时）"""
        default_ieds = []

        # 创建10个默认IED
        for i in range(1, 11):
            ied_name = f"IED_{i:03d}"
            logical_devices = self.create_default_logical_devices(ied_name)

            ied_info = {
                'name': ied_name,
                'desc': f'默认IED设备{i}',
                'type': 'DefaultIED',
                'logical_devices': logical_devices,
                'signal_count': sum(len(ld.get('logical_nodes', [])) for ld in logical_devices)
            }

            default_ieds.append(ied_info)

        self.log_message(f"创建了{len(default_ieds)}个默认IED")
        return default_ieds

    def create_default_logical_devices(self, ied_name: str) -> List[Dict]:
        """创建默认的逻辑设备结构"""
        logical_devices = []

        # 创建默认逻辑设备LD0
        ld_inst = "LD0"
        logical_nodes = self.create_default_logical_nodes(ied_name, ld_inst)

        ld_info = {
            'inst': ld_inst,
            'desc': '默认逻辑设备',
            'logical_nodes': logical_nodes
        }

        logical_devices.append(ld_info)
        return logical_devices

    def create_default_logical_nodes(self, ied_name: str, ld_inst: str) -> List[Dict]:
        """创建默认的逻辑节点结构"""
        logical_nodes = []

        # 定义默认的逻辑节点类型
        default_ln_types = [
            ('XCBR', '1', '断路器'),
            ('MMXU', '1', '测量单元'),
            ('PTRC', '1', '保护装置'),
            ('CSWI', '1', '控制开关'),
            ('GGIO', '1', '通用输入输出')
        ]

        for ln_class, ln_inst, desc in default_ln_types:
            data_objects = self.create_default_data_objects(ied_name, ld_inst, ln_class, ln_inst)

            ln_info = {
                'lnClass': ln_class,
                'inst': ln_inst,
                'desc': desc,
                'data_objects': data_objects
            }

            logical_nodes.append(ln_info)

        return logical_nodes

    def create_default_data_objects(self, ied_name: str, ld_inst: str,
                                  ln_class: str, ln_inst: str) -> List[Dict]:
        """创建默认的数据对象结构"""
        data_objects = []

        # 根据逻辑节点类型创建相应的数据对象
        if ln_class == 'XCBR':  # 断路器
            do_types = [
                ('Pos', '位置', [('stVal', '0'), ('ctlVal', '0')]),
                ('BlkOpn', '分闸闭锁', [('stVal', '0')]),
                ('BlkCls', '合闸闭锁', [('stVal', '0')]),
                ('Loc', '就地/远方', [('stVal', '0')])
            ]
        elif ln_class == 'MMXU':  # 测量单元
            do_types = [
                ('TotW', '有功功率', [('mag', '0.0')]),
                ('TotVAr', '无功功率', [('mag', '0.0')]),
                ('Hz', '频率', [('mag', '50.0')]),
                ('PPV', '相电压', [('phsA', '220.0'), ('phsB', '220.0'), ('phsC', '220.0')])
            ]
        elif ln_class == 'PTRC':  # 保护装置
            do_types = [
                ('Str', '启动', [('general', '0')]),
                ('Op', '动作', [('general', '0')]),
                ('Tr', '跳闸', [('general', '0')])
            ]
        elif ln_class == 'CSWI':  # 控制开关
            do_types = [
                ('Pos', '位置', [('stVal', '0'), ('ctlVal', '0')]),
                ('Loc', '就地/远方', [('stVal', '0')])
            ]
        else:  # GGIO 通用输入输出
            do_types = [
                ('SPCSO1', '单点控制1', [('stVal', '0'), ('ctlVal', '0')]),
                ('SPCSO2', '单点控制2', [('stVal', '0'), ('ctlVal', '0')]),
                ('Ind1', '指示1', [('stVal', '0')]),
                ('Ind2', '指示2', [('stVal', '0')])
            ]

        for do_name, do_desc, da_list in do_types:
            data_attributes = []

            for da_name, value in da_list:
                signal_point = self.create_signal_point(
                    ied_name, ld_inst, ln_class, ln_inst, do_name, da_name, value
                )
                data_attributes.append(signal_point)
                self.signal_points.append(signal_point)

            do_info = {
                'name': do_name,
                'desc': do_desc,
                'data_attributes': data_attributes
            }

            data_objects.append(do_info)

        return data_objects

    def create_signal_point(self, ied_name: str, ld_inst: str, ln_class: str,
                          ln_inst: str, do_name: str, da_name: str, value: str) -> Dict:
        """创建信号点信息"""
        # 生成信号名称
        signal_name = self.generate_signal_name(ied_name, ln_class, ln_inst, do_name, da_name)
        
        # 确定信号类型
        signal_type = self.determine_signal_type(da_name)
        
        # 确定数据类型
        data_type = self.determine_data_type(value, da_name)
        
        # 生成SCD路径
        scd_path = f"{ied_name}.{ld_inst}.{ln_class}{ln_inst}.{do_name}.{da_name}"
        
        # 生成描述
        description = self.generate_description(ln_class, ln_inst, do_name, da_name)
        
        return {
            'signal_name': signal_name,
            'signal_type': signal_type,
            'data_type': data_type,
            'expected_value': value,
            'description': description,
            'scd_path': scd_path,
            'ied_name': ied_name,
            'ld_inst': ld_inst,
            'ln_class': ln_class,
            'ln_inst': ln_inst,
            'do_name': do_name,
            'da_name': da_name
        }
    
    def generate_signal_name(self, ied_name: str, ln_class: str, ln_inst: str,
                           do_name: str, da_name: str) -> str:
        """生成标准化信号名称"""
        pattern = self.conversion_rules['naming_pattern']
        
        signal_name = pattern.format(
            ied_name=ied_name,
            ln_class=ln_class,
            ln_inst=ln_inst,
            do_name=do_name,
            da_name=da_name
        )
        
        # 处理特殊字符
        signal_name = signal_name.replace('.', '_').replace('[', '_').replace(']', '')
        
        # 长度限制
        if len(signal_name) > 32:
            signal_name = signal_name[:32]
        
        return signal_name
    
    def determine_signal_type(self, da_name: str) -> str:
        """确定信号类型"""
        type_mapping = self.conversion_rules['signal_type_mapping']
        
        for key, signal_type in type_mapping.items():
            if key in da_name:
                return signal_type
        
        # 默认为遥信
        return 'DI'
    
    def determine_data_type(self, value: str, da_name: str) -> str:
        """确定数据类型"""
        if not value:
            return 'BOOL'
        
        # 尝试判断数据类型
        try:
            if value.lower() in ['true', 'false', '0', '1']:
                return 'BOOL'
            elif '.' in value:
                float(value)
                return 'FLOAT'
            else:
                int(value)
                return 'INT'
        except ValueError:
            return 'STRING'
    
    def generate_description(self, ln_class: str, ln_inst: str, do_name: str, da_name: str) -> str:
        """生成信号描述"""
        ln_descriptions = {
            'XCBR': '断路器',
            'XSWI': '开关',
            'MMXU': '测量单元',
            'PTRC': '保护',
            'CSWI': '控制开关',
            'CILO': '联锁',
            'YPTR': 'PT',
            'TCTR': 'CT'
        }
        
        do_descriptions = {
            'Pos': '位置',
            'Alm': '告警',
            'TotW': '有功功率',
            'TotVAr': '无功功率',
            'Hz': '频率',
            'PPV': '相电压',
            'A': '电流'
        }
        
        da_descriptions = {
            'stVal': '状态值',
            'mag': '幅值',
            'setVal': '设定值',
            'ctlVal': '控制值'
        }
        
        ln_desc = ln_descriptions.get(ln_class, ln_class)
        do_desc = do_descriptions.get(do_name, do_name)
        da_desc = da_descriptions.get(da_name, da_name)
        
        return f"{ln_desc}{ln_inst}_{do_desc}_{da_desc}"
    
    def parse_data_types(self, root, namespace: Dict) -> Dict:
        """解析数据类型定义"""
        data_types = {}
        
        # 这里可以扩展解析DataTypeTemplates
        # 目前使用默认映射
        
        return data_types
    
    def convert_to_point_table(self, output_format: str = 'csv') -> str:
        """转换为点表格式"""
        if not self.signal_points:
            raise Exception("没有可转换的信号点，请先解析SCD文件")
        
        self.log_message(f"开始转换{len(self.signal_points)}个信号点为点表格式")
        
        # 分配地址
        self.allocate_addresses()
        
        # 根据格式输出
        if output_format.lower() == 'csv':
            return self.export_to_csv()
        elif output_format.lower() == 'json':
            return self.export_to_json()
        elif output_format.lower() == 'xml':
            return self.export_to_xml()
        else:
            raise Exception(f"不支持的输出格式: {output_format}")
    
    def allocate_addresses(self):
        """分配点表地址"""
        address_counters = self.conversion_rules['address_allocation'].copy()
        
        for signal in self.signal_points:
            signal_type = signal['signal_type']
            
            if signal_type == 'DI':
                signal['address'] = address_counters['DI_start']
                address_counters['DI_start'] += 1
            elif signal_type == 'AI':
                signal['address'] = address_counters['AI_start']
                address_counters['AI_start'] += 1
            elif signal_type == 'DO':
                signal['address'] = address_counters['DO_start']
                address_counters['DO_start'] += 1
            elif signal_type == 'AO':
                signal['address'] = address_counters['AO_start']
                address_counters['AO_start'] += 1
            else:
                signal['address'] = 9999  # 未知类型
        
        self.log_message("地址分配完成")
    
    def export_to_csv(self) -> str:
        """导出为CSV格式"""
        output_file = f"point_table_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        with open(output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
            fieldnames = [
                '点号', '信号名称', '信号类型', '数据类型', '期望值', 
                '描述', 'SCD路径', 'IED名称'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for signal in self.signal_points:
                writer.writerow({
                    '点号': signal['address'],
                    '信号名称': signal['signal_name'],
                    '信号类型': signal['signal_type'],
                    '数据类型': signal['data_type'],
                    '期望值': signal['expected_value'],
                    '描述': signal['description'],
                    'SCD路径': signal['scd_path'],
                    'IED名称': signal['ied_name']
                })
        
        self.log_message(f"CSV点表已导出: {output_file}")
        return output_file
    
    def export_to_json(self) -> str:
        """导出为JSON格式"""
        output_file = f"point_table_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        point_table_data = {
            'metadata': {
                'export_time': datetime.now().isoformat(),
                'total_points': len(self.signal_points),
                'conversion_rules': self.conversion_rules
            },
            'signal_points': self.signal_points
        }
        
        with open(output_file, 'w', encoding='utf-8') as jsonfile:
            json.dump(point_table_data, jsonfile, ensure_ascii=False, indent=2)
        
        self.log_message(f"JSON点表已导出: {output_file}")
        return output_file
    
    def export_to_xml(self) -> str:
        """导出为XML格式"""
        output_file = f"point_table_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xml"
        
        root = ET.Element("PointTable")
        root.set("exportTime", datetime.now().isoformat())
        root.set("totalPoints", str(len(self.signal_points)))
        
        for signal in self.signal_points:
            point_elem = ET.SubElement(root, "Point")
            point_elem.set("address", str(signal['address']))
            point_elem.set("name", signal['signal_name'])
            point_elem.set("type", signal['signal_type'])
            point_elem.set("dataType", signal['data_type'])
            point_elem.set("expectedValue", signal['expected_value'])
            point_elem.set("description", signal['description'])
            point_elem.set("scdPath", signal['scd_path'])
            point_elem.set("iedName", signal['ied_name'])
        
        tree = ET.ElementTree(root)
        tree.write(output_file, encoding='utf-8', xml_declaration=True)
        
        self.log_message(f"XML点表已导出: {output_file}")
        return output_file
    
    def get_conversion_statistics(self) -> Dict:
        """获取转换统计信息"""
        if not self.signal_points:
            return {}
        
        stats = {
            'total_points': len(self.signal_points),
            'signal_types': {},
            'data_types': {},
            'ied_distribution': {}
        }
        
        for signal in self.signal_points:
            # 统计信号类型
            signal_type = signal['signal_type']
            stats['signal_types'][signal_type] = stats['signal_types'].get(signal_type, 0) + 1
            
            # 统计数据类型
            data_type = signal['data_type']
            stats['data_types'][data_type] = stats['data_types'].get(data_type, 0) + 1
            
            # 统计IED分布
            ied_name = signal['ied_name']
            stats['ied_distribution'][ied_name] = stats['ied_distribution'].get(ied_name, 0) + 1
        
        return stats
    
    def log_message(self, message: str, level: str = 'INFO'):
        """记录日志消息"""
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'level': level,
            'message': message
        }
        self.conversion_log.append(log_entry)
        print(f"[{level}] {message}")
    
    def get_conversion_log(self) -> List[Dict]:
        """获取转换日志"""
        return self.conversion_log
    
    def clear_data(self):
        """清空数据"""
        self.signal_points = []
        self.conversion_log = []

# 使用示例
if __name__ == "__main__":
    converter = SCDToPointConverter()
    
    try:
        # 解析SCD文件
        scd_result = converter.parse_scd_file("large_substation_500points.scd")
        print(f"解析完成，发现{scd_result['total_ieds']}个IED")
        
        # 转换为点表
        csv_file = converter.convert_to_point_table('csv')
        print(f"点表已生成: {csv_file}")
        
        # 获取统计信息
        stats = converter.get_conversion_statistics()
        print(f"转换统计: {stats}")
        
    except Exception as e:
        print(f"转换失败: {e}")
